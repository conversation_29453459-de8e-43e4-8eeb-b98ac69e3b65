# Dockerfile pro aplikaci
FROM php:8.1-apache

RUN apt-get update && apt-get install -y \
    cron \
    supervisor \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    zip \
    unzip \
    git \
    curl \
    libldap2-dev \
    libzip-dev \
    net-tools \
    default-mysql-client \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install gd pdo pdo_mysql ldap opcache zip

# Enable Apache modules
RUN a2enmod rewrite

# Instalace Composeru
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Instalace ionCube Loaderu
# RUN curl -fsSL https://downloads.ioncube.com/loader_downloads/ioncube_loaders_lin_x86-64.tar.gz | tar -xz -C /usr/local/src/ \
#    && cp /usr/local/src/ioncube/ioncube_loader_lin_8.1.so /usr/local/lib/php/extensions/no-debug-non-zts-20210902/ \
#    && echo "zend_extension=/usr/local/lib/php/extensions/no-debug-non-zts-20210902/ioncube_loader_lin_8.1.so" > /usr/local/etc/php/conf.d/00-ioncube.ini

# Copy Apache configuration
COPY apache.conf /etc/apache2/sites-available/000-default.conf
COPY scripts/ /var/www/scripts/

# Copy supervisor configuration
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

EXPOSE 80

# Nakopíruju zálohovací skript (o +x se postara entry point)
COPY db_backup /etc/cron.daily/db_backup

# Systemd služby už nepotřebujeme - používáme supervisor

# Nastavíme práva pro skripty, pokud existují
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]




CMD apache2-foreground
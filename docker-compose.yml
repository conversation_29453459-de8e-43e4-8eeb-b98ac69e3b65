services:
  app:
    build: .
    container_name: adsys_app
    restart: unless-stopped
    working_dir: /var/www/html
    ports:
      - "80:80"
    volumes:
      - ./html:/var/www/html
      # Persistentní volumes pro data, která se nemají přepsat při aktualizaci
      - adsys_storage:/var/www/html/backend/storage
      - adsys_env:/var/www/html/backend/.env_persistent
    networks:
      - adsys
    depends_on:
      - db
    env_file:
      - .env

  db:
    image: mariadb:10.5
    container_name: adsys_db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: adsys_db
      MYSQL_USER: adsys_admin
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - dbdata:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - adsys
    env_file:
      - .env

volumes:
  dbdata:
  adsys_storage:
  adsys_env:

networks:
  adsys:
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('move_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('move_id')->nullable();
            $table->foreignId('item_id')->nullable();
            $table->timestamps();

            $table->foreign('move_id')->references('id')->on('moves');
            $table->foreign('item_id')->references('id')->on('items');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('move_items');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organization_units', function (Blueprint $table) {
            $table->id();
            $table->string('guid')->nullable();
            $table->string('parent')->nullable();
            $table->string('name');
            $table->string('distinguished_name')->nullable();
            $table->boolean('promotion')->default(0);
            $table->string('map_bakalari')->nullable();
            $table->string('map_skola_online')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organization_units');
    }
};

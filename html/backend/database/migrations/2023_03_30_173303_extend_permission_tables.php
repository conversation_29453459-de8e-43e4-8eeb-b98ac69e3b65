<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

   public function up() : void
   {
       Schema::table('permissions', function (Blueprint $table) {
            $table->string('human_name', 100)->nullable();
            $table->foreignId('category_id')->default(1);
            $table->integer('position')->nullable();
            $table->foreign('category_id')->references('id')->on('permission_categories')->restrictOnDelete();;
       });
   }

   public function down() : void
   {
       Schema::table('permissions', function (Blueprint $table) {
           $table->dropColumn('human_name');
           $table->dropColumn('category_id');
           $table->dropColumn('position');
       });
   }
};

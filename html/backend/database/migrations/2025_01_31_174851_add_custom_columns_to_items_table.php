<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('items', function (Blueprint $table) {         
            $table->string('purchased_from')->after('state')->nullable();
            $table->text('description')->after('purchased_from')->nullable();
            $table->string('custom_1')->after('description')->nullable();
            $table->string('custom_2')->after('custom_1')->nullable();
            $table->string('custom_3')->after('custom_2')->nullable();
            $table->string('custom_4')->after('custom_3')->nullable();
            $table->string('custom_5')->after('custom_4')->nullable();
            $table->string('custom_6')->after('custom_5')->nullable();
            $table->string('custom_7')->after('custom_6')->nullable();
            $table->string('custom_8')->after('custom_7')->nullable();
            $table->string('custom_9')->after('custom_8')->nullable();
            $table->string('custom_10')->after('custom_9')->nullable();
            $table->text('import_string')->after('custom_10')->nullable();
            $table->dateTime('discarded_at')->after('import_string')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('items', function (Blueprint $table) {
            $table->dropColumn('purchased_from');
            $table->dropColumn('description');
            $table->dropColumn('custom_1');
            $table->dropColumn('custom_2');
            $table->dropColumn('custom_3');
            $table->dropColumn('custom_4');
            $table->dropColumn('custom_5');
            $table->dropColumn('custom_6');
            $table->dropColumn('custom_7');
            $table->dropColumn('custom_8');
            $table->dropColumn('custom_9');
            $table->dropColumn('custom_10');
            $table->dropColumn('import_string');
            $table->dropColumn('discarded_at');
        });
    }
};

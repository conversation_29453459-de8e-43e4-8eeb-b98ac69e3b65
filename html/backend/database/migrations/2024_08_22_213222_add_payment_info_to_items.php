<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('items', function (Blueprint $table) {
            $table->string('invoice_number')->nullable()->after('evidence_number');
            $table->date('buyed_at')->nullable()->after('invoice_number');
            $table->double('price',10,2)->nullable()->after('buyed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('items', function (Blueprint $table) {
            $table->dropColumn('invoice_number');
            $table->dropColumn('buyed_at');
            $table->dropColumn('price');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Crypt;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        // Check if the database file does not exist, and create it
        if (!Storage::disk('database')->exists('certificate.sqlite')) {
            Storage::disk('database')->put('certificate.sqlite', '');
        } else {
            Storage::disk('database')->delete('certificate.sqlite');
            Storage::disk('database')->put('certificate.sqlite', '');
        }


        Schema::connection('sqlite')->create('license', function (Blueprint $table) {
            $table->id();
            $table->string('token')->nullable();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection('sqlite')->dropIfExists('license');
    }
};

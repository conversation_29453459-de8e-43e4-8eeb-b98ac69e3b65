<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Models\TicketState;


class TicketStatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        TicketState::create(['key' => 'NEW', 'name' => 'Nový']);
        TicketState::create(['key' => 'WAITING_FOR_ADMIN', 'name' => 'Čeká se na odpověď administrátora']);
        TicketState::create(['key' => 'WAITING_FOR_USER', 'name' => '<PERSON>ek<PERSON> se na odpověď uživatele']);
        TicketState::create(['key' => 'CLOSED', 'name' => 'Uzavřený']);
    }
}

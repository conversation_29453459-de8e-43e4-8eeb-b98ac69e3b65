<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Models\PermissionCategory;

class PermissionCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            ['id' => 1, 'name' => 'Nezařazeno', 'position' => 100],
            ['id' => 2, 'name' => 'Správa uživatelů', 'position' => 1],
            ['id' => 3, 'name' => 'Práva k účtu', 'position' => 2],
            ['id' => 4, 'name' => 'Správa rolí', 'position' => 3],
            ['id' => 5, 'name' => 'Správa oprávnění', 'position' => 4],
            ['id' => 7, 'name' => 'Tickety', 'position' => 5],
            ['id' => 8, 'name' => 'Active Directory', 'position' => 6],
            ['id' => 11, 'name' => 'Skupiny AD', 'position' => 7],
            ['id' => 12, 'name' => 'Organizační jednotky AD', 'position' => 8],
            ['id' => 13, 'name' => 'Zálohy DB', 'position' => 79],
            ['id' => 9, 'name' => 'Časový rozvrh', 'position' => 9],
            ['id' => 10, 'name' => 'Blokace internetu', 'position' => 10],

            ['id' => 29, 'name' => 'Správa majetku', 'position' => 29],
            ['id' => 30, 'name' => 'Majetek', 'position' => 30],
            ['id' => 31, 'name' => 'Místnosti', 'position' => 31],
            ['id' => 32, 'name' => 'Převodky majetku', 'position' => 32],
            ['id' => 33, 'name' => 'Inventury majetku', 'position' => 33],
            ['id' => 34, 'name' => 'Budovy', 'position' => 34],

            ['id' => 6, 'name' => 'Systémová oprávnění', 'position' => 99],
        ];

        foreach($categories as $category){
            PermissionCategory::create($category);
        }
    }
}
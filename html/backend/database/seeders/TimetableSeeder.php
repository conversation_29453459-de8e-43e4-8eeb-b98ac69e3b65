<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Models\Timetable;

class TimetableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            ['teaching_hour_number'=> 0, 'start'=> '07:05', 'end'=> '07:50'],
            ['teaching_hour_number'=> 1, 'start'=> '08:00', 'end'=> '08:45'],
            ['teaching_hour_number'=> 2, 'start'=> '08:55', 'end'=> '09:40'],
            ['teaching_hour_number'=> 3, 'start'=> '10:00', 'end'=> '10:45'],
            ['teaching_hour_number'=> 4, 'start'=> '10:55', 'end'=> '11:40'],
            ['teaching_hour_number'=> 5, 'start'=> '11:50', 'end'=> '12:35'],
            ['teaching_hour_number'=> 6, 'start'=> '12:45', 'end'=> '13:30'],
            ['teaching_hour_number'=> 7, 'start'=> '13:40', 'end'=> '14:25'],
            ['teaching_hour_number'=> 8, 'start'=> '14:35', 'end'=> '15:20']
        ];
        
        Timetable::insert($data); // Eloquent approach
    }
}

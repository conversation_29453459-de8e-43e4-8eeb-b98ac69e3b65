<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Models\Role;;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Role::create(['name' => 'Super Admin', 'role_priority' => 1]);
        $adminRole = Role::create(['name' => 'Admin', 'role_priority' => 2]);
        $teacherRole = Role::create(['name' => 'Učitel', 'role_priority' => 3]);

        $adminRole->givePermissionTo([
            'roles.read',
            'users.read', 'users.edit', 'users.set_password',
            'profil.read', 'profil.edit', 'profil.change_password', 'profil.change_email',
            'permissions.create', 'permissions.read', 'permissions.edit', 'permissions.delete',
            'internet_blocks.create', 'internet_blocks.read', 'internet_blocks.delete',
            'tickets.create','tickets.read', 'tickets.edit', 'tickets.delete', 'tickets.close',
            'active_directory_group.read',
            'active_directory_ou.read',
            'active_directory.login',
            'timetables.read'
        ]);

        $teacherRole->givePermissionTo([
            'roles.read',
            'users.read', 'users.edit', 'users.set_password',
            'profil.read', 'profil.edit', 'profil.change_password', 'profil.change_email',
            'internet_blocks.create', 'internet_blocks.read', 'internet_blocks.delete',
            'tickets.create','tickets.read', 'tickets.edit', 'tickets.delete', 'tickets.close',
            'active_directory_group.read',
            'active_directory_ou.read',
            'active_directory.login',
            'timetables.read'    
        ]);
    }
}

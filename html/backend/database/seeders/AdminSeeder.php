<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Models\User;
use Carbon\Carbon;

use Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::create([
            'first_name' => 'Admin',
            'last_name' => 'Všemocný',
            'email' => '<EMAIL>',
            'email_verified_at' => Carbon::now(),
            'password' => Hash::make('In1tHesl0')
        ]);

        $user->assignRole('Super Admin');
    }
}

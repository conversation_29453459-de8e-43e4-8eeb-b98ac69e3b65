.env vzít z .env.example

# v jedne konzoli (pokud ti front jede jako localhost)
php artisan serve --host=localhost --port=8000  
herd php artisan serve --host=localhost --port=8000   

# v druhe konzoli zapnout worker
php artisan queue:listen --queue=high,medium,low,default,sms
herd php artisan queue:listen --queue=high,medium,low,default,sms

# start scheduleru
php artisan schedule:work
herd php artisan schedule:work

# doc k balikum
https://laravel.com/docs/10.x/telescope

https://github.com/Adldap2/Adldap2-<PERSON>vel
https://adldap2.github.io/Adldap2/#/ - docu
https://github.com/Adldap2/Adldap2-Laravel/issues/318?fbclid=IwAR3IjXe8ZOShKGdlhYFQjdfBR639Wae9-myl2sSrlZWnlD-f1G3btd56hWk

https://support.yeastar.com/hc/en-us/articles/217393078-HTTP-Interface-for-SMS?fbclid=IwAR24vJGXAo4y2V_I5hvyqxcPVUi2juvLZfdUOIaAQX09XyA7ET18PiQDtlM

# na produkci zmenit 
APP_ENV=local na APP_ENV=production






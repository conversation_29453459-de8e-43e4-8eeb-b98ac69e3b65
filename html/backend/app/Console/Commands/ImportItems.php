<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Item\ItemImportService;

class ImportItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-items';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        echo "Start importing items." . PHP_EOL;
        $service = New ItemImportService;
        $result = $service->process();

        if($result){
            echo "Stop importing - success." . PHP_EOL;
        } else {
            echo "Stop importing - failed." . PHP_EOL;
        }
    }
}

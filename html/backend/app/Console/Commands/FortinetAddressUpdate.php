<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Services\Fortinet\FortinetAddressUpdateService;


class FortinetAddressUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fortinet-address-update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Spustí servicu na updatování blokovaných účtů.';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $service = New FortinetAddressUpdateService;
        $service->process();
    }
}

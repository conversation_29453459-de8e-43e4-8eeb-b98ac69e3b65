<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use DB;

use Carbon\Carbon;

class ClearJobTables extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:clear-job-tables';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $dateTime = Carbon::now()->subDays(7)->toDateTimeString();
        DB::table('failed_jobs')->whereDate('failed_at', '<', $dateTime)->delete();
        DB::table('completed_jobs')->whereDate('created_at', '<', $dateTime)->delete();
    }
}

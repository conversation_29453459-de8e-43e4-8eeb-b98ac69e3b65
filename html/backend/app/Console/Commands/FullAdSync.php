<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Jobs\AdUsersSync;
use App\Jobs\AdGroupsSync;
use App\Jobs\AdOrganizationUnitsSync;

class FullAdSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:full-ad-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronizace Active Directory';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        AdOrganizationUnitsSync::dispatch();
        AdGroupsSync::dispatch();
        AdUsersSync::dispatch();
    }
}

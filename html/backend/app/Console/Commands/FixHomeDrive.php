<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Models\User;

use Adldap;
use Adldap\Utilities;
use App\Models\OrganizationUnit;
use App\Models\Group;
use App\Models\AccountControlCode;

class FixHomeDrive extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-home-drive';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $users = User::where('home_drive', '1')->get();
        foreach($users as $user){
            $adUser = Adldap::search()->where('samaccountname',$user->account_name)->first();
            $adUser->setHomeDrive(null);
            $result = $adUser->save();
        }
    }
}

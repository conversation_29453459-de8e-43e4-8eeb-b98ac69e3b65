<?php

namespace App\Http\Resources\Role;

use App\Http\Resources\Permission\PermissionCollection;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RoleDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'role_priority' => $this->role_priority,
            'permissions' => New PermissionCollection($this->permissions),
            'permission_names' => $this->permissions->pluck('name'),
        ];

        return $data;
    }
}

<?php

namespace App\Http\Resources\Group;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use App\Http\Resources\Group\GroupShortCollection;

class GroupListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'default' => $this->default,
            'visitors' => $this->visitors,
            'distinguished_name' => $this->distinguished_name,
            'groups' => New GroupShortCollection($this->parentGroups),
        ];

        return $data;
    }
}

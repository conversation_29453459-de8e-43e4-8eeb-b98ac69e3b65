<?php

namespace App\Http\Resources\Ticket;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use App\Http\Resources\User\UserShortResource;
use App\Http\Resources\TicketState\TicketStateResource;
use App\Http\Resources\TicketCategory\TicketCategoryResource;
use App\Http\Resources\TicketAnswer\TicketAnswerCollection;

class TicketDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'user' => New UserShortResource($this->user),
            'category' => New TicketCategoryResource($this->category),
            'state' => New TicketStateResource($this->state),
            'subject' => $this->subject,
            'text' => $this->text,
            'answers'  => New TicketAnswerCollection($this->answers),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];

        return $data;
    }
}

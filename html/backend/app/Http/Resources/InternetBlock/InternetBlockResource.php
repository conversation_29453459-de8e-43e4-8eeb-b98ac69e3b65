<?php

namespace App\Http\Resources\InternetBlock;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use App\Http\Resources\User\UserShortResource;


class InternetBlockResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'user' => New UserShortResource($this->user),
            'from' => $this->from,
            'to' => $this->to,
            'permanent' => $this->permanent,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];

        return $data;
    }
}

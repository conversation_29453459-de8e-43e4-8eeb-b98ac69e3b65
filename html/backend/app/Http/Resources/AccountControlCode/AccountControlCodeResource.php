<?php

namespace App\Http\Resources\AccountControlCode;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AccountControlCodeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'listing' => $this->listing
        ];

        return $data;
    }
}

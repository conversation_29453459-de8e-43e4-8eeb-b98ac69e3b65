<?php

namespace App\Http\Resources\CustomTableSetting;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use App\Http\Resources\User\UserShortResource;

class CustomTableSettingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'table_name' => $this->table_name,
            'settings' => $this->settings
        ];

        return $data;
    }
}

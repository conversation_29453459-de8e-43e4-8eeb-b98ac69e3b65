<?php

namespace App\Http\Resources\Room;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use App\Http\Resources\User\UserShortResource;
use App\Http\Resources\Item\ItemShortCollection;
use App\Http\Resources\Building\BuildingResource;

class RoomDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'user' => New UserShortResource($this->user),
            'items' => New ItemShortCollection($this->items),
            'building' => New BuildingResource($this->building),
        ];

        return $data;
    }
}

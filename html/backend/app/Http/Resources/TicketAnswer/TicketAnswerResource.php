<?php

namespace App\Http\Resources\TicketAnswer;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use App\Http\Resources\User\UserShortResource;
use App\Http\Resources\TicketState\TicketStateResource;
use App\Http\Resources\TicketCategory\TicketCategoryResource;

class TicketAnswerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'user' => New UserShortResource($this->user),
            'text' => $this->text,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];

        return $data;
    }
}

<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserRolePermissionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'middle_name' => $this->middle_name,
            'last_name' => $this->last_name,

            'roles' => $this->getRoleNames(),
            'direct_permissions' => $this->getDirectPermissions()->pluck('name'),
            'role_permissions' => $this->getPermissionsViaRoles()->pluck('name')

        ];

        return $data;
    }
}

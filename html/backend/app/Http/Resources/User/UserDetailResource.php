<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'midle_name' => $this->midle_name,
            'last_name' => $this->last_name,
            'account_name' => $this->account_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'block_internet' => $this->getBlockInternetStatus(),
            'active_directory' => $this->active_directory,
            'active_directory_data' => null,
            'email_verified_at' => $this->email_verified_at
        ];

        if($this->active_directory){
            $ad_data = [
                'guid' => $this->guid,
                'groups' => $this->groups,
                'organization_unit' => $this->organizationUnit,
                'account_control_code' => $this->accountControlCode,
                'visitor' => $this->visitor,
                'editable' => $this->getEditable(),
                'expire' => $this->expire,
                'profile_path' => $this->profile_path,
                'script_path' => $this->script_path,
                'home_directory' => $this->home_directory,
                'home_drive' => $this->home_drive
            ];
            $data['active_directory_data'] = $ad_data;
        }

        return $data;
    }
}

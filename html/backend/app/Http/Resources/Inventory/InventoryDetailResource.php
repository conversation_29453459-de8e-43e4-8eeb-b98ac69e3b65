<?php

namespace App\Http\Resources\Inventory;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use App\Http\Resources\User\UserShortResource;
use App\Http\Resources\Item\ItemShortCollection;
use App\Http\Resources\Room\RoomShortResource;

class InventoryDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'type' => $this->type,
            'state' => $this->state,
            'user'  => New UserShortResource($this->user),
            'room' => New RoomShortResource($this->room),
            'items' => New ItemShortCollection($this->items)
        ];

        return $data;
    }
}

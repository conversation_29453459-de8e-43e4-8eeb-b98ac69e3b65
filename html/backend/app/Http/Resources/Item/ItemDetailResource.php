<?php

namespace App\Http\Resources\Item;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

use App\Http\Resources\User\UserShortResource;
use App\Http\Resources\Room\RoomShortResource;
use App\Http\Resources\AccountingCategory\AccountingCategoryResource;

class ItemDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'evidence_number' => $this->evidence_number,
            'state' => $this->state,
            'invoice_number' =>  $this->invoice_number,
            'buyed_at' =>  $this->buyed_at,
            'purchased_from' => $this->purchased_from,
            'description' =>  $this->description,
            'price' =>  $this->price,
            'user'  => New UserShortResource($this->user),
            'room' => New RoomShortResource($this->room),
            'accounting_category' => New AccountingCategoryResource($this->accountingCategory),
            'custom_1' =>  $this->custom_1,
            'custom_2' =>  $this->custom_2,
            'custom_3' =>  $this->custom_3,
            'custom_4' =>  $this->custom_4,
            'custom_5' =>  $this->custom_5,
            'custom_6' =>  $this->custom_6,
            'custom_7' =>  $this->custom_7,
            'custom_8' =>  $this->custom_8,
            'custom_9' =>  $this->custom_9,
            'custom_10' =>  $this->custom_10,
            'discarded_at' => $this->discarded_at,
        ];

        return $data;
    }
}

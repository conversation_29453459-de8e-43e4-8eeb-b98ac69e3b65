<?php

namespace App\Http\Resources\Item;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ItemShortResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'evidence_number' => $this->evidence_number,
            'invoice_number' =>  $this->invoice_number,
            'buyed_at' =>  $this->buyed_at,
            'price' =>  $this->price
        ];

        if(isset($this->pivot)){
            $data['pivot'] = $this->pivot;
        }

        return $data;
    }
}

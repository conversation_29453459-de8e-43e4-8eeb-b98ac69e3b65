<?php

namespace App\Http\Resources\OrganizationUnit;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrganizationUnitResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'parent' => $this->parent,
            'distinguished_name' => $this->distinguished_name,
            'users_count' => $this->users_count,
            'promotion' => $this->promotion,
            'is_class' => $this->is_class,
            'map_bakalari' => $this->map_bakalari,
            'map_skola_online' => $this->map_skola_online
        ];

        return $data;
    }
}

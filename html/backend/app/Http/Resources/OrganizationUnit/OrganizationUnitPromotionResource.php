<?php

namespace App\Http\Resources\OrganizationUnit;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrganizationUnitPromotionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'parent' => $this->parent,
            'new_name' => $this->increaseNumber($this->name)
        ];

        return $data;
    }

    public function increaseNumber($text) {
        $editedText = preg_replace_callback('/\d+/', function($match) {
            return $match[0] + 1;
        }, $text);

        return $editedText;
    }
}

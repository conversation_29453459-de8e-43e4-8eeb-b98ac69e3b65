<?php

namespace App\Http\Requests\MoveItem;

use Illuminate\Foundation\Http\FormRequest;

class MoveItemMultipleStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'move_id' => 'required|exists:App\Models\Move,id',
            'items' => 'required|array'
        ];
    }
}

<?php

namespace App\Http\Requests\Item;

use Illuminate\Foundation\Http\FormRequest;

class ItemUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required',
            'invoice_number' => 'nullable',
            'buyed_at' => 'nullable|date',
            'price' => 'nullable|numeric',
            'accounting_category_id' => 'nullable|exists:App\Models\AccountingCategory,id' 
        ];
    }
}

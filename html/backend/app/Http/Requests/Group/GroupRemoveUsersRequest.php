<?php

namespace App\Http\Requests\Group;

use Illuminate\Foundation\Http\FormRequest;

use  Illuminate\Validation\Rule;

use App\Models\OrganizationUnit;


class GroupRemoveUsersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'users' => 'required|array',
        ];
    }   
    
}

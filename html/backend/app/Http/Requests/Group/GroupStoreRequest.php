<?php

namespace App\Http\Requests\Group;

use Illuminate\Foundation\Http\FormRequest;

use  Illuminate\Validation\Rule;

use App\Models\OrganizationUnit;


class GroupStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        $parent = OrganizationUnit::where('id', $this->input('organization_unit_id'))->first();
        $fullDn = 'CN='.$this->input('name').','.$parent?->distinguished_name;
        return [
            'name' =>  [
                'required',
                Rule::unique('groups', 'name')->where('distinguished_name', $fullDn),
            ],
            'organization_unit_id' => 'nullable|exists:App\Models\OrganizationUnit,id',
            'description' => 'nullable',
            'default'   => 'required|boolean',
            'visitors' => 'required|boolean',
            'groups' => 'nullable'
        ];
    }   
    
    public function messages()
    {
        return [
            'name.unique' => 'Jméno skupiny není unikátní pro danou organizační jednotku.',
        ];
    }

}

<?php

namespace App\Http\Requests\Group;

use Illuminate\Foundation\Http\FormRequest;

use  Illuminate\Validation\Rule;



class GroupUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        $group = $this->route('group');
        $parentDN = $this->getParent($group->distinguished_name);

        $fullDn = 'CN='.$this->input('name').','.$parentDN;

        return [
            'name' =>  [
                'required',
                Rule::unique('groups', 'name')->where('distinguished_name', $fullDn)->ignore($group->id),
            ],
            'description' => 'nullable',
            'default' => 'required|boolean',
            'visitors' => 'required|boolean',
            'groups' => 'nullable'
        ];
    }   
    
    public function messages()
    {
        return [
            'name.unique' => 'Jméno skupiny není unikátní pro danou organizační jednotku.',
        ];
    }

    public function getParent($distinguishedName){
        $generatedParent = null;

        $explodedNames = explode(",", $distinguishedName, 2);
        $explodedCount = count($explodedNames);

        if($explodedCount != 1){
            $generatedParent = $explodedNames[1];
        } else {
            $generatedParent = null;
        }

        return $generatedParent;
    }

}

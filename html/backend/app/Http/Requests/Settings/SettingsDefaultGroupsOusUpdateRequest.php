<?php

namespace App\Http\Requests\Settings;

use Illuminate\Foundation\Http\FormRequest;

class SettingsDefaultGroupsOusUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {

        return [
            'student_default_groups' => 'nullable',
            'employee_default_groups' => 'nullable',
            'guest_default_groups' => 'nullable',
            'guest_default_organization_unit' => 'nullable'
        ];
    }    
    

}

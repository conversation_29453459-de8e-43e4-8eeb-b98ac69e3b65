<?php

namespace App\Http\Requests\Settings;

use Illuminate\Foundation\Http\FormRequest;

class SettingsGsmGateUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {

        return [
            'gsm_gate_ip' => 'required',
            'gsm_gate_account' => 'required',
            'gsm_gate_sim_port' => 'required',
            'gsm_gate_password' => 'nullable',
            'gsm_send_sms' => 'required|boolean'
        ];
    }    
    

}

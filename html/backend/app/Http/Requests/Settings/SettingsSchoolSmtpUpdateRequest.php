<?php

namespace App\Http\Requests\Settings;

use Illuminate\Foundation\Http\FormRequest;

class SettingsSchoolSmtpUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {

        return [
            'school_mail_host' => 'required',
            'school_mail_port' => 'required',
            'school_mail_username' => 'required',
            'school_mail_password' => 'nullable',
            'school_mail_encryption' => 'required',
        ];
    }    
    

}

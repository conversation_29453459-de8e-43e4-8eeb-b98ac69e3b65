<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class UserUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        $user = $this->route('user');
        
        return [
            'first_name' => 'required|min:2',
            'middle_name' => 'nullable|min:2',
            'last_name' => 'required|min:2',
            'degree_before' => 'nullable|min:2',
            'degree_after' => 'nullable|min:2',
            'email' => 'nullable|unique:users,id,'.$user->id.'|email',
            'email_verified' => 'nullable|boolean',
            'phone' => 'nullable|digits:9',
        ];
    }
}

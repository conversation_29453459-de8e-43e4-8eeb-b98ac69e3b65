<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class UserStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'first_name' => 'required|min:2',
            'middle_name' => 'nullable|min:2',
            'last_name' => 'required|min:2',
            'degree_before' => 'nullable|min:2',
            'degree_after' => 'nullable|min:2',
            'password' => 'nullable|min:8|confirmed',
            'email' => 'required|unique:users|confirmed|email',
            'email_verified' => 'nullable|boolean',
            'show_password' => 'required|boolean',
            'roles' => 'array'   
        ];
    }
}

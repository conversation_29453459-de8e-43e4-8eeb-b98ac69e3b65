<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

use App\Rules\VariablePasswordPolicy;

class AdUserStudentStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(Request $request): array
    {
        $lastName = $this->input('last_name');
        $organizationUnit = $this->input('organization_unit');

        return [
            'first_name' => [
                'required',
                Rule::unique('users', 'first_name')->where('last_name', $lastName)->where('organization_unit', $organizationUnit),
            ],
            'last_name' => 'required|min:2',
            'email' => 'nullable|email',
            'organization_unit' => 'required|exists:App\Models\OrganizationUnit,id',
            'password' => ['confirmed', 'nullable', new VariablePasswordPolicy()],
                
        ];
    }    
    
    public function messages()
    {
        return [
            'first_name.unique' => 'Duplikátní uživatel pro danou organizační jednotku.',
        ];
    }

}

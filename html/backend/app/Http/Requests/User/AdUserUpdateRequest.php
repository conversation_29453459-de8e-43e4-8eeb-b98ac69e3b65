<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

use  Illuminate\Validation\Rule;

class AdUserUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        $user = $this->route('user');
        $lastName = $this->input('last_name');
        $organizationUnit = $this->input('organization_unit');

        return [
            'first_name' => [
                'required',
                Rule::unique('users', 'first_name')->where('last_name', $lastName)->where('organization_unit', $organizationUnit)->ignore($user->id),
            ],
            'last_name' => 'required|min:2',
            'email' => 'nullable|unique:users,id,'.$user->id.'|email',
            'phone' => 'nullable|digits:9',
            'organization_unit' => 'required|exists:App\Models\OrganizationUnit,id',
            'visitor' => 'required|boolean',
            'expire' => 'nullable|date_format:Y-m-d H:i',
            // 'account_control_code_id' => 'required|exists:App\Models\AccountControlCode,id',
            'groups' => 'array',
            'profile_path' => 'nullable', 
            'script_path' => 'nullable', 
            'home_directory' => 'nullable', 
            'home_drive' => 'nullable'
        ];
    }

    public function messages()
    {
        return [
            'first_name.unique' => 'Duplikátní uživatel pro danou organizační jednotku.',
        ];
    }
}

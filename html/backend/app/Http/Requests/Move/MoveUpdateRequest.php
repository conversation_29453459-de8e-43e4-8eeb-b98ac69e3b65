<?php

namespace App\Http\Requests\Move;

use Illuminate\Foundation\Http\FormRequest;

class MoveUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'type' => 'required',
            'user_id' => 'nullable|exists:App\Models\User,id',
            'move_id' => 'nullable|exists:App\Models\Move,id'
        ];
    }
}

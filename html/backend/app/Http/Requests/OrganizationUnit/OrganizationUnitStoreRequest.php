<?php

namespace App\Http\Requests\OrganizationUnit;

use Illuminate\Foundation\Http\FormRequest;

use  Illuminate\Validation\Rule;

use App\Models\OrganizationUnit;


class OrganizationUnitStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        $parent = OrganizationUnit::where('id', $this->input('parent_id'))->first();

        return [
            'name' =>  [
                'required',
                Rule::unique('organization_units', 'name')->where('parent', $parent?->distinguished_name),
            ],
            'promotion' => 'required|boolean',
            'is_class' => 'required|boolean',
            'map_bakalari' => 'nullable',
            'map_skola_online' => 'nullable',
            'parent_id' => 'required|exists:App\Models\OrganizationUnit,id'
        ];
    }   
    
    public function messages()
    {
        return [
            'name.unique' => 'Jméno není unikátní pro danou organizační jednotku.',
        ];
    }

}

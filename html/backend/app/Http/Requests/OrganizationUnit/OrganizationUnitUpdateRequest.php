<?php

namespace App\Http\Requests\OrganizationUnit;

use Illuminate\Foundation\Http\FormRequest;

use  Illuminate\Validation\Rule;

use App\Models\OrganizationUnit;


class OrganizationUnitUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        $organizationUnit = $this->route('organizationUnit');

        return [
            'name' =>  [
                'required',
                Rule::unique('organization_units', 'name')->where('parent', $organizationUnit->parent)->ignore($organizationUnit->id),
            ],
            'promotion' => 'required|boolean',
            'is_class' => 'required|boolean',
            'map_bakalari' => 'nullable',
            'map_skola_online' => 'nullable'
        ];
    }   
    
    public function messages()
    {
        return [
            'name.unique' => 'Jméno není unikátní pro danou organizační jednotku.',
        ];
    }

}

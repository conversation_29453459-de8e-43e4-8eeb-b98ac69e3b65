<?php

namespace App\Http\Requests\OrganizationUnit;

use Illuminate\Foundation\Http\FormRequest;

use  Illuminate\Validation\Rule;

use App\Models\OrganizationUnit;


class OrganizationUnitPromotionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        $parent = OrganizationUnit::where('id', $this->input('parent_id'))->first();

        return [
            'organization_units' => 'required|array'
        ];
    }   
    
    public function messages()
    {
        return [
            'name.unique' => 'Jméno není unikátní pro danou organizační jednotku.',
        ];
    }

}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

use App\Models\License;

use Carbon\Carbon;

class LicenseIsValid
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $license = License::first();

        if(is_null($license)){
            $response = [
                'success' => false,
                'message' => "Chyba licence. Prosím kontaktujte podporu.",
            ];

            return response()->json($response, 400);
        }

        if(is_null($license->token)){
            $response = [
                'success' => false,
                'message' => "Chyba licence. Prosím kontaktujte podporu.",
            ];

            return response()->json($response, 400);
        }

            //Desifruju token
            $key = "4rICJ7SlrpGVNBsgWAcThxUz4yEiirt8";
            $data = base64_decode($license->token);
            $ivLength = openssl_cipher_iv_length('aes-256-cbc');
            $iv = substr($data, 0, $ivLength);
            $encryptedData = substr($data, $ivLength);
            $decData = openssl_decrypt($encryptedData, 'aes-256-cbc', $key, 0, $iv);
            $parsedData = explode(';;',$decData);


            //Vytahnu si aktualni MAC adresu
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            $mac = exec('getmac');
            $mac = strtok($mac, ' ');  
        } elseif (strtoupper(PHP_OS) === 'DARWIN') { // macOS
            $mac = exec("ifconfig | grep -Eo '([0-9A-Fa-f]{2}[:-]){5}[0-9A-Fa-f]{2}'");
        } else { // Linux
            $mac = exec("ifconfig | grep -Eo '([0-9A-Fa-f]{2}[:-]){5}[0-9A-Fa-f]{2}'");
        }

        $mac = str_replace([' ', ':', '-'], '', $mac);
        $mac = strtoupper($mac);

        $check = Carbon::now()->subHours(12)->timestamp;


        //Porovnam jestli je dobry timestamp a zaroven sedi MAC adresa
        if($parsedData[0] > $check and $parsedData[1] = $mac){
            return $next($request);
        } else {
            $response = [
                'success' => false,
                'message' => "Chyba licence. Prosím kontaktujte podporu.",
            ];
     
            return response()->json($response, 400);
        }
        
    }
}

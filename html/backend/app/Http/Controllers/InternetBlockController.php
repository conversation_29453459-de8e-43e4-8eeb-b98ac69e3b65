<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\InternetBlock;

use App\Http\Resources\InternetBlock\InternetBlockCollection;

use App\Services\InternetBlock\InternetBlockDatetimeService;
use App\Services\InternetBlock\InternetBlockOuDatetimeService;
use App\Services\InternetBlock\InternetBlockPermanentService;
use App\Services\InternetBlock\InternetBlockOuPermanentService;
use App\Services\InternetBlock\InternetBlockTimetableService;
use App\Services\InternetBlock\InternetBlockOuTimetableService;

class InternetBlockController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {

        $this->authorize('read', InternetBlock::class);

        $perPage = (int)$request->perpage;
        $page = (int)$request->page;
        $search = $request->search;
        $organizationUnit = $request->organization_unit;

        $blocks = InternetBlock::search($search)
                        ->byOrganizationUnit($organizationUnit)
                        ->orderBy('created_at','desc')
                        ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);

        $data = new InternetBlockCollection($blocks);
        return $data;
    }

        /**
     * Display a listing of the resource.
     */
    public function datetime(Request $request)
    {
        $this->authorize('store', InternetBlock::class);

        $service = New InternetBlockDatetimeService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function organizationUnitDatetime(Request $request)
    {
        $this->authorize('store', InternetBlock::class);

        $service = New InternetBlockOuDatetimeService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

            /**
     * Display a listing of the resource.
     */
    public function permanent(Request $request)
    {
        $this->authorize('store', InternetBlock::class);

        $service = New InternetBlockPermanentService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function organizationUnitPermanent(Request $request)
    {
        $this->authorize('store', InternetBlock::class);

        $service = New InternetBlockOuPermanentService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function timetable(Request $request)
    {
        $this->authorize('store', InternetBlock::class);

        $service = New InternetBlockTimetableService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function organizationUnitTimetable(Request $request)
    {
        $this->authorize('store', InternetBlock::class);

        $service = New InternetBlockOuTimetableService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function destroy(Request $request)
    {
        $this->authorize('destroy', InternetBlock::class);

        $internetBlocks = $request->input('internet_blocks');
        if(is_null($internetBlocks) or count($internetBlocks) == 0){
            return $this->sendError(error: "Nebyla vybrána žádná blokace.");
        }

        if(count($internetBlocks) == 1){
            $message = "Blokace smazána.";
        } else {
            $message = "Blokace smazány.";
        }

        foreach($internetBlocks as $internetBlock){
            $dbInternetBlock = InternetBlock::where('id',$internetBlock)->first();
            if(!is_null($dbInternetBlock)){
                $dbInternetBlock->delete();
            }
        }  
        return $this->sendResponse(message: $message);
    }
}

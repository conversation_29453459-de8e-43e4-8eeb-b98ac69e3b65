<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\PermissionCategory;

use App\Http\Requests\PermissionCategory\PermissionCategoryStoreRequest;
use App\Http\Requests\PermissionCategory\PermissionCategoryUpdateRequest;

use App\Http\Resources\PermissionCategory\PermissionCategoryResource;
use App\Http\Resources\PermissionCategory\PermissionCategoryCollection;

use App\Services\PermissionCategory\PermissionCategoryUpdateService;
use App\Services\PermissionCategory\PermissionCategoryCreateService;

class PermissionCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {       
        $this->authorize('read', PermissionCategory::class);

        $permissionCategories = PermissionCategory::all();
        $data = new PermissionCategoryCollection($permissionCategories);
        return $data;
    }

    public function indexWithPermissions()
    {

        $loggedUser = auth()->user();
        $this->authorize('read', PermissionCategory::class);

        $permissionCategories = PermissionCategory::with('permissions')->orderBy('position', 'ASC')->get();
        return $permissionCategories;
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PermissionCategoryStoreRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('store', PermissionCategory::class);

        $service = New PermissionCategoryCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Display the specified resource.
     */
    public function show(PermissionCategory $permissioncategory)
    {
        $this->authorize('read', PermissionCategory::class);

        return new PermissionCategoryResource($permissioncategory);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PermissionCategoryUpdateRequest $request, PermissionCategory $permissioncategory)
    {
        $this->authorize('update', PermissionCategory::class);

        $service = New PermissionCategoryUpdateService;
        $serviceData = $service->processPostRequest($request, $permissioncategory);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PermissionCategory $permissioncategory)
    {
        $this->authorize('destroy', PermissionCategory::class);

        if($permissioncategory->id == 1){
            return $this->sendError(error: 'Tato kategorie nemůže být smazána');
        } else {
            $permissions = $permissioncategory->permissions;
            if($permissions != null){
                foreach($permissions as $permission){
                    $permission->category_id = 1;
                    $permission->save();
                }
            }
            $permissioncategory->delete();
            return $this->sendResponse(message: 'Kategorie smazána');
        }
    }
}

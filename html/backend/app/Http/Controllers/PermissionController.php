<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\Permission;

use App\Http\Requests\Permission\PermissionStoreRequest;
use App\Http\Requests\Permission\PermissionUpdateRequest;

use App\Http\Resources\Permission\PermissionResource;

use App\Services\Permission\PermissionCreateService;
use App\Services\Permission\PermissionUpdateService;

class PermissionController extends Controller
{

    /**
     * Store a newly created resource in storage.
     */
    public function store(PermissionStoreRequest $request)
    {
        $this->authorize('store', Permission::class);

        $service = New PermissionCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Display the specified resource.
     */
    public function show(Permission $permission)
    {
        $this->authorize('read', Permission::class);

        return new PermissionResource($permission);
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(PermissionUpdateRequest $request, Permission $permission)
    {
        $this->authorize('update', Permission::class);

        $service = New PermissionUpdateService;
        $serviceData = $service->processPostRequest($request, $permission);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Permission $permission)
    {
        $this->authorize('destroy', Permission::class);

        $permission->delete();
        return $this->sendResponse(message: 'Oprávnění odebráno');
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests\AccountingCategory\AccountingCategoryStoreRequest;
use App\Http\Requests\AccountingCategory\AccountingCategoryUpdateRequest;

use App\Http\Resources\AccountingCategory\AccountingCategoryResource;
use App\Http\Resources\AccountingCategory\AccountingCategoryCollection;

use App\Services\AccountingCategory\AccountingCategoryUpdateService;
use App\Services\AccountingCategory\AccountingCategoryCreateService;

use App\Models\AccountingCategory;

class AccountingCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {

        $perPage = (int)$request->perpage;
        $page = (int)$request->page;
        
        $accountingCategories = AccountingCategory::orderBy('id','asc')
            ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);


        $data = new AccountingCategoryCollection($accountingCategories);
        return $data;
    }


    public function store(AccountingCategoryStoreRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('store', AccountingCategory::class);

        $service = New AccountingCategoryCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(AccountingCategoryUpdateRequest $request, AccountingCategory $accountingCategory)
    {
        $this->authorize('update', $accountingCategory);

        $service = New AccountingCategoryUpdateService;
        $serviceData = $service->processPostRequest($request, $accountingCategory);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AccountingCategory $accountingCategory)
    {
        $this->authorize('destroy', $accountingCategory);
        $accountingCategory->delete();
        return $this->sendResponse(message: 'Účetní druh majetku smazán.');
        
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Services\CustomTableSetting\CustomTableSettingUpdateService;

use App\Models\CustomTableSetting;

use App\Http\Resources\CustomTableSetting\CustomTableSettingCollection;

class CustomTableSettingsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $customTableSettings = CustomTableSetting::get();
        $data = new CustomTableSettingCollection($customTableSettings);  
        return $data;
    }

    /**
     * Display the specified resource.
     */
    public function show(string $tableName)
    {
        $customTableSetting = CustomTableSetting::firstOrCreate(['table_name' => $tableName]);
        return response()->json(['data' => $customTableSetting->settings], 200);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $tableName)
    {
        $this->authorize('update', Settings::class);

        $service = New CustomTableSettingUpdateService;
        $serviceData = $service->processPostRequest($request, $tableName);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        } 
    }

}

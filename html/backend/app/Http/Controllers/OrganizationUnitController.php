<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Jobs\AdOrganizationUnitsSync;

use App\Services\OrganizationUnit\OrganizationUnitCreateService;
use App\Services\OrganizationUnit\OrganizationUnitUpdateService;
use App\Services\OrganizationUnit\OrganizationUnitCheckPhoneService;
use App\Services\OrganizationUnit\OrganizationUnitGeneratePasswordService;
use App\Services\OrganizationUnit\OrganizationUnitPromotionService;

use App\Models\OrganizationUnit;

use App\Http\Resources\OrganizationUnit\OrganizationUnitCollection;
use App\Http\Resources\OrganizationUnit\OrganizationUnitPromotionCollection;

use App\Http\Requests\OrganizationUnit\OrganizationUnitStoreRequest;
use App\Http\Requests\OrganizationUnit\OrganizationUnitUpdateRequest;
use App\Http\Requests\OrganizationUnit\OrganizationUnitPromotionRequest;

use Adldap;
use Adldap\Utilities;
use DB;

class OrganizationUnitController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {

        $this->authorize('read', OrganizationUnit::class);

        $promotion = $request->promotion;
        $isClass = $request->is_class;

        $organizationUnit = OrganizationUnit::byPromotion($promotion)
        ->byIsClass($isClass)
        ->whereNotIn('name', [
            "Builtin",
            "Computers",
            "Domain Controllers",
            "ForeignSecurityPrincipals",
            "Keys",
            "LostAndFound",
            "Managed Service Accounts",
            "Program Data",
            "System",
            "NTDS Quotas",
            "TPM Devices"
        ])   
        ->withCount('users')->get();

        $data = new OrganizationUnitCollection($organizationUnit);
        return $data;
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(OrganizationUnitStoreRequest $request)
    {

        $this->authorize('store', OrganizationUnit::class);

        $service = New OrganizationUnitCreateService;
        $serviceData = $service->processPostRequest($request);

       if($serviceData['success']){
           return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
       } else {
           return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
       }  
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(OrganizationUnitUpdateRequest $request, OrganizationUnit $organizationUnit)
    {
        $this->authorize('update', OrganizationUnit::class);

        $service = New OrganizationUnitUpdateService;
        $serviceData = $service->processPostRequest($request, $organizationUnit);
 
        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OrganizationUnit $organizationUnit)
    {
        $this->authorize('destroy', OrganizationUnit::class);

        //Najdeme dane OU ktere chceme smazat
        $ad = new Adldap();
        $ou = Adldap::search()->ous()->whereEquals('distinguishedname', $organizationUnit->distinguished_name)->firstOrFail();

        //ADLAP nepusti mazat pokud v nem neco je - overeni
        $users = Adldap::search()->users()->in($organizationUnit->distinguished_name)->first();
        if($users == null){
            //prazdne smaze
            $ou->delete();
            $organizationUnit->delete();
            return $this->sendResponse(message: 'Organizační jednotka odstraněna');
        } else {
            //pokud neni prazdne tak error
            return $this->sendError(error: 'Organizační jednotka nebyla odstraněna. Zkontrolujte, jestli je prázdná.');
        }
    }

    public function sync()
    {
        $this->authorize('sync', OrganizationUnit::class);

        AdOrganizationUnitsSync::dispatch();
    }

    public function tree()
    {
        $this->authorize('read', OrganizationUnit::class);

        $data = $this->getChildrens();
        return response()->json(['data' => $data], 200);
    }

    public function getChildrens($parent = null){

        if(is_null($parent)){
            $parent = config('ldap.connections.default.settings.base_dn');;
        } 

        $organizationUnits = OrganizationUnit::where(DB::raw('BINARY `parent`'), $parent)
        ->whereNotIn('name', [
            "Builtin",
            "Computers",
            "Domain Controllers",
            "ForeignSecurityPrincipals",
            "Keys",
            "LostAndFound",
            "Managed Service Accounts",
            "Program Data",
            "System",
            "NTDS Quotas",
            "TPM Devices"
        ])->withCount('users')->get();

        $childrens = array();

        foreach($organizationUnits as $organizationUnit){

            $data = [
                'id' => $organizationUnit->id,
                'name' => $organizationUnit->name,
                'promotion' => $organizationUnit->promotion,
                'is_class' => $organizationUnit->is_class,
                'map_bakalari' => $organizationUnit->map_bakalari,
                'map_skola_online' => $organizationUnit->map_skola_online,
                'users_count' => $organizationUnit->users_count,
                'childrens' => $this->getChildrens($organizationUnit->distinguished_name)
            ];
            $childrens[] = $data;
        }
        return $childrens;
    }

    public function checkPhone(Request $request)
    {
        $this->authorize('read', OrganizationUnit::class);

        $service = New OrganizationUnitCheckPhoneService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
           return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
           return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], data: $serviceData['custom_data']);
        }  
    }

    public function generatePassword(Request $request)
    {
        $this->authorize('userPasswordUpdate', OrganizationUnit::class);

        $service = New OrganizationUnitGeneratePasswordService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendTempFile(fileData: $serviceData['file_data']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], data: $serviceData['custom_data']);
        }  
    }

    public function promotion(OrganizationUnitPromotionRequest $request)
    {
        $this->authorize('promotion', OrganizationUnit::class);

        $service = New OrganizationUnitPromotionService;
        $serviceData = $service->processPostRequest($request);
 
        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], customCode: $serviceData['custom_code'], code: $serviceData['status_code'], data: $serviceData['custom_data']);
        }  
    }

    public function promotionIndex()
    {
        $this->authorize('read', OrganizationUnit::class);

        $organizationUnit = OrganizationUnit::byPromotion(1)->get();
        $data = new OrganizationUnitPromotionCollection($organizationUnit);

        return $data;
    }
}

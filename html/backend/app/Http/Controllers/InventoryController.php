<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\Inventory;

use App\Http\Requests\Inventory\InventoryStoreRequest;
use App\Http\Requests\Inventory\InventoryUpdateRequest;

use App\Http\Resources\Inventory\InventoryResource;
use App\Http\Resources\Inventory\InventoryDetailResource;
use App\Http\Resources\Inventory\InventoryCollection;

use App\Services\Inventory\InventoryCreateService;
use App\Services\Inventory\InventoryProcessService;

class InventoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {       
        $this->authorize('read', Inventory::class);
        $loggedUser = auth()->user();

        if($loggedUser->can('property.master')) {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $state = $request->state;
            $userId = $request->user_id;
            $roomId = $request->room_id;
            
            $inventories = Inventory::byState($state)
                ->byUserId($userId)
                ->byRoomId($roomId)
                ->orderBy('id','desc')
                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);

        } else {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $state = $request->state;
            $roomId = $request->room_id;
                   
            $inventories = Inventory::byState($state)
                ->byUserId($loggedUser->id)
                ->byRoomId($roomId)
                ->orderBy('id','desc')
                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);
        } 

        $data = new InventoryCollection($inventories);
        return $data;
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(InventoryStoreRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('store', Inventory::class);

        $service = New InventoryCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Display the specified resource.
     */
    public function show(Inventory $inventory)
    {
        $this->authorize('view', $inventory);

        return new InventoryDetailResource($inventory);
    }


    /**
     * Reinventory the specified resource from storage.
     */
    public function destroy(Inventory $inventory)
    {
        $this->authorize('destroy', $inventory);
        $inventory->delete();
        return $this->sendResponse(message: 'Inventorvní seznam smazán');
        
    }

    
    public function process(Inventory $inventory)
    {
        $this->authorize('process', $inventory);

        $service = New InventoryProcessService;
        $serviceData = $service->processPostRequest($inventory);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }
}

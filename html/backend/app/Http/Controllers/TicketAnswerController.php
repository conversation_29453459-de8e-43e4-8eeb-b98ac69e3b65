<?php

namespace App\Http\Controllers;

use App\Models\Ticket;
use App\Models\TicketAnswer;
use Illuminate\Http\Request;

use App\Services\TicketAnswer\TicketAnswerCreateService;
use App\Services\TicketAnswer\TicketAnswerUpdateService;

class TicketAnswerController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Ticket $ticket, Request $request)
    {
        $this->authorize('store', [TicketAnswer::class, $ticket]);

        $service = New TicketAnswerCreateService;
        $serviceData = $service->processPostRequest($ticket, $request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Ticket $ticket, TicketAnswer $ticketAnswer)
    {
        $this->authorize('update', $ticketAnswer);

        $service = New TicketAnswerUpdateService;
        $serviceData = $service->processPostRequest($ticketAnswer, $request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Ticket $ticket, TicketAnswer $ticketAnswer)
    {
        $loggedUser = auth()->user();
        //$this->authorize('destroy', [$ticket, $loggedUser]);

        $ticketAnswer->delete();
        return $this->sendResponse(message: 'Odpověď smazána');
    }
}

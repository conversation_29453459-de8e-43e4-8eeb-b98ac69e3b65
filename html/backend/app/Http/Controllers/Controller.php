<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;

    /**
     * success response method.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendResponse($result = null, $message = null)
    {
    	$response = [
            'success' => true,
            'data'    => $result,
            'message' => $message,
        ];


        return response()->json($response, 200);
    }

    
    /**
     * success response method.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendTempFile($fileData)
    {
        return response()->download($fileData['path'], $fileData['file_name'], $fileData['headers'])->deleteFileAfterSend();
    }

    public function sendFile($fileData)
    {
        return response()->download($fileData['path'], $fileData['file_name'], $fileData['headers']);
    }


    /**
     * return error response.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendError($error, $errorMessages = [], $code = null, $customCode = null, $data = null)
    {

        if(is_null($code)){
            $code = 400;
        }

    	$response = [
            'success' => false,
            'message' => $error,
            'customCode' => $customCode,
            'data' => $data
        ];


        if(!empty($errorMessages)){
            $response['data'] = $errorMessages;
        }


        return response()->json($response, $code);
    }
}

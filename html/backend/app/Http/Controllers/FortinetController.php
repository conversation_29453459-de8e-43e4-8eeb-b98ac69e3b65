<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\Fortinet\FortinetService;
use App\Services\Fortinet\FortinetLogService;
use App\Services\Fortinet\FortinetWebfilterUpdateService;

use App\Http\Requests\Fortinet\WebfilterUpdateRequest;

class FortinetController extends Controller
{
    public function webfilters()
    {
        $this->authorize('read', Fortinet::class);

        $fortinetService = New FortinetService;

        $serviceData = $fortinetService->get("/api/v2/cmdb/webfilter/profile");
        if($serviceData['success']){
            $data = $this->webfiltersNames($serviceData['custom_data']);
            return $this->sendResponse(result: $data, message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], customCode: $serviceData['custom_code']);
        }  
    }

    public function webfilter($filterName)
    {
        $this->authorize('read', Fortinet::class);

        $fortinetService = New FortinetService;

        $serviceData = $fortinetService->get("/api/v2/cmdb/webfilter/profile/".$filterName);
        if($serviceData['success']){
            $data = $this->filters($serviceData['custom_data']);
            return $this->sendResponse(result: $data, message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], customCode: $serviceData['custom_code']);
        }  
    }

    public function webfilterUpdate($filterName, WebfilterUpdateRequest $request)
    {
        $this->authorize('update', Fortinet::class);

        $service = New FortinetWebfilterUpdateService;
        $serviceData = $service->processPostRequest($filterName, $request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function log(Request $request)
    {
        $service = New FortinetLogService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return response()->make('', 200);
            //return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    

    /*
        Tady funkce na response kolekce, nejde to udelat klasicky
        protoze to nejsou kolekce realnych objektu z Laravelu
        --REFACTOR
    */

    public function webfiltersNames($data){
        $newData = [];
        foreach($data->results as $item){
            $newData[] = $item->name;
        }
        return ['webfilters' => $newData];
    }

    public function filters($data){
        $newData = [];
        $filters = $data->results[0]->{'ftgd-wf'}->filters;

        $i = 0;
        //Ty co prijdou tak jim pridam jmeno
        foreach($filters as $filter){
            $name = $this->getCategoryName($filter->id);

            $newFilter = [];
            $newFilter = [
                'id' => $filter->id,
                'name' => $name,
                'action' => $filter->action
            ];
            
            $newData[] = $newFilter;

            $i++;
        }

        //Ziskam ty co neprisly, allowed pravidla nechodi v response a pridam je
        $categories = $this->getCategories();
        foreach($categories as $category){
            $exist = false;
            foreach($newData as $newItem){
                if($category['id'] == $newItem['id']){
                    $exist = true;
                    break;
                }
            }
            if(!$exist){
                $newFilter = [
                    'id' => $category['id'],
                    'name' => $category['cs'],
                    'action' => "allow"
                ];
                
                $newData[] = $newFilter;
            }
        }


        //Setrizeni vsech poli
        $indexedFilters = array_values($newData);

        usort($indexedFilters, function ($a, $b) {
            return $a['id'] - $b['id'];
        });

        $sortedFilters = $indexedFilters;
        return ['filters' => $sortedFilters];
    }

    public function getCategories(){
        $categories = [
            0 => ['id' => 0, 'en' => 'Unrated', 'cs' => 'Nehodnoceno'],
            1 => ['id' => 1, 'en' => 'Drug abuse', 'cs' => 'Zneužívání drog'],
            2 => ['id' => 2, 'en' => 'Alternative beliefs', 'cs' => 'Alternativní víry'],
            3 => ['id' => 3, 'en' => 'Hacking', 'cs' => 'Hacking'],
            4 => ['id' => 4, 'en' => 'Illegal or unethical', 'cs' => 'Nelegální nebo neetické'],
            5 => ['id' => 5, 'en' => 'Discrimination', 'cs' => 'Diskriminace'],
            6 => ['id' => 6, 'en' => 'Explicit violence', 'cs' => 'Explicitní násilí'],
            7 => ['id' => 7, 'en' => 'Abortion', 'cs' => 'Potrat'],
            8 => ['id' => 8, 'en' => 'Other adult materials', 'cs' => 'Ostatní materiály pro dospělé'],
            9 => ['id' => 9, 'en' => 'Advocacy organizations', 'cs' => 'Organizace na obranu práv'],
            11 => ['id' => 11, 'en' => 'Gambling', 'cs' => 'Hazardní hry'],
            12 => ['id' => 12, 'en' => 'Extremist groups', 'cs' => 'Extremistické skupiny'],
            13 => ['id' => 13, 'en' => 'Nudity and risque', 'cs' => 'Nahota a rizikové obsahy'],
            14 => ['id' => 14, 'en' => 'Pornography', 'cs' => 'Pornografie'],
            15 => ['id' => 15, 'en' => 'Dating', 'cs' => 'Seznamování'],
            16 => ['id' => 16, 'en' => 'Weapons (sales)', 'cs' => 'Zbraně (prodej)'],
            17 => ['id' => 17, 'en' => 'Advertising', 'cs' => 'Reklama'],
            18 => ['id' => 18, 'en' => 'Brokerage and trading', 'cs' => 'Burza a obchodování'],
            19 => ['id' => 19, 'en' => 'Freeware and software downloads', 'cs' => 'Freewarové a stahování software'],
            20 => ['id' => 20, 'en' => 'Games', 'cs' => 'Hry'],
            23 => ['id' => 23, 'en' => 'Web-based email', 'cs' => 'Webová emailová služba'],
            24 => ['id' => 24, 'en' => 'File sharing and storage', 'cs' => 'Sdílení a ukládání souborů'],
            25 => ['id' => 25, 'en' => 'Streaming media and download', 'cs' => 'Streamování médií a stahování'],
            26 => ['id' => 26, 'en' => 'Malicious websites', 'cs' => 'Zákeřné webové stránky'],
            28 => ['id' => 28, 'en' => 'Entertainment', 'cs' => 'Zábava'],
            29 => ['id' => 29, 'en' => 'Arts and culture', 'cs' => 'Umění a kultura'],
            30 => ['id' => 30, 'en' => 'Education', 'cs' => 'Vzdělání'],
            31 => ['id' => 31, 'en' => 'Finance and banking', 'cs' => 'Finance a bankovnictví'],
            33 => ['id' => 33, 'en' => 'Health and wellness', 'cs' => 'Zdraví a pohoda'],
            34 => ['id' => 34, 'en' => 'Job search', 'cs' => 'Hledání zaměstnání'],
            35 => ['id' => 35, 'en' => 'Medicine', 'cs' => 'Medicína'],
            36 => ['id' => 36, 'en' => 'News and media', 'cs' => 'Zprávy a média'],
            37 => ['id' => 37, 'en' => 'Social networking', 'cs' => 'Sociální sítě'],
            38 => ['id' => 38, 'en' => 'Political organizations', 'cs' => 'Politické organizace'],
            39 => ['id' => 39, 'en' => 'Reference', 'cs' => 'Reference'],
            40 => ['id' => 40, 'en' => 'Global religion', 'cs' => 'Světová náboženství'],
            41 => ['id' => 41, 'en' => 'Search engines and portals', 'cs' => 'Vyhledávače a portály'],
            42 => ['id' => 42, 'en' => 'Shopping', 'cs' => 'Nákupy'],
            43 => ['id' => 43, 'en' => 'General organizations', 'cs' => 'Obecné organizace'],
            44 => ['id' => 44, 'en' => 'Society and lifestyles', 'cs' => 'Společnost a životní styl'],
            46 => ['id' => 46, 'en' => 'Sports', 'cs' => 'Sporty'],
            47 => ['id' => 47, 'en' => 'Travel', 'cs' => 'Cestování'],
            48 => ['id' => 48, 'en' => 'Personal vehicles', 'cs' => 'Osobní vozidla'],
            49 => ['id' => 49, 'en' => 'Business', 'cs' => 'Podnikání'],
            50 => ['id' => 50, 'en' => 'Information and computer security', 'cs' => 'Informační a počítačová bezpečnost'],
            51 => ['id' => 51, 'en' => 'Government and legal organizations', 'cs' => 'Vláda a právní organizace'],
            52 => ['id' => 52, 'en' => 'Information technology', 'cs' => 'Informační technologie'],
            53 => ['id' => 53, 'en' => 'Armed forces', 'cs' => 'Ozbrojené síly'],
            54 => ['id' => 54, 'en' => 'Dynamic content', 'cs' => 'Dynamický obsah'],
            55 => ['id' => 55, 'en' => 'Meaningless content', 'cs' => 'Bezvýznamný obsah'],
            56 => ['id' => 56, 'en' => 'Web hosting', 'cs' => 'Webhosting'],
            57 => ['id' => 57, 'en' => 'Marijuana', 'cs' => 'Marihuana'],
            58 => ['id' => 58, 'en' => 'Folklore', 'cs' => 'Folklor'],
            59 => ['id' => 59, 'en' => 'Proxy avoidance', 'cs' => 'Vyhýbání se proxy'],
            61 => ['id' => 61, 'en' => 'Phishing', 'cs' => 'Phishing'],
            62 => ['id' => 62, 'en' => 'Plagiarism', 'cs' => 'Plagiátorství'],
            63 => ['id' => 63, 'en' => 'Sex education', 'cs' => 'Sexuální výchova'],
            64 => ['id' => 64, 'en' => 'Alcohol', 'cs' => 'Alkohol'],
            65 => ['id' => 65, 'en' => 'Tobacco', 'cs' => 'Tabák'],
            66 => ['id' => 66, 'en' => 'Lingerie and swimsuit', 'cs' => 'Spodní prádlo a plavky'],
            67 => ['id' => 67, 'en' => 'Sports hunting and war games', 'cs' => 'Sportovní lov a válečné hry'],
            68 => ['id' => 68, 'en' => 'Web chat', 'cs' => 'Webový chat'],
            69 => ['id' => 69, 'en' => 'Instant messaging', 'cs' => 'Okamžité zprávy'],
            70 => ['id' => 70, 'en' => 'Newsgroups and message boards', 'cs' => 'Diskuze a diskusní fóra'],
            71 => ['id' => 71, 'en' => 'Digital postcards', 'cs' => 'Digitální pohlednice'],
            72 => ['id' => 72, 'en' => 'Peer-to-peer file sharing', 'cs' => 'Peer-to-peer sdílení souborů'],
            75 => ['id' => 75, 'en' => 'Internet radio and TV', 'cs' => 'Internetové rádio a TV'],
            76 => ['id' => 76, 'en' => 'Internet telephony', 'cs' => 'Internetové volání'],
            77 => ['id' => 77, 'en' => 'Child education', 'cs' => 'Vzdělávání dětí'],
            78 => ['id' => 78, 'en' => 'Real estate', 'cs' => 'Realitní záležitosti'],
            79 => ['id' => 79, 'en' => 'Restaurant and dining', 'cs' => 'Restaurace a stolování'],
            80 => ['id' => 80, 'en' => 'Personal websites and blogs', 'cs' => 'Osobní weby a blogy'],
            81 => ['id' => 81, 'en' => 'Secure websites', 'cs' => 'Bezpečné weby'],
            82 => ['id' => 82, 'en' => 'Content servers', 'cs' => 'Obsahové servery'],
            83 => ['id' => 83, 'en' => 'Child abuse', 'cs' => 'Zneužívání dětí'],
            84 => ['id' => 84, 'en' => 'Web-based applications', 'cs' => 'Webové aplikace'],
            85 => ['id' => 85, 'en' => 'Domain parking', 'cs' => 'Parkování domén'],
            86 => ['id' => 86, 'en' => 'Spam URLs', 'cs' => 'Spamové URL'],
            87 => ['id' => 87, 'en' => 'Personal privacy', 'cs' => 'Osobní soukromí'],
            88 => ['id' => 88, 'en' => 'Dynamic DNS', 'cs' => 'Dynamický DNS'],
            89 => ['id' => 89, 'en' => 'Auction', 'cs' => 'Aukce'],
            90 => ['id' => 90, 'en' => 'Newly observed domain', 'cs' => 'Nově pozorovaná doména'],
            91 => ['id' => 91, 'en' => 'Newly registered domain', 'cs' => 'Nově zaregistrovaná doména'],
            92 => ['id' => 92, 'en' => 'Charitable organizations', 'cs' => 'Charitativní organizace'],
            93 => ['id' => 93, 'en' => 'Remote access', 'cs' => 'Vzdálený přístup'],
            94 => ['id' => 94, 'en' => 'Web analytics', 'cs' => 'Webová analýza'],
            95 => ['id' => 95, 'en' => 'Online meeting', 'cs' => 'Online setkání'],
            96 => ['id' => 96, 'en' => 'Terrorism', 'cs' => 'Terorismus'],
            97 => ['id' => 97, 'en' => 'URL shortening', 'cs' => 'Zkracování URL'],
            98 => ['id' => 98, 'en' => 'Crypto mining', 'cs' => 'Kryptomining'],
            99 => ['id' => 99, 'en' => 'Potentially unwanted program', 'cs' => 'Potenciálně nežádoucí program'],
            100 => ['id' => 100, 'en' => null, 'cs' => null],
        ];

        return $categories;
    }

    public function getCategoryName($id){
        $categories = $this->getCategories();

        if(isset($categories[$id])){
            return $categories[$id]['cs'];
        } else {
            return null;
        }
    }

}

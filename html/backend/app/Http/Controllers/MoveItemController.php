<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\Move;
use App\Models\MoveItem;

use App\Http\Requests\MoveItem\MoveItemStoreRequest;
use App\Http\Requests\MoveItem\MoveItemMultipleStoreRequest;
use App\Http\Requests\MoveItem\MoveItemMultipleCheckRequest;

use App\Services\MoveItem\MoveItemDeleteService;
use App\Services\MoveItem\MoveItemCreateService;
use App\Services\MoveItem\MoveItemMultipleCreateService;
use App\Services\MoveItem\MoveItemMultipleCheckService;

class MoveItemController extends Controller
{

    /**
     * Store a newly created resource in storage.
     */
    public function store(MoveItemStoreRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('items', Move::class);

        $service = New MoveItemCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function multipleCheck(MoveItemMultipleCheckRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('items', Move::class);

        $service = New MoveItemMultipleCheckService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function multipleStore(MoveItemMultipleStoreRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('items', Move::class);

        $service = New MoveItemMultipleCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], data: $serviceData['custom_data'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MoveItem $moveItem)
    {
        $this->authorize('items', Move::class);

        $service = New MoveItemDeleteService;
        $serviceData = $service->processPostRequest($moveItem);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
        
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\InventoryItem;

use App\Http\Requests\InventoryItem\InventoryItemUpdateRequest;
use App\Http\Requests\InventoryItem\InventoryItemMultipleCheckRequest;
use App\Http\Requests\InventoryItem\InventoryItemMultipleUpdateRequest;

use App\Services\InventoryItem\InventoryItemUpdateService;
use App\Services\InventoryItem\InventoryItemMultipleCheckService;
use App\Services\InventoryItem\InventoryItemMultipleUpdateService;

class InventoryItemController extends Controller
{
    /**
     * Update the specified resource in storage.
     */
    public function update(InventoryItemUpdateRequest $request, InventoryItem $inventoryItem)
    {
        $this->authorize('items', Inventory::class);

        $service = New InventoryItemUpdateService;
        $serviceData = $service->processPostRequest($request, $inventoryItem);
 
        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function multipleCheck(InventoryItemMultipleCheckRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('items', Inventory::class);

        $service = New InventoryItemMultipleCheckService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function multipleUpdate(InventoryItemMultipleUpdateRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('items', Move::class);

        $service = New InventoryItemMultipleUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], data: $serviceData['custom_data'], code: $serviceData['status_code']);
        }  
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests\Role\RoleStoreRequest;
use App\Http\Requests\Role\RoleUpdateRequest;

use App\Http\Resources\Role\RoleResource;
use App\Http\Resources\Role\RoleDetailResource;
use App\Http\Resources\Role\RoleCollection;

use App\Models\Role;

use App\Services\Role\RoleCreateService;
use App\Services\Role\RoleUpdateService;

class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('read', Role::class);

        $perPage = (int)$request->perpage;
        $page = (int)$request->page;

        $roles = Role::paginate($perPage = 15, $columns = ['*'], $pageName = 'page', $page);
        $data = new RoleCollection($roles);
        return $data;
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(RoleStoreRequest $request)
    {
        $this->authorize('store', Role::class);

        $service = New RoleCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Display the specified resource.
     */
    public function show(Role $role)
    {
        $this->authorize('read', Role::class);

        return new RoleDetailResource($role);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(RoleUpdateRequest $request, Role $role)
    {
        $this->authorize('update', Role::class);

        $service = New RoleUpdateService;
        $serviceData = $service->processPostRequest($request, $role);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Role $role)
    {
        $this->authorize('destroy', Role::class);

        $role->delete();
        return $this->sendResponse(message: 'Role odebrána');
    }
}

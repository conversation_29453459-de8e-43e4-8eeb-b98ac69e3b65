<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Resources\User\LoginResource;

use App\Services\Auth\SendVerifyEmailService;
use App\Services\Auth\SendResetPasswordEmailService;
use App\Services\Auth\VerifyEmailService;
use App\Services\Auth\SetNewPasswordService;
use App\Services\Permission\GetPermissionService;

use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\LoginAdRequest;
use App\Http\Requests\Auth\EmailRequest;
use App\Http\Requests\Auth\SetNewPasswordRequest;

use App\Models\User;

use Auth;
use Adldap;

class AuthController extends Controller
{
    public function login(LoginRequest $request): JsonResponse
    {
        if(Auth::attempt(['email' => $request->email, 'password' => $request->password])){
            $user = Auth::user(); 
            if($user->active_directory == 0){
                $permissionService = New GetPermissionService();
                $request->session()->regenerate();
                $success['user'] =  New LoginResource($user);
                //$success['permissions'] =  $user->getAllPermissions()->pluck('name')->toArray();
                $success['permissions'] = $permissionService->getPermissionsArray($user);
    
                if(config('system.NEED_EMAIL_VERIFICATION')){
                    if($user->isEmailVerified()){
                        return $this->sendResponse($success, 'Přihlášení bylo úspěšné.');  
                    } else {
                        return $this->sendError(error: 'Email nebyl ověřen.', code: 401, customCode: 40101);
                    }
                } else {
                    return $this->sendResponse($success, 'Přihlášení bylo úspěšné.');
                }
            } else {
                return $this->sendError(error: 'Špatný typ přihlášení.', code: 401);
            }

        } else {
            return $this->sendError(error: 'Špatné přihlašovací údaje.', code: 401);
        } 
    }

    public function loginAd(LoginAdRequest $request): JsonResponse
    {

        if(Adldap::auth()->attempt($request->username, $request->password)) {
            $adUser = Adldap::search()->where('samaccountname', '=', $request->username)->first();
            if(is_null($adUser)){
                return $this->sendError(error: 'Špatné přihlašovací údaje.', code: 401);
            }

            $user = User::where('guid',$adUser->getConvertedGuid())->first();
            if(is_null($user)){
                return $this->sendError(error: 'Špatné přihlašovací údaje.', code: 401);
            }

            if($user->active_directory == 0){
                return $this->sendError(error: 'Špatné přihlašovací údaje.', code: 401);
            }

            if($user->username != $request->username){
                Auth::login($user);
                if($user->can('active_directory.login')){
                        $permissionService = New GetPermissionService();
                        $request->session()->regenerate();
                        $success['user'] =  New LoginResource($user);
                        $success['permissions'] = $permissionService->getPermissionsArray($user);
                        return $this->sendResponse($success, 'Přihlášení bylo úspěšné.');
                    } else {
                        return $this->sendError(error: 'Uživatel nemá práva k přihlášení.', code: 401); 
                    }
                 } else {
                    return $this->sendError(error: 'Uživatel není synchronizován.', code: 401);  
              }

            } else {
                return $this->sendError(error: 'Špatné přihlašovací údaje.', code: 401);
            }

    }

    
    public function loginApi(LoginRequest $request): JsonResponse
    {
        if($request->active_directory == 1){
            try {
                if (Adldap::auth()->attempt($request->username, $request->password)) {
                    $adUser = Adldap::search()->where('samaccountname', '=', $request->username)->first();
                    $user = User::where('guid',$adUser->getConvertedGuid())->first();
                    if($user != null){
                        if($user->can('active_directory.login')){
                            $permissionService = New GetPermissionService();
                            $success['token'] =  $user->createToken('ADAM')->plainTextToken; 
                            $success['user'] =  New LoginResource($user);
                            $success['permissions'] = $permissionService->getPermissionsArray($user);
                            return $this->sendResponse($success, 'Přihlášení bylo úspěšné.');
                        } else {
                            return $this->sendError(error: 'Uživatel nemá práva k přihlášení.', code: 401); 
                        }
                    } else {
                        return $this->sendError(error: 'Uživatel není synchronizován.', code: 401);  
                    }

                } else {
                    return $this->sendError(error: 'Špatné přihlašovací údaje.', code: 401);
                }
            } catch (Adldap\Auth\UsernameRequiredException $e) {
                return $this->sendError(error: 'Špatné přihlašovací údaje.', code: 401);
            } catch (Adldap\Auth\PasswordRequiredException $e) {
                return $this->sendError(error: 'Špatné přihlašovací údaje.', code: 401);
            }     
        } else {
            if(Auth::attempt(['email' => $request->email, 'password' => $request->password])){ 
                $user = Auth::user(); 
                if($user->active_directory == 0){
                    $permissionService = New GetPermissionService();
    
                    if(config('login.NEED_VERIFIED_EMAIL')){
                        if(is_null($user->email_verified_at)){
                            return $this->sendError(error: 'Email nebyl ověřen.', code: 401, customCode: 40101);  
                        }
                    }
        
                    $success['token'] =  $user->createToken('ADAM')->plainTextToken; 
                    $success['user'] =  New LoginResource($user);
                    //$success['permissions'] =  $user->getAllPermissions()->pluck('name')->toArray();
                    $success['permissions'] = $permissionService->getPermissionsArray($user);
           
                    if(config('system.NEED_EMAIL_VERIFICATION')){
                        if($user->isEmailVerified()){
                            return $this->sendResponse($success, 'Přihlášení bylo úspěšné.');  
                        } else {
                            return $this->sendError(error: 'Email nebyl ověřen.', code: 401, customCode: 40101);
                        }
                    } else {
                        return $this->sendResponse($success, 'Přihlášení bylo úspěšné.');
                    }
                } else {
                    return $this->sendError(error: 'Špatný typ přihlášení.', code: 401);
                }       
            } 
            else{ 
                return $this->sendError(error: 'Špatné přihlašovací údaje.', code: 401);
            } 
        }

    }
    

    public function logout(Request $request): JsonResponse
    {
        Auth::guard('web')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return $this->sendResponse(message: 'Odhlášení bylo úspěšné.');
    }

    public function verifyEmail(Request $request): JsonResponse
    {
        if(config('system.NEED_EMAIL_VERIFICATION')){
            $token = $request->token;
            $service = New VerifyEmailService;
            $serviceData = $service->process($token);
    
    
            if($serviceData['success']){
                return $this->sendResponse(message: $serviceData['message']);
            } else {
                return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
            }  
        } else {
            return $this->sendError(error: "Ověřování emailu je pozastaveno.");
        }
    }

    public function sendVerifyEmail(EmailRequest $request)
    {
        if(config('system.NEED_EMAIL_VERIFICATION')){
            $service = New SendVerifyEmailService;
            $serviceData = $service->processPostRequest($request, true);

            if($serviceData['success']){
                return $this->sendResponse(message: $serviceData['message']);
            } else {
                return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
            }  
        } else {
            return $this->sendError(error: "Ověřování emailu je pozastaveno");
        }
    }

    public function sendResetPasswordEmail(EmailRequest $request)
    {
        $service = New SendResetPasswordEmailService;
        $serviceData = $service->processPostRequest($request, true);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function setNewPassword(SetNewPasswordRequest $request): JsonResponse
    {
        $service = New SetNewPasswordService;
        $serviceData = $service->processPostRequest($request); 
    
        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Module;

use App\Http\Resources\Module\ModuleCollection;
use App\Http\Resources\Module\ModuleResource;

class ModuleController extends Controller
{
    public function index()
    {
        $propertyRecords = Module::firstOrCreate(['name' => 'property_records'], ['name' => 'property_records', 'enable' => 0]);

        $collection = collect([$propertyRecords]);
        
        $data = new ModuleCollection($collection);
        return $data;
    }
}

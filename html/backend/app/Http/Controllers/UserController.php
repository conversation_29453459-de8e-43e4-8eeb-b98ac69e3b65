<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\User\ProfilUpdateRequest;
use App\Http\Requests\User\ProfilEmailUpdateRequest;
use App\Http\Requests\User\ProfilPasswordUpdateRequest;
use App\Http\Requests\User\AdUserStoreRequest;
use App\Http\Requests\User\AdUserStudentStoreRequest;
use App\Http\Requests\User\AdUserGuestStoreRequest;
use App\Http\Requests\User\AdUserEmployeeStoreRequest;
use App\Http\Requests\User\AdUserUpdateRequest;
use App\Http\Requests\User\UserStoreRequest;
use App\Http\Requests\User\UserUpdateRequest;
use App\Http\Requests\User\UserPasswordUpdateRequest;
use App\Http\Requests\User\UserRolesUpdateRequest;
use App\Http\Requests\User\UserPermissionsUpdateRequest;
use App\Http\Requests\User\AdUserPasswordUpdateRequest;
use App\Http\Requests\User\UserMultipleDeleteRequest;

use App\Http\Resources\User\UserResource;
use App\Http\Resources\User\UserDetailResource;
use App\Http\Resources\User\UserRolePermissionResource;
use App\Http\Resources\User\UserCollection;
use App\Http\Resources\User\UserPropertyCollection;
use App\Http\Resources\User\UserShortCollection;

use App\Services\User\ProfilUpdateService;
use App\Services\User\EmailUpdateService;
use App\Services\User\PasswordUpdateService;

use App\Services\User\UserUpdateService;
use App\Services\User\UserCreateService;
use App\Services\User\UserDeleteService;
use App\Services\User\UserMultipleDeleteService;
use App\Services\User\UserRoleUpdateService;
use App\Services\User\UserPermissionUpdateService;
use App\Services\User\UserPasswordUpdateService;
use App\Services\User\AdUserPasswordUpdateService;
use App\Services\User\AdUserCreateService;
use App\Services\User\AdUserStudentCreateService;
use App\Services\User\AdUserEmployeeCreateService;
use App\Services\User\AdUserGuestCreateService;
use App\Services\User\AdUserUpdateService;
use App\Services\User\UserCheckPhoneService;
use App\Services\User\AdUserGeneratePasswordService;
use App\Services\User\AdUserDisableService;
use App\Services\User\AdUserEnableService;
use App\Services\User\AdUserImportService;
use App\Services\User\AdUserMultipleProfileDataUpdateService;
use App\Services\User\AdUserMultipleOuUpdateService;
use App\Services\User\AdUserMultipleGroupUpdateService;
use App\Services\User\UserGeneratePropertyPdfService;

use App\Jobs\AdUsersSync;

use App\Models\User;
use App\Models\Group;

use App\Services\File\GenerateTempXmlService;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function profil()
    {
        $loggedUser = auth()->user();
        $this->authorize('profil', User::class);

        return $loggedUser;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('read', User::class);

        $perPage = (int)$request->perpage;
        $page = (int)$request->page;
        $search = $request->search;

        $orderBy = $request->orderby;
        $order = $request->order;
        $withDeleted = $request->with_deleted;

        $organizationUnit = $request->organization_unit;
        $missingTelNumber = $request->missing_tel_number;

        if(!isset($orderBy) or $orderBy == ""){
            $orderBy = 'last_name';
        }

        if(!isset($order) or $order == ""){
            $order = 'asc';
        }

        $users = User::byWithDeleted($withDeleted)
                        ->search($search)
                        ->byOrganizationUnit($organizationUnit)
                        ->byMissingTelNumber($missingTelNumber)                        
                        ->orderBy($orderBy,$order)
                        ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);

        $data = new UserCollection($users);
        return $data;
    }

    public function indexProperty(Request $request)
    {
        $this->authorize('read', User::class);

        $perPage = (int)$request->perpage;
        $page = (int)$request->page;
        $search = $request->search;
        $deleted = $request->deleted;

        $orderBy = $request->orderby;
        $order = $request->order;

        if(!isset($orderBy) or $orderBy == ""){
            $orderBy = 'last_name';
        }

        if(!isset($order) or $order == ""){
            $order = 'asc';
        }

        $users = User::search($search)
                        ->ByWithItems()
                        ->withTrashed()
                        ->ByDeleted($deleted)
                        ->orderBy($orderBy,$order)
                        ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);

        $data = new UserPropertyCollection($users);
        return $data;
    }

    public function indexGroup(Group $group, Request $request)
    {
        $this->authorize('read', User::class);

        $perPage = (int)$request->perpage;
        $page = (int)$request->page;
        $search = $request->search;

        $orderBy = $request->orderby;
        $order = $request->order;

        if(!isset($orderBy) or $orderBy == ""){
            $orderBy = 'last_name';
        }

        if(!isset($order) or $order == ""){
            $order = 'asc';
        }

        $users = User::search($search)
                        ->ByWithGroup($group)
                        ->orderBy($orderBy,$order)
                        ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);

        $data = new UserShortCollection($users);
        return $data;
    }

    public function generatePropertyPdf(Request $request)
    {
        $this->authorize('read', User::class);

        $service = new UserGeneratePropertyPdfService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendTempFile(fileData: $serviceData['file_data']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $this->authorize('read', User::class);
        return new UserDetailResource($user);
    }

        /**
     * Display the specified resource.
     */
    public function rolePermissionShow(User $user)
    {
        $this->authorize('rolePermissionShow', User::class);
        return new UserRolePermissionResource($user);
    }

    public function update(UserUpdateRequest $request, User $user)
    {
        $this->authorize('update', [User::class, $user]);

        $service = New UserUpdateService;
        $serviceData = $service->processPostRequest($request, $user);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }


    public function updateAd(AdUserUpdateRequest $request, User $user)
    {
        $this->authorize('update', User::class);

        $service = New UserUpdateService;
        $serviceData = $service->processPostRequest($request, $user);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }


    /**
     * Update the specified resource in storage.
     */
    public function profilUpdate(ProfilUpdateRequest $request)
    {
        $loggedUser = auth()->user();
        $this->authorize('profilUpdate', User::class);

        $service = New ProfilUpdateService;
        $serviceData = $service->processPostRequest($request, $loggedUser);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function profilEmailUpdate(ProfilEmailUpdateRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('profilEmailUpdate', User::class);

        $service = New EmailUpdateService;
        $serviceData = $service->processPostRequest($request, $loggedUser);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function profilPasswordUpdate(ProfilPasswordUpdateRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('profilPasswordUpdate', User::class);

        $service = New PasswordUpdateService;
        $serviceData = $service->processPostRequest($request, $loggedUser);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function userPasswordUpdate(UserPasswordUpdateRequest $request, User $user)
    {
        $this->authorize('userPasswordUpdate', [User::class, $user]);

        $service = New UserPasswordUpdateService;
        $serviceData = $service->processPostRequest($request, $user);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function userAdPasswordUpdate(AdUserPasswordUpdateRequest $request, User $user)
    {
        $this->authorize('userPasswordUpdate', [User::class, $user]);

        $service = New AdUserPasswordUpdateService;
        $serviceData = $service->processPostRequest($request, $user);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserStoreRequest $request)
    {
        $this->authorize('store', User::class);

        $service = New UserCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }


    public function storeAd(AdUserStoreRequest $request)
    {
       $this->authorize('store', User::class);

       $service = New AdUserCreateService;
       $serviceData = $service->processPostRequest($request);

       if($serviceData['success']){
           return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
       } else {
           return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
       }  
    }

    public function storeAdStudent(AdUserStudentStoreRequest $request)
    {
       $this->authorize('store', User::class);

       $service = New AdUserStudentCreateService;
       $serviceData = $service->processPostRequest($request);

       if($serviceData['success']){
           return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
       } else {
           return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
       }  
    }

    public function storeAdGuest(AdUserGuestStoreRequest $request)
    {
       $this->authorize('store', User::class);

       $service = New AdUserGuestCreateService;
       $serviceData = $service->processPostRequest($request);

       if($serviceData['success']){
           return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
       } else {
           return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
       }  
    }

    public function storeAdEmployee(AdUserEmployeeStoreRequest $request)
    {
       $this->authorize('store', User::class);

       $service = New AdUserEmployeeCreateService;
       $serviceData = $service->processPostRequest($request);

       if($serviceData['success']){
           return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
       } else {
           return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
       }  
    }

    public function rolesUpdate(UserRolesUpdateRequest $request, User $user)
    {
        $this->authorize('rolesUpdate', [User::class, $user]);

        $service = New UserRoleUpdateService;

        $serviceData = $service->processPostRequest($request, $user);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function permissionsUpdate(UserPermissionsUpdateRequest $request, User $user)
    {
        $this->authorize('permissionsUpdate', [User::class, $user]);

        $service = New UserPermissionUpdateService;

        $serviceData = $service->processPostRequest($request, $user);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function destroy(User $user)
    {
        $this->authorize('destroy', [User::class, $user]);

        $service = New UserDeleteService;

        $serviceData = $service->processPostRequest($user);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }


    public function multipleDestroy(UserMultipleDeleteRequest $request)
    {
        $service = New UserMultipleDeleteService;

        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function sync()
    {
        $this->authorize('sync', User::class);

        AdUsersSync::dispatch(); 
    }

    public function export(Request $request)
    {

        $this->authorize('read', User::class);
        
        $organizationUnits = $request->organization_units;
        $type = $request->type;

        if($type == "bakalari"){
            $data = User::select('first_name AS JMENO','last_name AS PRIJMENI')
                ->organizationUnits($organizationUnits)
                ->where('active_directory',1)
                ->get();

            $name = "Export uživatelů - Bakaláři";  
        }

        if($type == "skolaonline"){
            $data = User::select('first_name AS JMENO','last_name AS PRIJMENI')
                ->organizationUnits($organizationUnits)
                ->where('active_directory',1)
                ->get();

            $name = "Export uživatelů - Škola Online";  
        }
     
        
        $service = New GenerateTempXmlService;
        $fileName = $service->process($data->toArray(),$name);

        $headers = [
            'File-Name' => $name.'.xlsx'
        ];

        return response()->download($fileName, $fileName, $headers)->deleteFileAfterSend();
    }

    public function checkPhone(Request $request)
    {
        $this->authorize('read', User::class);
        $service = New UserCheckPhoneService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
           return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
           return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], data: $serviceData['custom_data']);
        }  
    }

    public function generatePassword(Request $request)
    {
        $this->authorize('userPasswordUpdate', User::class);
        $service = New AdUserGeneratePasswordService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendTempFile(fileData: $serviceData['file_data']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], data: $serviceData['custom_data']);
        }  
    }

    public function disable(Request $request)
    {
        $this->authorize('update', User::class);

        $service = New AdUserDisableService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], data: $serviceData['custom_data']);
        }  
    }

    public function enable(Request $request)
    {
        $this->authorize('update', User::class);
        $service = New AdUserEnableService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], data: $serviceData['custom_data']);
        }  
    }


    public function updateAdProfileData(Request $request)
    {
        $this->authorize('update', User::class);
        $service = New AdUserMultipleProfileDataUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], data: $serviceData['custom_data']);
        }  
    }

    public function import(Request $request)
    {
        $this->authorize('store', User::class);

        $service = New AdUserImportService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], customCode: $serviceData['custom_code'], data: $serviceData['custom_data']);
        }  
    }

    public function updateAdOuData(Request $request)
    {
        $this->authorize('update', User::class);
        $service = New AdUserMultipleOuUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], data: $serviceData['custom_data']);
        }  
    }

    public function updateAdGroupData(Request $request)
    {
        $this->authorize('update', User::class);
        $service = New AdUserMultipleGroupUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code'], data: $serviceData['custom_data']);
        }  
    }
}

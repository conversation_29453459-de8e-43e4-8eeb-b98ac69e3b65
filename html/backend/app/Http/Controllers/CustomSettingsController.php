<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\CustomSetting;

class CustomSettingsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function publicIndex()
    {
        $customSettings = CustomSetting::get();

        $settings = [];

        foreach ($customSettings as $setting) {
            $settings[$setting['key']] = $setting['value'];
        }

        return response()->json(['data' =>  $settings ], 200);
    }

}

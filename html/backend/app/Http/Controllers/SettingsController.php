<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Requests\Settings\SettingsFortinetUpdateRequest;
use App\Http\Requests\Settings\SettingsGsmGateUpdateRequest;
use App\Http\Requests\Settings\SettingsMysqlUpdateRequest;
use App\Http\Requests\Settings\SettingsActiveDirectoryUpdateRequest;
use App\Http\Requests\Settings\SettingsSchoolSmtpUpdateRequest;
use App\Http\Requests\Settings\SettingsDefaultGroupsOusUpdateRequest;

use App\Services\Settings\SettingsFortinetUpdateService;
use App\Services\Settings\SettingsGsmGateUpdateService;
use App\Services\Settings\SettingsMysqlUpdateService;
use App\Services\Settings\SettingsActiveDirectoryUpdateService;
use App\Services\Settings\SettingsSchoolSmtpUpdateService;
use App\Services\Settings\SettingsLicenseUpdateService;
use App\Services\Settings\SettingsDefaultGroupsOusUpdateService;
use App\Services\System\LicenseCheckService;
use App\Services\System\ActiveDirectoryCheckService;


class SettingsController extends Controller
{
    public function index()
    {
        $this->authorize('read', Settings::class);

        $hosts = config('ldap.connections.default.settings.hosts');
        $data = [
            'db_host' => config('database.connections.mysql.host'),
            'db_port' => config('database.connections.mysql.port'),
            'db_database' => config('database.connections.mysql.database'),
            'db_username' => config('database.connections.mysql.username'),
            'db_password' => null,
            
            'ldap_hosts' => $hosts[0],
            'ldap_port' => config('ldap.connections.default.settings.port'),
            'ldap_base_dn' => config('ldap.connections.default.settings.base_dn'),
            'ldap_username' => config('ldap.connections.default.settings.username'),
            'ldap_password' => null,
            'ldap_account_prefix' => config('ldap.connections.default.settings.account_prefix'),
            'ldap_account_suffix' => config('ldap.connections.default.settings.account_suffix'),
            'ldap_use_ssl' => config('ldap.connections.default.settings.use_ssl'),
            'ldap_use_tls' => config('ldap.connections.default.settings.use_tls'),
            'ldap_username_scheme' => config('system.active_directory.scheme'),
            'ldap_user_password_complexity' => config('system.active_directory.user_password_complexity'),
            'ldap_user_password_length' => config('system.active_directory.user_password_length'),

            'gsm_gate_ip' => config('system.gsm_gate.ip'),
            'gsm_gate_account' => config('system.gsm_gate.account'),
            'gsm_gate_password' => null,
            'gsm_gate_sim_port' => config('system.gsm_gate.sim_port'),
            'gsm_send_sms' => config('system.gsm_gate.send_sms'),

            'fortinet_ip' => config('system.fortinet.ip'),
            'fortinet_token' => null,

            'school_mail_host' => config('mail.mailers.smtp_school.host'),
            'school_mail_port' => config('mail.mailers.smtp_school.port'),
            'school_mail_username' => config('mail.mailers.smtp_school.username'),
            'school_mail_password' => null,
            'school_mail_encryption' => config('mail.mailers.smtp_school.encryption'),

            'license_key' => config('system.license.license_key'),

            'student_default_groups' => config('system.active_directory.student_default_groups'),
            'employee_default_groups' => config('system.active_directory.employee_default_groups'),
            'guest_default_groups' => config('system.active_directory.guest_default_groups'),
            'guest_default_organization_unit' => config('system.active_directory.guest_default_organization_unit')

            ];

        return response()->json(['data' => $data], 200);
    }

    public function updateFortinet(SettingsFortinetUpdateRequest $request)
    {
        $this->authorize('update', Settings::class);

        $service = New SettingsFortinetUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function updateGsmGate(SettingsGsmGateUpdateRequest $request)
    {
        $this->authorize('update', Settings::class);

        $service = New SettingsGsmGateUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function updateMysql(SettingsMysqlUpdateRequest $request)
    {
        $this->authorize('update', Settings::class);

        $service = New SettingsMysqlUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function updateLicense(Request $request)
    {
        $this->authorize('update', Settings::class);

        $service = New SettingsLicenseUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function checkLicense(Request $request)
    {
        $this->authorize('update', Settings::class);

        $service = New LicenseCheckService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function updateActiveDirectory(SettingsActiveDirectoryUpdateRequest $request)
    {
        $this->authorize('update', Settings::class);

        $service = New SettingsActiveDirectoryUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function checkActiveDirectory(Request $request)
    {
        $this->authorize('update', Settings::class);

        $service = New ActiveDirectoryCheckService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    

    public function updateSchoolSmtp(SettingsSchoolSmtpUpdateRequest $request)
    {
        $this->authorize('update', Settings::class);
        
        $service = New SettingsSchoolSmtpUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function updateDefaultGroupsOus(SettingsDefaultGroupsOusUpdateRequest $request)
    {
        $this->authorize('update', Settings::class);
        
        $service = New SettingsDefaultGroupsOusUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }
}

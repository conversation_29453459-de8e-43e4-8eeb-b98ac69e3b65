<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\Move;

use App\Http\Requests\Move\MoveStoreRequest;
use App\Http\Requests\Move\MoveUpdateRequest;

use App\Http\Resources\Move\MoveResource;
use App\Http\Resources\Move\MoveDetailResource;
use App\Http\Resources\Move\MoveCollection;

use App\Services\Move\MoveUpdateService;
use App\Services\Move\MoveCreateService;
use App\Services\Move\MoveProcessService;
use App\Services\Move\MoveDeleteService;
use App\Services\Move\MovePdfService;

class MoveController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {       
        $this->authorize('read', Move::class);
        $loggedUser = auth()->user();

        if($loggedUser->can('property.master')) {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $search = $request->search;
            $state = $request->state;
            $type = $request->type;
            $userId = $request->user_id;
            $roomId = $request->room_id;
            
            $moves = Move::byState($state)
                ->byType($type)
                ->byUserId($userId)
                ->byRoomId($roomId)
                ->orderBy('id','desc')
                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);

        } else {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $search = $request->search;
            $state = $request->state;
            $roomId = $request->room_id;
                   
            $moves = Move::byState($state)
                ->byType($type)
                ->byUserId($loggedUser->id)
                ->byRoomId($roomId)
                ->orderBy('id','desc')
                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);
        } 

        $data = new MoveCollection($moves);
        return $data;
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(MoveStoreRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('store', Move::class);

        $service = New MoveCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Display the specified resource.
     */
    public function show(Move $move)
    {
        $this->authorize('view', $move);

        return new MoveDetailResource($move);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(MoveUpdateRequest $request, Move $move)
    {
        $this->authorize('update', $move);

        $service = New MoveUpdateService;
        $serviceData = $service->processPostRequest($request, $move);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function process(Move $move)
    {
        $this->authorize('process', $move);

        $service = New MoveProcessService;
        $serviceData = $service->processPostRequest($move);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Move $move)
    {
        $this->authorize('destroy', $move);

        $service = New MoveDeleteService;
        $serviceData = $service->processPostRequest($move);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
        
    }

    public function generatePdf(Move $move)
    {
        $this->authorize('view', $move);

        $service = new MovePdfService;
        $serviceData = $service->process($move);

        if($serviceData['success']){
            return $this->sendTempFile(fileData: $serviceData['file_data']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }
    }
}

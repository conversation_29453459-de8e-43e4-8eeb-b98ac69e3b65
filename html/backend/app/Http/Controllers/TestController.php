<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

use App\Models\User;
use App\Models\Group;
use App\Models\UserMacAddress;
use App\Models\OrganizationUnit;
use App\Models\License;

use Adldap;
use Adldap\Utilities;

use Illuminate\Support\Facades\Http;

use App\Jobs\SendSms;

use DB;
use Exception;
use Illuminate\Support\Facades\Crypt;
use Str;
use Illuminate\Support\Facades\Network;

use GuzzleHttp\Client;

use Carbon\Carbon;



use App\Services\File\GeneratePdfService;

class TestController extends Controller
{

    function customEncryptTimestamp($timestamp) {
        $timestamp = (int)$timestamp*13;
        $randomNumberString = mt_rand(100000, 999999);
        $saltedTimestamp = $randomNumberString.$timestamp;
        $base16Encoded = base_convert($saltedTimestamp, 10, 16);
        return $base16Encoded;
    }
    
    function customDecryptTimestamp($encryptedData) {
        $base16Decoded = base_convert($encryptedData, 16, 10);
        $remainingString = substr($base16Decoded, 6);
        $timestamp = (int)$remainingString/13;
        return $timestamp;
    }

    
    function encrypt($data, $key) {
        $ivLength = openssl_cipher_iv_length('aes-256-cbc');
        $iv = openssl_random_pseudo_bytes($ivLength);
        $encrypted = openssl_encrypt($data, 'aes-256-cbc', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    function decrypt($data, $key) {
        $data = base64_decode($data);
        $ivLength = openssl_cipher_iv_length('aes-256-cbc');
        $iv = substr($data, 0, $ivLength);
        $encryptedData = substr($data, $ivLength);
        return openssl_decrypt($encryptedData, 'aes-256-cbc', $key, 0, $iv);
    }

    
    public function test1(){
        /*
        $adGroup = Adldap::search()->groups()->where('distinguishedname', '=', "CN=Users,CN=Builtin,DC=skola,DC=local")->first();
        
        $dbChildGroup = Group::where('guid', $adGroup->getConvertedGuid())->first();
        $adGroupGroups = $adGroup->getMemberof();

        $oldFamilyGroupsIds = $dbChildGroup->parentGroups->pluck('id')->toArray();

        $newFamilyGroupsIds = Group::whereIn('distinguished_name', $adGroupGroups)->pluck('id')->toArray();

        $addToIds = array_diff($newFamilyGroupsIds, $oldFamilyGroupsIds);
        $removeToIds = array_diff($oldFamilyGroupsIds, $newFamilyGroupsIds);

        $dbChildGroup->parentGroups()->detach($removeToIds);
        $dbChildGroup->parentGroups()->attach($addToIds);
        */
    }

    public function test2(){

        $secretKey = "tvoje_tajne_heslo";
        $originalData = "Toto je tajná zpráva.";
        
        $encryptedData = $this->encrypt($originalData, $secretKey);
        echo "Zašifrovaná data: " . $encryptedData . "\n";
        
        $decryptedData = $this->decrypt($encryptedData, $secretKey)
        ;
        echo "Dešifrovaná data: " . $decryptedData . "\n";
    }

    public function test3(){


// PHP code to get the MAC address of Server
$MAC = exec('getmac');
  
// Storing 'getmac' value in $MAC
$MAC = strtok($MAC, ' ');
  
// Updating $MAC value using strtok function, 
// strtok is used to split the string into tokens
// split character of strtok is defined as a space
// because getmac returns transport name after
// MAC address   
echo "MAC address of Server is: $MAC";
    }

    public function test4(){

        //$user = Adldap::search()->users()->find('liliana.kohla');
        //return $user;

        $now = Carbon::now()->timestamp;
        $windowsTimestamp = Utilities::convertUnixTimeToWindowsTime($now);
        $adUser = Adldap::search()->where('samaccountname','kekel.test1')->first();
        $adUser->pwdlastset = 0;
        $adUser->save();

    }

    public function test5(){
        print_r($nigga);
    }

    public function getChildrens($parent = null){

        $dn = Utilities::explodeDn(config('ldap.connections.default.settings.base_dn'));

        if(is_null($parent)){
            $parent = $dn[0];
        } 

        $organizationUnits = OrganizationUnit::where(DB::raw('BINARY `parent`'), $parent)->withCount('users')->get();

        $childrens = array();

        foreach($organizationUnits as $organizationUnit){

            $data = [
                'id' => $organizationUnit->id,
                'name' => $organizationUnit->name,
                'users_count' => $organizationUnit->users_count,
                'childrens' => $this->getChildrens($organizationUnit->name)
            ];
            $childrens[] = $data;
        }

        return $childrens;

    }

    function replaceEnvVariable($key, $newValue)
{
    $envFile = base_path('.env');

    if (!file_exists($envFile)) {
        throw new Exception('.env soubor neexistuje.');
    }

    // Přečtení obsahu souboru .env
    $currentEnv = file_get_contents($envFile);

    // Hledání proměnné v souboru .env
    $pattern = "/^($key\s*=\s*)(.*)$/m";
    if (preg_match($pattern, $currentEnv, $matches)) {
        // Nahrazení hodnoty proměnné novou hodnotou
        $updatedEnv = preg_replace($pattern, "$key=$newValue", $currentEnv);

        // Uložení upraveného obsahu zpět do souboru .env
        file_put_contents($envFile, $updatedEnv);

        return true;
    }

    // Pokud proměnná nebyla nalezena, vrátíme false
    return false;
}

    public function testPost1(Request $request){
        $data = $request->getContent();
     
        $xml = new SimpleXMLElement($data);

        $data = [
            'account_name' => (string)$xml->{'User-Name'},
            'calling_station_id' =>  (string)$xml->{'Calling-Station-Id'},
            'acct_status_type' =>  (string)$xml->{'Acct-Status-Type'},
        ];        

        UserMacAddress::create($data);

        $response = [
            'success' => true,
            'data' => $data
        ];

        return response()->json($response, 200);
    }
   

}

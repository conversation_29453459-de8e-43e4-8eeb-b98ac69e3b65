<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\Item;

use App\Http\Requests\Item\ItemStoreRequest;
use App\Http\Requests\Item\ItemUpdateRequest;

use App\Http\Resources\Item\ItemResource;
use App\Http\Resources\Item\ItemDetailResource;
use App\Http\Resources\Item\ItemCollection;

use App\Services\Item\ItemUpdateService;
use App\Services\Item\ItemDiscardService;
use App\Services\Item\ItemCreateService;
use App\Services\Item\ItemBarcodeService;

class ItemController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {       
        $this->authorize('read', Item::class);
        $loggedUser = auth()->user();

        if($loggedUser->can('property.master')) {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $search = $request->search;
            $state = $request->state;
            $userId = $request->user_id;
            $roomId = $request->room_id;
            $accountingCategoryId = $request->accounting_category_id;
            
            $items = Item::search($search)
                ->byState($state)
                ->byUserId($userId)
                ->byRoomId($roomId)
                ->byAccountingCategoryId($accountingCategoryId)
                ->orderBy('id','desc')
                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);

        } else {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $search = $request->search;
            $state = $request->state;
            $roomId = $request->room_id;
            $accountingCategoryId = $request->accounting_category_id;
                   
            $items = Item::search($search)
                ->byState($state)
                ->byUserId($loggedUser->id)
                ->byRoomId($roomId)
                ->byAccountingCategoryId($accountingCategoryId)
                ->orderBy('id','desc')
                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);
        } 

        $data = new ItemCollection($items);  
        return $data;
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(ItemStoreRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('store', Item::class);

        $service = New ItemCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Display the specified resource.
     */
    public function show(Item $item)
    {
        $this->authorize('view', $item);

        return new ItemDetailResource($item);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ItemUpdateRequest $request, Item $item)
    {
        $this->authorize('update', $item);

        $service = New ItemUpdateService;
        $serviceData = $service->processPostRequest($request, $item);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function discard(Request $request, Item $item)
    {
        $this->authorize('update', $item);

        $service = New ItemDiscardService;
        $serviceData = $service->processPostRequest($request, $item);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Item $item)
    {
        $this->authorize('destroy', $item);

        if($item->state != "NOT_ASSIGNED"){
            return $this->sendError(error: "Přiřazenou nebo vyřazenou položku nelze smazat.", code: 400);
        }

        $item->delete();
        return $this->sendResponse(message: 'Položka smazána');
        
    }

    public function barcodes(Request $request)
    {
        $this->authorize('read', Item::class);

        $service = new ItemBarcodeService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendTempFile(fileData: $serviceData['file_data']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\AccountControlCode;

use App\Http\Resources\AccountControlCode\AccountControlCodeCollection;

class AccountControlCodeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $listing = $request->listing;

        $accountControlCodes = AccountControlCode::byListing($listing)
                        ->orderBy('code','asc')
                        ->get();

        $data = new AccountControlCodeCollection($accountControlCodes);
        return $data;
    }
}

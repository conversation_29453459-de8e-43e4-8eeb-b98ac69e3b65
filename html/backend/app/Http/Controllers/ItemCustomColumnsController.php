<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\ItemCustomColumn;

use App\Services\ItemCustomColumn\ItemCustomColumnUpdateService;

class ItemCustomColumnsController extends Controller
{
    public function index(Request $request){
        $dbItemsColumns = ItemCustomColumn::get();
        $dbItemsColumnsArray = [];

        for ($i = 1; $i <= 10; $i++) {
            $array[] = [
                'name' => 'custom_'.$i,
                'custom_name' => null,
                'enable' => 0
            ];
        }

        foreach($dbItemsColumns as $dbItemsColumn){
            $index = array_search($dbItemsColumn->name, array_column($array, 'name'));
            if ($index !== false) {
                $array[$index]['enable'] = $dbItemsColumn->enable;
                $array[$index]['custom_name'] = $dbItemsColumn->custom_name;
            }
        }


        return $this->sendResponse(result: $array);
        
    }

    public function updateCustomColumns(Request $request){

        $service = New ItemCustomColumnUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        } 
    }
}

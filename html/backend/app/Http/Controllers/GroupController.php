<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Jobs\AdGroupsSync;

use App\Models\Group;

use App\Http\Resources\Group\GroupCollection;
use App\Http\Resources\Group\GroupListCollection;
use App\Http\Resources\Group\GroupListResource;

use App\Http\Requests\Group\GroupStoreRequest;
use App\Http\Requests\Group\GroupUpdateRequest;
use App\Http\Requests\Group\GroupStoreUserRequest;
use App\Http\Requests\Group\GroupRemoveUsersRequest;

use App\Services\Group\GroupCreateService;
use App\Services\Group\GroupAddUserService;
use App\Services\Group\GroupUpdateService;
use App\Services\Group\GroupRemoveUsersService;

use Adldap;
use Adldap\Utilities;

class GroupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('read', Group::class);

        $perPage = (int)$request->perpage;
        $page = (int)$request->page;
        $search = $request->search;

        $groups = Group::search($search)
                        ->orderBy('id','asc')
                        ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);

        $data = new GroupListCollection($groups);
        return $data;
    }

    public function list(Request $request)
    {
        $this->authorize('read', Group::class);

        $perPage = (int)$request->perpage;
        $page = (int)$request->page;
        $search = $request->search;

        $groups = Group::search($search)
                        ->excludedName()
                        ->orderBy('id','asc')
                        ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);

        $data = new GroupListCollection($groups);
        return $data;
    }

    public function show(Group $group)
    {
        $this->authorize('read', $group);

        return new GroupListResource($group);
    }

    public function sync()
    {
        $this->authorize('sync', Group::class);
        AdGroupsSync::dispatch();
    }

    public function store(GroupStoreRequest $request)
    {

        $this->authorize('store', Group::class);

        $service = New GroupCreateService;
        $serviceData = $service->processPostRequest($request);

       if($serviceData['success']){
           return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
       } else {
           return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
       }  
    }

    public function storeUser(GroupStoreUserRequest $request, Group $group)
    {

        $this->authorize('store', Group::class);

        $service = New GroupAdduserService;
        $serviceData = $service->processPostRequest($request, $group);

       if($serviceData['success']){
           return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
       } else {
           return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
       }  
    }

    public function destroyUsers(GroupRemoveUsersRequest $request, Group $group)
    {

        $this->authorize('store', Group::class);

        $service = New GroupRemoveUsersService;
        $serviceData = $service->processPostRequest($request, $group);

       if($serviceData['success']){
           return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
       } else {
           return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
       }  
    }

    public function update(GroupUpdateRequest $request, Group $group)
    {
        $this->authorize('update', Group::class);

        $service = New GroupUpdateService;
        $serviceData = $service->processPostRequest($request, $group);
 
        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function destroy(Group $group)
    {
        $this->authorize('destroy', Group::class);

        //Najdeme dane OU ktere chceme smazat
        $ad = new Adldap();
        $adGroup = Adldap::search()->groups()->whereEquals('distinguishedname', $group->distinguished_name)->firstOrFail();
        

        try {
            // Pokud chcete zároveň zachytit i potenciální E_WARNING z ldap_* funkcí,
            // můžete sem doplnit set_error_handler(...) viz předchozí příklady.
            
            // Zkusíme smazat skupinu v AD.
            $adGroup->delete();
        
            // Pokud smazání proběhlo bez vyhození výjimky, smažeme i v DB.
            $group->delete();
        
            // Všechno dopadlo dobře -> vrátíme úspěšnou odpověď.
            return $this->sendResponse(message: 'Skupina odstraněna');
        
        } catch (\Adldap\Exceptions\AdldapException $e) {
            // Zachytí interní výjimky z Adldap2 (např. pokud se nepodaří smazat v AD).
            // Tady si klidně můžete zalogovat detail chyby: logger()->error($e->getMessage());
            return $this->sendError(error: 'Skupina nebyla odstraněna. Chyba AD (AdldapException).');
        
        } catch (\Exception $e) {
            // Zachytí ostatní obecné výjimky (včetně potenciální ErrorException, pokud použijete set_error_handler).
            // Také lze zalogovat detail: logger()->error($e->getMessage());
            return $this->sendError(error: 'Skupina nebyla odstraněna. Chyba AD (Exception).');
        }
    
    }
}

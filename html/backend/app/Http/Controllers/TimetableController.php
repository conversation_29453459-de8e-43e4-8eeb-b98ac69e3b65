<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\Timetable;

use App\Http\Resources\Timetable\TimetableCollection;

use App\Services\Timetable\TimetableUpdateService;


class TimetableController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->authorize('read', Timetable::class);
        
        $timetables = Timetable::orderBy('teaching_hour_number','asc')
                        ->get();

        $data = new TimetableCollection($timetables);
        return $data;
    }



    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {
        $this->authorize('update', Timetable::class);

        $service = New TimetableUpdateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        } 

    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Ticket;
use Illuminate\Http\Request;

use App\Http\Resources\Ticket\TicketCollection;
use App\Http\Resources\Ticket\TicketDetailResource;

use App\Services\Ticket\TicketCreateService;
use App\Services\Ticket\TicketUpdateService;
use App\Services\Ticket\TicketCloseService;
use App\Services\Ticket\TicketMultipleCloseService;

class TicketController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $loggedUser = auth()->user();
        $this->authorize('read', Ticket::class);

        if($loggedUser->can('tickets.master')) {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $category = $request->category_id;
            $state = $request->state_id;
            $user = $request->user_id;
            $search = $request->search;
            $date_from = $request->date_from;
            $date_to = $request->date_to;
            
            $tickets = Ticket::with(['state','category','user'])
                                ->search($search)
                                ->byDateRange($date_from, $date_to)
                                ->byUser($user)
                                ->byState($state)
                                ->byCategory($category)
                                ->orderBy('created_at','desc')
                                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);  
        } else {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $category = $request->category_id;
            $state = $request->state_id;
            $search = $request->search;
            $date_from = $request->date_from;
            $date_to = $request->date_to;
            
            $tickets = Ticket::with(['state','category','user'])
                                ->search($search)
                                ->byDateRange($date_from, $date_to)
                                ->byUser($loggedUser->id)
                                ->byState($state)
                                ->byCategory($category)
                                ->orderBy('created_at','desc')
                                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);  
        } 

        return new TicketCollection($tickets);
    }

    
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('store', Ticket::class);

        $service = New TicketCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }
    }


    public function close(Ticket $ticket)
    {
        $this->authorize('close', $ticket);

        $service = New TicketCloseService;
        $serviceData = $service->processPostRequest($ticket);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }   
    }

    public function multipleClose(Request $request)
    {

        $this->authorize('close', Ticket::class);

        $service = New TicketMultipleCloseService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }   
    }

    /**
     * Display the specified resource.
     */
    public function show(Ticket $ticket)
    {
        $this->authorize('view', $ticket);

        return new TicketDetailResource($ticket); 
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Ticket $ticket)
    {
        $this->authorize('update', $ticket);

        $service = New TicketUpdateService;
        $serviceData = $service->processPostRequest($ticket, $request);

        if($serviceData['success']){
            return $this->sendResponse(result: $serviceData['custom_data'], message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Ticket $ticket)
    {
        $this->authorize('destroy', $ticket);

        $ticket->delete();
        return $this->sendResponse(message: 'Ticket smazán');
    }
}

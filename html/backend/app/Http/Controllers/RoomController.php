<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\Room;

use App\Http\Requests\Room\RoomStoreRequest;
use App\Http\Requests\Room\RoomUpdateRequest;

use App\Http\Resources\Room\RoomResource;
use App\Http\Resources\Room\RoomDetailResource;
use App\Http\Resources\Room\RoomCollection;

use App\Services\Room\RoomUpdateService;
use App\Services\Room\RoomCreateService;
use App\Services\Room\RoomGeneratePdfService;

class RoomController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {       
        $this->authorize('read', Room::class);
        $loggedUser = auth()->user();

        if($loggedUser->can('property.master')) {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $search = $request->search;
            $userId = $request->user_id;
            
            $rooms = Room::search($search)
                ->byUserId($userId)
                ->orderBy('id','desc')
                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);

        } else {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $search = $request->search;
                   
            $rooms = Room::search($search)
                ->byUserId($loggedUser->id)
                ->orderBy('id','desc')
                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);
        } 


        $data = new RoomCollection($rooms);
        return $data;
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(RoomStoreRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('store', Room::class);

        $service = New RoomCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Display the specified resource.
     */
    public function show(Room $room)
    {
        $this->authorize('view', $room);

        return new RoomDetailResource($room);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(RoomUpdateRequest $request, Room $room)
    {
        $this->authorize('update', $room);

        $service = New RoomUpdateService;
        $serviceData = $service->processPostRequest($request, $room);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    public function generatePdf(Request $request)
    {
        $this->authorize('read', Room::class);

        $service = new RoomGeneratePdfService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendTempFile(fileData: $serviceData['file_data']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Room $room)
    {
        $this->authorize('destroy', $room);
        $room->delete();
        return $this->sendResponse(message: 'Místnost smazána');
        
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

use App\Http\Requests\Backup\DownloadRequest;

class BackupController extends Controller
{
    public function index()
    {
        $this->authorize('read', Backup::class);
        $backupDisk = Storage::disk('backups');
        $files = $backupDisk->files();

        $data = $this->filesName($files);
        return $this->sendResponse(result: $data, message: "");
    }

    public function download(DownloadRequest $request)
    {
        $this->authorize('download', Backup::class);

        $fileName = $request->input('name');

        $backupDisk = Storage::disk('backups');
        if($backupDisk->exists($fileName)) {
            $headers = [
                'File-Name' => urlencode($fileName),
            ];
    
            $fileData = [
                'path' => storage_path('/app/backups/'. $fileName),
                'file_name' => $fileName,
                'headers' => $headers
            ];
    
            return $this->sendFile($fileData);
        } else {
            return $this->sendError(error: "Záloha nenalezena", code: null, customCode: null);
        }



    }

    public function filesName($files){
        $data = [];
        foreach($files as $file){
            if($file != ".gitignore"){
                $data[] = [
                    'name' => $file
                ];
            }
        }
        return $data;
    }

}

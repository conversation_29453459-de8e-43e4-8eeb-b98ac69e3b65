<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Jobs\AdUsersSync;
use App\Jobs\AdGroupsSync;
use App\Jobs\AdOrganizationUnitsSync;

class ActiveDirectoryController extends Controller
{
    public function fullSync()
    {
        $this->authorize('sync', ActiveDirectory::class);

        AdOrganizationUnitsSync::dispatch();
        AdGroupsSync::dispatch();
        AdUsersSync::dispatch();
    }
}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\Building;

use App\Http\Requests\Building\BuildingStoreRequest;
use App\Http\Requests\Building\BuildingUpdateRequest;

use App\Http\Resources\Building\BuildingResource;
use App\Http\Resources\Building\BuildingCollection;

use App\Services\Building\BuildingUpdateService;
use App\Services\Building\BuildingCreateService;

class BuildingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {       
        $this->authorize('read', Building::class);
        $loggedUser = auth()->user();

        if($loggedUser->can('property.master')) {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $search = $request->search;
            
            $buildings = Building::search($search)
                ->orderBy('id','desc')
                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);

        } else {
            $perPage = (int)$request->perpage;
            $page = (int)$request->page;
            $search = $request->search;
                   
            $buildings = Building::search($search)
                ->orderBy('id','desc')
                ->paginate($perPage, $columns = ['*'], $pageName = 'page', $page);
        } 

        $data = new BuildingCollection($buildings);
        return $data;
    }


    /**
     * Store a newly created resource in storage.
     */
    public function store(BuildingStoreRequest $request)
    {

        $loggedUser = auth()->user();
        $this->authorize('store', Building::class);

        $service = New BuildingCreateService;
        $serviceData = $service->processPostRequest($request);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }


    /**
     * Update the specified resource in storage.
     */
    public function update(BuildingUpdateRequest $request, Building $building)
    {
        $this->authorize('update', $building);

        $service = New BuildingUpdateService;
        $serviceData = $service->processPostRequest($request, $building);

        if($serviceData['success']){
            return $this->sendResponse(message: $serviceData['message']);
        } else {
            return $this->sendError(error: $serviceData['message'], code: $serviceData['status_code']);
        }  
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Building $building)
    {
        $this->authorize('destroy', $building);
        $building->delete();
        return $this->sendResponse(message: 'Budova smazána');
        
    }
}

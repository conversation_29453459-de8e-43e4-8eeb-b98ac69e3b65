<?php
namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class VariablePasswordPolicy implements Rule
{
    protected string $messageText = 'Heslo nesplňuje p<PERSON>žadovaná kritéria.';

    public function passes($attribute, $value): bool
    {
        $min = config('system.active_directory.user_password_length', 8);
        $complexity = config('system.active_directory.user_password_complexity', false);

        $rule = Password::min($min);

        if ($complexity) {
            $rule->letters()->mixedCase()->numbers()->symbols();
        }

        $validator = Validator::make(
            [$attribute => $value],
            [$attribute => [$rule]]
        );

        if ($validator->fails()) {
            $this->messageText = $validator->errors()->first($attribute);
            return false;
        }

        return true;
    }

    public function message(): string
    {
        return $this->messageText;
    }
}
<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class ValidUsernameScheme implements Rule
{
    private array $validTokenPatterns = [
        '/^\{JMENO\}$/',
        '/^\{PRIJMENI\}$/',
        '/^\{PRVNI\.PISMENO\.JMENO\}$/',
        '/^\{PRVNI\.PISMENO\.PRIJMENI\}$/',
        '/^\{JMENO_\d+\}$/',
        '/^\{PRIJMENI_\d+\}$/',
    ];

    public function passes($attribute, $value): bool
    {
        if (empty($value)) {
            return false;
        }

        if (str_starts_with($value, '.') || str_ends_with($value, '.')) {
            return false;
        }

        preg_match_all('/\{[^{}]+\}/', $value, $matches);
        $foundTokens = $matches[0];

        if (empty($foundTokens)) {
            return false;
        }


        foreach ($foundTokens as $token) {
            if (!$this->isValidToken($token)) {
                return false;
            }
        }


        $valueStripped = $value;
        foreach ($foundTokens as $token) {
            $valueStripped = str_replace($token, '', $valueStripped);
        }


        return preg_match('/^[.]*$/', $valueStripped) === 1;
    }

    private function isValidToken(string $token): bool
    {
        foreach ($this->validTokenPatterns as $pattern) {
            if (preg_match($pattern, $token)) {
                return true;
            }
        }

        return false;
    }

    public function message(): string
    {
        return 'Šablona pro přihlašovací jméno není platná.';
    }
}
<?php namespace App\Services\System;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class ActiveDirectoryCheckService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'ldap_hosts'            =>  $request->input('ldap_hosts'),
            'ldap_port'             =>  (int)$request->input('ldap_port'),
            'ldap_base_dn'          =>  $request->input('ldap_base_dn'),
            'ldap_username'         =>  $request->input('ldap_username'),   
            'ldap_password'         =>  $request->input('ldap_password'),
            'ldap_account_prefix'   =>  $request->input('ldap_account_prefix'),
            'ldap_account_suffix'   =>  $request->input('ldap_account_suffix'),
            'ldap_use_ssl'          =>  (boolean)$request->input('ldap_use_ssl'),
            'ldap_use_tls'          =>  (boolean)$request->input('ldap_use_tls')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {
        // Construct new Adldap instance.
        $ad = new \Adldap\Adldap();

        // Create a configuration array.
        $config = [  
            'hosts'         => [$data['ldap_hosts']],
            'port'          => $data['ldap_port'],
            'base_dn'       => $data['ldap_base_dn'],
            'username'      => $data['ldap_username'],
            'password'      => $data['ldap_password'],
            'account_prefix'=> $data['ldap_account_prefix'],
            'account_suffix'=> $data['ldap_account_suffix'],
            'use_ssl'       => $data['ldap_use_ssl'],
            'use_tls'       => $data['ldap_use_tls'],
            'custom_options' => [
                // See: http://php.net/ldap_set_option
                LDAP_OPT_X_TLS_REQUIRE_CERT => LDAP_OPT_X_TLS_NEVER
            ]
        ];

        $ad->addProvider($config);

        try {
            // If a successful connection is made to your server, the provider will be returned.
            $provider = $ad->connect();

        } catch (\Adldap\Auth\BindException $e) {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Nepodařilo se připojit k AD.'
            ];     
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Připojení k AD bylo úspěšné.',
            'custom_data' => null 
        ]; 


    }
}

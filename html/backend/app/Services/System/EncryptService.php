<?php namespace App\Services\System;

class EncryptService {

    function customEncryptTimestamp($timestamp) {
        $timestamp = (int)$timestamp*13;
        $randomNumberString = mt_rand(100000, 999999);
        $saltedTimestamp = $randomNumberString.$timestamp;
        $base16Encoded = base_convert($saltedTimestamp, 10, 16);
        return $base16Encoded;
    }
    
    function customDecryptTimestamp($encryptedData) {
        $base16Decoded = base_convert($encryptedData, 16, 10);
        $remainingString = substr($base16Decoded, 6);
        $timestamp = (int)$remainingString/13;
        return $timestamp;
    }

    
    function encrypt($data) {
        $key = "4rICJ7SlrpGVNBsgWAcThxUz4yEiirt8";
        $ivLength = openssl_cipher_iv_length('aes-256-cbc');
        $iv = openssl_random_pseudo_bytes($ivLength);
        $encrypted = openssl_encrypt($data, 'aes-256-cbc', $key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    function decrypt($data) {
        $key = "4rICJ7SlrpGVNBsgWAcThxUz4yEiirt8";
        $data = base64_decode($data);
        $ivLength = openssl_cipher_iv_length('aes-256-cbc');
        $iv = substr($data, 0, $ivLength);
        $encryptedData = substr($data, $ivLength);
        return openssl_decrypt($encryptedData, 'aes-256-cbc', $key, 0, $iv);
    }
}
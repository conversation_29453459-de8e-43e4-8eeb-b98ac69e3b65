<?php namespace App\Services\Inventory;
use Illuminate\Http\Request;

use App\Models\Inventory;

class InventoryProcessService {
    public function processPostRequest(Inventory $inventory): Array {
        return $this->process($inventory);  
    }

    public function process(Inventory $inventory) : array {

        $uprocessedItems = $inventory->items()->where('inventory_items.state', 'UNPROCESSED')->count();
        if($uprocessedItems != 0){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Inventůru nelze uzavřít. Je nutné zadat stav všech položek na seznamu.'
            ];   
        }

        $notFoundItems = $inventory->items()->where('inventory_items.state', 'NOT_FOUND')->count();
        if($notFoundItems == 0){
            $inventory->state = 'SUCCESSFULLY_CLOSED';
            $message = 'Inventůra uzavřena';
        } else {

            $inventory->state = 'FAILED_CLOSED';
            $message = 'Inventůra uzavřena s chy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>';
        }

        $inventory->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => $message
        ]; 

    }
}

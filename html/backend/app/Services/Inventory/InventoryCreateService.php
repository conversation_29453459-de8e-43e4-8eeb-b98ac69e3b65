<?php namespace App\Services\Inventory;
use Illuminate\Http\Request;

use App\Models\Item;
use App\Models\Inventory;
use App\Models\InventoryItem;


class InventoryCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'room_id' =>  $request->input('room_id'),
            'user_id' =>  $request->input('user_id')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $data = [
            'room_id' => $data['room_id'],
            'user_id' => $data['user_id']
        ]; 

        $items = null;
        if(!is_null($data['user_id'])){
            $items = Item::where('user_id',$data['user_id'])->where('state','ASSIGNED_TO_USER')->get();
        } else{
            $items = Item::where('room_id',$data['room_id'])->where('state','ASSIGNED_TO_ROOM')->get();
        }

        $inventory = Inventory::create($data); 

        foreach($items as $item){
            InventoryItem::create(['inventory_id' => $inventory->id, 'item_id' => $item->id]);
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Ineventorní seznam vytvořen'
        ]; 

    }
}

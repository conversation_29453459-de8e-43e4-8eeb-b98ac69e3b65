<?php namespace App\Services\PermissionCategory;
use Illuminate\Http\Request;

use App\Models\PermissionCategory;


class PermissionCategoryCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'name' =>  $request->input('name'),
            'position' =>  $request->input('position')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $data = [
            'name' => $data['name'],
            'position' => $data['position']
        ];

        $role = PermissionCategory::create($data); 

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Kategorie byla vytvořena.'
        ]; 

    }
}

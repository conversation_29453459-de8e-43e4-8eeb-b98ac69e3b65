<?php namespace App\Services\PermissionCategory;
use Illuminate\Http\Request;

use App\Models\PermissionCategory;


class PermissionCategoryUpdateService {
    public function processPostRequest(Request $request, PermissionCategory $permissioncategory): Array {
        $data = [
            'name' =>  $request->input('name'),
            'position' =>  $request->input('position')
        ];

        return $this->process($data, $permissioncategory);  
    }

    public function process(Array $data, PermissionCategory $permissioncategory) : array {
        $permissioncategory->name = $data['name'];
        $permissioncategory->position = $data['position'];
        $permissioncategory->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Kategorie byla upravena.'
        ]; 

    }
}

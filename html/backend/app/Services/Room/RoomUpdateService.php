<?php namespace App\Services\Room;
use Illuminate\Http\Request;

use App\Models\Room;


class RoomUpdateService {
    public function processPostRequest(Request $request, Room $room): Array {
        $data = [
            'name' =>  $request->input('name'),
            'code' =>  $request->input('code'),
            'user_id' =>  $request->input('user_id'),
            'building_id' =>  $request->input('building_id')
        ];

        return $this->process($data, $room);  
    }

    public function process(Array $data, Room $room) : array {
        $room->name = $data['name'];
        $room->code = $data['code'];
        $room->user_id = $data['user_id'];
        $room->building_id = $data['building_id'];
        $room->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Místnost byla upravena.'
        ]; 

    }
}

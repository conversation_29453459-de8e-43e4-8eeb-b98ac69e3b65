<?php namespace App\Services\Room;
use Illuminate\Http\Request;

use App\Models\Room;


class RoomCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'name' =>  $request->input('name'),
            'code' =>  $request->input('code'),
            'user_id' =>  $request->input('user_id'),
            'building_id' =>  $request->input('building_id'),
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $data = [
            'name' => $data['name'],
            'code' => $data['code'],
            'user_id' => $data['user_id'],
            'building_id' => $data['building_id']
        ];

        $room = Room::create($data); 

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Místnost byla vytvořena.'
        ]; 

    }
}

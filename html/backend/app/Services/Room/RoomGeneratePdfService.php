<?php namespace App\Services\Room;


use App\Models\Room;
use App\Services\File\GeneratePdfService;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;

class RoomGeneratePdfService {
    public function processPostRequest(Request $request): Array {
        
        $roomsId =  $request->input('rooms');

        return $this->process($roomsId);  
    }

    public function process(Array $roomsId) : Array{     

        $service = New GeneratePdfService;
        $data['rooms'] = [];

        foreach($roomsId as $roomId){
            $room = null;
            $room = Room::where('id', $roomId)->first();
            if(!is_null($room)){
                $data['rooms'][] = $room->toArray();
            }
        }

        $data['responsible_user'] = Auth::user();
        $data['now'] = Carbon::now();

        $filePath = $service->process('pdf.room.inventory_list', $data);

        $fileName = "Inventář místnosti";
        $headers = [
            'File-Name' => urlencode($fileName)
        ];

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Dokument vytvořen',
            'custom_data' => null,
            'file_data' => [
                'path' => storage_path('/app/temp/'. $filePath),
                'file_name' => $fileName,
                'headers' => $headers
            ]
        ]; 

    }
}

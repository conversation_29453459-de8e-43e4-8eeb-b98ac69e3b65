<?php namespace App\Services\InventoryItem;
use Illuminate\Http\Request;

use App\Models\InventoryItem;
use App\Models\Inventory;

class InventoryItemUpdateService {
    public function processPostRequest(Request $request, InventoryItem $inventoryItem): Array {
        $data = [
            'state' =>  $request->input('state')
        ];

        return $this->process($data, $inventoryItem);  
    }

    public function process(Array $data, InventoryItem $inventoryItem) : array {
        $inventory = $inventoryItem->inventory;
        if($inventory->state != 'OPEN'){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Tato inventůra již byla uzavřena.'
            ];   
        }

        $inventoryItem->state = $data['state'];
        $inventoryItem->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Stav položky uložen'
        ]; 

    }
}

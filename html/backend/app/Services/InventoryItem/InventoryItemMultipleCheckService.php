<?php namespace App\Services\InventoryItem;
use Illuminate\Http\Request;

use App\Models\InventoryItem;
use App\Models\Inventory;
use App\Models\Item;

class InventoryItemMultipleCheckService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'inventory_id' =>  $request->input('inventory_id'),
            'evidence_numbers' =>  $request->input('evidence_numbers')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $inventory = Inventory::find($data['inventory_id']);
        if($inventory->state == 'PROCESSED'){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Tato inventura byla zpracována. Nelze dělat úpravy.'
            ];   
        }

        $failedItems = [];
        $stringItems = explode(";", $data['evidence_numbers']);

        $errors = [];
        $items = [];

        foreach($stringItems as $stringItem){
            if($stringItem != ""){
                $item = Item::where('evidence_number',$stringItem)->first();
                if($item != null){
                    $items[] = [
                       'id' => $item->id,
                       'name' => $item->name,
                       'evidence_number' => $item->evidence_number,
                       'state' => $item->state,
                       'errors' => $this->checkErrors($inventory, $item)
                    ];  
                } else {
                    $errors[] = [
                        'message' => 'Položka s evidenčním číslem: '.$stringItem.' nebyla nalezena.'
                    ]; 
                } 
            }
             
        }
        
        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Zkontrolujte prosím načtené položky.',
            'custom_data' => [
                'items' => $items,
                'errors' => $errors
            ]
        ]; 
        

    }

    public function checkErrors($inventory, $item){
        $error = null;

        if(!is_null($inventory->user_id)){
            //Invetura uzivatele
            if(($item->state == 'NOT_ASSIGNED') or ($item->state == 'LOCKED')){
                $error = "Tato položka není přiřazena na žádného uživatele ani místnost.";   
            } 

            if($item->state == 'ASSIGNED_TO_ROOM'){
                $roomName = $item->room->name;
                $error = "Tato položka je přiřazena k místnosti: ".$roomName.".";    
            } 

            if($item->state == 'ASSIGNED_TO_USER'){
                if($inventory->user_id != $item->user_id){
                    $userName = $item->user->first_name." ".$item->user->last_name;
                    $error = "Tato položka je přiřazena k osobě: ".$userName."."; 
                } 
            } 

            if($item->state == 'DISCARDED'){
                $error = "Tato položka je vyřazena.";   
            } 

        } else {
            //Invetura mistnosti
            if(($item->state == 'NOT_ASSIGNED') or ($item->state == 'LOCKED')){
                $error = "Tato položka není přiřazena na žádného uživatele ani místnost.";   
            } 

            if($item->state == 'ASSIGNED_TO_ROOM'){
                if($inventory->room_id != $item->room_id){
                    $roomName = $item->room->name;
                    $error = "Tato položka je přiřazena k místnosti: ".$roomName.".";  
                }  
            } 

            if($item->state == 'ASSIGNED_TO_USER'){
                $userName = $item->user->first_name." ".$item->user->last_name;
                $error = "Tato položka je přiřazena k osobě: ".$userName."."; 
            } 

            if($item->state == 'DISCARDED'){
                $error = "Tato položka je vyřazena.";   
            } 
        }
        
        return $error;
    }
}

<?php namespace App\Services\InventoryItem;
use Illuminate\Http\Request;

use App\Models\InventoryItem;
use App\Models\Inventory;
use App\Models\Item;

class InventoryItemMultipleUpdateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'inventory_id' =>  $request->input('inventory_id'),
            'items' =>  $request->input('items')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $inventory = Inventory::find($data['inventory_id']);
        if($inventory->state == 'PROCESSED'){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Tato inventura byla zpracována. Nelze dělat úpravy.'
            ];   
        }

        $errors = [];
        $items = $data['items'];
        $dbInventoryItems = [];

        //Tady to vsechno jeste jednou checku protoze mezi checkem a insertem se muze neco zmenit ze
        foreach($items as $item){
            $dbInventoryItem = InventoryItem::where('item_id',$item)->where('inventory_id',$data['inventory_id'])->first();  
            if($dbInventoryItem != null){
                $dbInventoryItems[] = $dbInventoryItem;  
            } else {
                $errors[] = "Item : '".$item."' nelze potvrdit."; 
            }  
        }

        //Pokud check v klidu tak to prote zalozim
        if(count($errors) == 0){
            foreach($dbInventoryItems as $databaseInventoryItem){
                $databaseInventoryItem->state = 'CONFIRMED';
                $databaseInventoryItem->save(); 
            }
        } else {
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Položky se nepodařilo potvrdit.',
                'custom_data' => [
                    'errors' => $errors
                ]
            ]; 
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Položky potvrzeny.'
        ]; 
    }
}

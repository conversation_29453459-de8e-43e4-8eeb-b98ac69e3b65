<?php namespace App\Services\User;
use Illuminate\Http\Request;
use Hash;

use App\Models\User;

use Illuminate\Support\Facades\Mail;
use App\Mail\User\UserUpdatePasswordMail;

class UserPasswordUpdateService {
    public function processPostRequest(Request $request, User $user): Array {
        $data = [
           'password' =>  $request->input('password')
        ];

        $sendPassword = $request->input('send_password');

        return $this->process($data, $user, $sendPassword);  
    }

    public function process(Array $data, User $user, Bool $sendPassword) : array {
        $rawPassword = null;

        if($data['password'] == null){
            $data['password'] = $this->generatePassword();
            $sendPassword = true;
        }

        if($sendPassword){
            $rawPassword = $data['password'];
        }

        $user->password = Hash::make($data['password']);
        $user->save();

        Mail::to($user->email)->queue(new UserUpdatePasswordMail($user, $rawPassword));

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Heslo bylo nastaveno.'
        ]; 
    }

    public function generatePassword($lengh = 8): string{
        $password = '';
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        for ($i = 0; $i < $lengh; $i++) {
            $password .= $characters[rand(0, $charactersLength - 1)];
        }
        return $password;
    }
}

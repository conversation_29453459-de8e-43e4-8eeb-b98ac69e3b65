<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;

class UserPermissionUpdateService {
    public function processPostRequest(Request $request, User $user): Array {
        $data = [
           'permissions' =>  $request->input('permissions')
        ];

        return $this->process($data, $user);  
    }

    public function process(Array $data, User $user) : array {

        $user->syncPermissions($data['permissions']);

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Oprávnění uloženo.'
        ]; 
    }
}

<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;

use App\Jobs\AdUserOuDataUpdate;

use Auth;

class AdUserMultipleOuUpdateService {
    public function processPostRequest(Request $request): Array {

        $users = $request->input('users');
        $data = [
            'organization_unit' => $request->input('organization_unit'),
        ];
        return $this->process($users, $data);  
    }

    public function process(Array $users, Array $data) : array {
        $userTopPriority =  Auth::user()->getTopRolePriority();
        foreach($users as $user){
            $dbUser = User::findOrFail($user);
            $targetUserTopPriority = $dbUser->getTopRolePriority();

            if($userTopPriority < $targetUserTopPriority){
                if($dbUser->active_directory == 1){
                    AdUserOuDataUpdate::dispatch($dbUser->id, $dbUser->account_name, $data['organization_unit'])->onQueue('medium'); 
                }    
            }
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny byly zad<PERSON>y.',
            'custom_data' => null
        ]; 
    }     
}

<?php namespace App\Services\User;
use Illuminate\Http\Request;
use Hash;

use App\Models\User;
use Adldap;

class AdUserPasswordUpdateService {
    public function processPostRequest(Request $request, User $user): Array {
        $data = [
           'password' =>  $request->input('password'),
           'must_change_password' =>  $request->input('must_change_password'),
        ];

        $sendSms = $request->input('send_sms');
        $showPassword = $request->input('show_password');

        return $this->process($data, $user, $sendSms, $showPassword);  
    }

    public function process(Array $data, User $user, Bool $sendSms = false, Bool $showPassword = false) : array {
        $rawPassword = null;

        if($data['password'] == null){
            $data['password'] = $this->generatePassword();
            $rawPassword = $data['password'];
            $showPassword = true;
        }

        if($showPassword){
            $rawPassword = $data['password'];
        }

        if($sendSms){
            $rawPassword = $data['password'];
            if(is_null($user->phone)){
                return [
                    'success' => false,
                    'status_code' => null,
                    'message' => 'Heslo nebylo změněno, uživatel nemá nastavené telefonní číslo.'
                ]; 
            }
        }

        $adUser = Adldap::search()->where('samaccountname',$user->account_name)->first();
        $adUser->setPassword($rawPassword);

        if(1 == $data['must_change_password']){
            $adUser->pwdlastset = 0;
        } else {
            $adUser->pwdlastset = -1;
        }

        $adUser->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Heslo bylo nastaveno.',
            'custom_data' => [
                'password' => ($showPassword) ? $rawPassword : null
            ]
        ]; 
    }
    // ^(?=.*?[a-z])(?=.*?[A-Z])(?=.*?[0-9])(?=.*?[#$%^&@*()\-_+={}[\]|\:;"'<>,.?\/]).{8,14}$
    public function generatePassword($lengh = 8): string{
        $password = '';
        $characters = '**********abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        for ($i = 0; $i < $lengh; $i++) {
            $password .= $characters[rand(0, $charactersLength - 1)];
        }
        $characters = '**********';
        $charactersLength = strlen($characters);
        for ($i = 0; $i < 2; $i++) {
            $password .= $characters[rand(0, $charactersLength - 1)];
        }
        return $password.'@';
    }
}

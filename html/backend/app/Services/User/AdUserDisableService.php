<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;

use App\Jobs\AdUserDisable;

use Auth;

class AdUserDisableService {
    public function processPostRequest(Request $request): Array {

        $users = $request->input('users');
        return $this->process($users);  
    }

    public function process(Array $users) : array {
        $userTopPriority =  Auth::user()->getTopRolePriority();

        foreach($users as $user){
            $dbUser = User::findOrFail($user);
            $targetUserTopPriority = $dbUser->getTopRolePriority();

            if($userTopPriority < $targetUserTopPriority){
                //if($dbUser->getEditable() and $dbUser->active_directory == 1){
                if($dbUser->active_directory == 1){
                    AdUserDisable::dispatch($dbUser->account_name)->onQueue('medium'); 
                }
            }
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Zakázán<PERSON> ú<PERSON> bylo zadáno.',
            'custom_data' => null
        ]; 
    }     
}

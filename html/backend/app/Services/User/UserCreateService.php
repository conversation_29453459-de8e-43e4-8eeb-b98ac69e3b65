<?php namespace App\Services\User;
use Illuminate\Http\Request;
use Hash;

use App\Models\User;
use Carbon\Carbon;

use App\Services\Auth\SendVerifyEmailService;

use Illuminate\Support\Facades\Mail;
use App\Mail\User\UserCreateMail;
use App\Services\User\UserRoleUpdateService;

class UserCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'first_name' =>  $request->input('first_name'),
            'middle_name' =>  $request->input('middle_name'),
            'last_name' =>  $request->input('last_name'),
            'degree_before' =>  $request->input('degree_before'),
            'degree_after' =>  $request->input('degree_after'),
            'password' =>  $request->input('password'),
            'email' =>  $request->input('email'),
            'roles' => $request->input('roles')
        ];

        $emailIsVerified = $request->input('email_verified');
        $showPassword = $request->input('show_password');

        return $this->process($data, $emailIsVerified, $showPassword);  
    }

    public function process(Array $data, Bool $emailIsVerified, Bool $showPassword) : array {
        $token = null;
        $rawPassword = null;

        if($data['password'] == null){
            $data['password'] = $this->generatePassword();
            $showPassword = true;
        }

        if($showPassword){
            $rawPassword = $data['password'];
        }

        $data['password'] = Hash::make($data['password']);


        if($emailIsVerified){
            $data['email_verified_at'] = Carbon::now();
            $user = User::create($data);
        } else {
            $user = User::create($data);

            $tokenService = New SendVerifyEmailService();
            $tokenData = $tokenService->process($data, false);
            $token = $tokenData['custom_data']['token'];
        }

        //updatneme k uzivateli role
        $roleService = New UserRoleUpdateService;
        $roleService->process(data: $data, user: $user);

        
        //Mail::to($user->email)->queue(new UserCreateMail($user,$token, $rawPassword));
        Mail::to($user->email)->queue(new UserCreateMail($user, $token, $rawPassword));

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Uživatel byl vytvořen.',
            'custom_data' => [
                'user' => $user
            ]
        ]; 

    }

    public function generatePassword($lengh = 8): string{
        $password = '';
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        for ($i = 0; $i < $lengh; $i++) {
            $password .= $characters[rand(0, $charactersLength - 1)];
        }
        return $password;
    }
}

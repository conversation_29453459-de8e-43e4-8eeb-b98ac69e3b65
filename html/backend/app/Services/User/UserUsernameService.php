<?php namespace App\Services\User;
use Illuminate\Http\Request;
use Hash;

use App\Models\User;


class UserUsernameService {
    public function process(string $firstName, string $lastName) : array {
        $scheme = config('system.active_directory.scheme'); 
        $username = null;
        $username = $this->generateUserName($scheme, $firstName, $lastName);

        $usernameServiceData = $this->buildUnique($username);

        if($usernameServiceData['success']){
            return [
                'success' => true,
                'status_code' => 200,
                'message' => 'Uživatelské jméno bylo vygenerováno.',
                'custom_data' => [
                    'username' => $usernameServiceData['custom_data']['username'],
                    'helper' => $usernameServiceData['custom_data']['helper']
                ]
            ]; 
        } else {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Chyba generování uživatelského jména.'
            ]; 
        }
    }

    public function buildUnique(string $username, int $helper = 0) : array {
        $rawUsername = $username;
        if($helper != 0){
            $username = $username.$helper;
        } 

        if($helper == 20){
            die;
        }

        $existUsername = User::where('account_name', $username)->exists();
        if($existUsername){
            $helper = $helper + 1;
            return $this->buildUnique($rawUsername, $helper);

        } else {
            return [
                'success' => true,
                'status_code' => 200,
                'message' => 'Uživatelské jméno bylo vygenerováno.',
                'custom_data' => [
                    'username' => $username,
                    'helper' => $helper
                ]
            ]; 
        }
    }

    // Funkce generuje uživatelské jméno dle předaného schématu
    function generateUserName($scheme, $firstName, $lastName)
    {
        // Odstraníme diakritiku a vše převedeme na malé písmena
        if(isset($firstName) AND $firstName != '')
        {
            $firstName = strtolower(removeDiacritics($firstName));
        }
 
        // Odstraníme diakritiku a vše převedeme na malé písmena
        if(isset($lastName) AND $lastName != '')
        {
            $lastName = strtolower(removeDiacritics($lastName));
        }
 
        // Dle zkrácenin jména uděláme omezení na počet
        for($firstName_i = 1; $firstName_i < 20; $firstName_i++)
        {
            $scheme = preg_replace('/{JMENO_'.$firstName_i.'}/', substr($firstName, 0, $firstName_i), $scheme); // {JMENO_N}
        }
 
        // Dle zkrácenin příjmení uděláme omezení na počet
        for($lastName_i = 1; $lastName_i < 20; $lastName_i++)
        {
            $scheme = preg_replace('/{PRIJMENI_'.$lastName_i.'}/', substr($lastName, 0, $lastName_i), $scheme); // {PRIJMENI_N}
        }
 
        // Převedeme jméno do vzoru
        $scheme = preg_replace('/{JMENO}/', $firstName, $scheme);            // {JMENO}
        $scheme = preg_replace('/{PRIJMENI}/', $lastName, $scheme);      // {PRIJMENI}
        $scheme = preg_replace('/{PRVNI.PISMENO.JMENO}/', $firstName[0], $scheme);      // {PRVNI.PISMENO.JMENO}
        $scheme = preg_replace('/{PRVNI.PISMENO.PRIJMENI}/', $lastName[0], $scheme);      // {PRVNI.PISMENO.PRIJMENI}
 
        return $scheme;
    }
}

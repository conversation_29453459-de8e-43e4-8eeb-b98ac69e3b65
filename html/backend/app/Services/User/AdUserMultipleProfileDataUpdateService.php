<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;

use App\Jobs\AdUserProfileDataUpdate;

use Auth;

class AdUserMultipleProfileDataUpdateService {
    public function processPostRequest(Request $request): Array {

        $users = $request->input('users');
        $data = [
            'profile_path' => $request->input('profile_path'),
            'script_path' => $request->input('script_path'),
            'home_directory' => $request->input('home_directory'),
            'home_drive' => $request->input('home_drive'),
            'profile_path_username' => $request->input('profile_path_username'),
            'home_directory_username' => $request->input('home_directory_username'),
        ];
        return $this->process($users, $data);  
    }

    public function process(Array $users, Array $data) : array {
        $userTopPriority =  Auth::user()->getTopRolePriority();
        foreach($users as $user){
            $dbUser = User::findOrFail($user);
            $targetUserTopPriority = $dbUser->getTopRolePriority();

            if($userTopPriority < $targetUserTopPriority){
                if($dbUser->active_directory == 1){
                //if($dbUser->getEditable() and $dbUser->active_directory == 1){
                    if($data['profile_path_username'] == 1){
                        $profilPath = $data['profile_path']."/".$dbUser->account_name;
                    } else {
                        $profilPath = $data['profile_path'];
                    }

                    if($data['home_directory_username'] == 1){
                        $homeDirectory = $data['home_directory']."/".$dbUser->account_name;
                    } else {
                        $homeDirectory = $data['home_directory'];
                    }

                    AdUserProfileDataUpdate::dispatch($dbUser->id, $dbUser->account_name, $profilPath, $data['script_path'], $homeDirectory, $data['home_drive'])->onQueue('medium'); 
                }    
            }
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny byly zadány.',
            'custom_data' => null
        ]; 
    }     
}

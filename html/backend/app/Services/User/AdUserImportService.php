<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;
use App\Models\OrganizationUnit;
use App\Models\AccountControlCode;

use Rap2hpoutre\FastExcel\FastExcel;
use Excel;

use App\Services\User\AdUserCreateService;
use App\Imports\UsersBakalariImport;
use App\Imports\UsersSkolaonlineImport;

class AdUserImportService {
    public function processPostRequest(Request $request): Array {

        $file = $request->file('file');
        $data = [
            'type'                          => $request->input('type'),
            'organization_unit'             => $request->input('organization_unit'),
            'ignore_organization_unit'      => $request->input('ignore_organization_unit')
        ];
        return $this->process($data, $file);  
    }

    public function process(Array $data, $file) : array {
        $code = AccountControlCode::where('code', 512)->first();
        if(is_null($code)){
            return [
                'success' => false,
                'status_code' => null,
                'custom_code' => null,
                'message' => 'Import se nezdařil. Nenalezen account control code.'
            ]; 
        }

        $userCreateService = New AdUserCreateService;
        $importedUsersArray = [];
        
        //Prebuildim si pole do naseho formatu first_name, last_name, organization_unit    
        //skola online asi posila defaultne Jméno, Příjmení, Třída
        if($data['type'] == "skolaonline"){
            $users = Excel::toArray(new UsersSkolaonlineImport, $file);

            foreach($users[0] as $user){         
                $importedUsersArray[] = [
                    'first_name' => $user['jmeno'],
                    'last_name' => $user['prijmeni'],
                    'organization_unit' => $user['trida']
                ];
            }
        }
        
        //skola online asi posila defaultne Jméno, Příjmení, Třída
        elseif($data['type'] == "bakalari"){
            $users = Excel::toArray(new UsersBakalariImport, $file);
            foreach($users[0] as $user){  
                $importedUsersArray[] = [
                    'first_name' => $user['jmeno'],
                    'last_name' => $user['prijmeni'],
                    'organization_unit' => $user['trida']
                ];
            }
        } 
        


        // kdyz je neznamy typ importu
        else {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Neznámý typ importu.',
                'custom_code' => null,
                'custom_data' => null
            ]; 
        }
 

        /*
            kdyz mam ignorovat ou tak kouknu ktere tridy
            jsou v souboru a overim namapovani
        */ 
        if($data['ignore_organization_unit'] == 1){
            $checkMappingData = $this->checkMapping($data['type'], $importedUsersArray);
            if($checkMappingData['success']){
                $importedUsersArray = $checkMappingData['custom_data']['new_data'];
                $organizationUnitIDs = $checkMappingData['custom_data']['organization_units']; 
                /*
                    Tady musim overit jestli nevzniknou duplicity jmena v danych OUckach
                */

                $checkNameDuplicityData = $this->checkNameDuplicity($organizationUnitIDs, $importedUsersArray);
                if($checkNameDuplicityData['success']){
                    foreach($importedUsersArray as $importedUser){
                        $data = $this->convertToInputArray($importedUser, $importedUser['organization_unit'], $code->id);
                        $serviceData = $userCreateService->process($data, false, false);
                    }

                } else {
                    return [
                        'success' => false,
                        'status_code' => null,
                        'custom_code' => 42201,
                        'message' => 'Duplicitní záznamy.',
                        'custom_data' => [
                            'duplicity_users' =>  $checkNameDuplicityData['custom_data']['duplicity_users']
                        ]
                    ];
                }


            } else {
                return [
                    'success' => false,
                    'status_code' => null,
                    'custom_code' => 42202,
                    'message' => 'Špatné mapování tříd na organizační jednotky.',
                    'custom_data' => [
                        'failed_classes' =>  $checkMappingData['custom_data']['failed_classes']
                    ]
                ];
            }
        } 
        /*
            Kdyz neignoruju selektlou OU tak importuju do selktle OU,
            takze overim, jestli by nevznikly duplicity jmen pro danou OU
        */       

        else {
            // overim duplicitu jen pro danou OU
            $nameDuplicityData = $this->checkNameDuplicityForOu($data['organization_unit'], $importedUsersArray);    
            if($nameDuplicityData['success']){
                foreach($importedUsersArray as $importedUser){
                    $data = $this->convertToInputArray($importedUser, $data['organization_unit'], $code->id);
                    $serviceData = $userCreateService->process($data, false, false);
                }
            } else {
                return [
                    'success' => false,
                    'status_code' => null,
                    'custom_code' => 42201,
                    'message' => 'Duplicitní záznamy.',
                    'custom_data' => [
                        'duplicity_users' =>  $nameDuplicityData['custom_data']['duplicity_users']
                    ]
                ];
            }
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Import dokončen.',
            'custom_data' => null
        ]; 
    }  
    
    public function checkMapping(String $type, Array $importedUsers){
        $failedClasses = [];
        $seenClasses = [];
        $seenClassesDbId = [];
        $mappingData = [];
        $mappingColumn = "";

        //nastavíme jméno sloupce podle typu importu
        switch ($type) {
            case "skolaonline":
                $mappingColumn = 'map_skola_online';
                break;
            case "bakalari":
                $mappingColumn = 'map_bakalari';
                break;
        }

        //Zacnu prochazet vsechny uzivatele
        $i = 0;
        foreach($importedUsers as $user){

            // overim jestli trida od uzivatele uz byla nactena,
            // pokud ano tak nic, pokud ne tak nacteme
            if(!in_array($user['organization_unit'], $seenClasses)){
                // zapisu si ze uz jsem ji videl a loadnul 
                $seenClasses[] = $user['organization_unit'];
                // zkusim najit v DB namapovane OUcko
                $organizationUnit = OrganizationUnit::where($mappingColumn, $user['organization_unit'])->first();
                if(is_null($organizationUnit)){
                    // pridam do mapovani ale s null hodnotou
                    $mappingData[$user['organization_unit']] = null; 
                    // nebyla nalezena v DB takze je i spatne namapovana
                    $failedClasses[] = $user['organization_unit'];
                } else{
                    // pridam do mapovani, kdy klic je nazev tridy v souboru a hodnota je ID OU z databaze
                    $mappingData[$user['organization_unit']] = $organizationUnit->id; 
                    $seenClassesDbId[] = $organizationUnit->id;;
                }
            }

            // tady uz si jen vytahnu ID oucka z mapovaci tabulky a nastavim u uzivatele
            // do old si poznacim i nazev tridy ze souboru - kvuli name validaci
            $importedUsers[$i]['organization_unit'] = $mappingData[$user['organization_unit']];
            $importedUsers[$i]['organization_unit_old'] = $user['organization_unit'];
            $i++;
        }

        if(count($failedClasses) == 0){
            return [
                'success' => true,
                'status_code' => 200,
                'message' => 'Mapovaní v pořádku.',
                'custom_data' => [
                    'new_data' => $importedUsers,
                    'organization_units' => $seenClassesDbId,
                ]
            ]; 
        } else {
            return [
                'success' => false,
                'status_code' => null,
                'custom_code' => null,
                'message' => 'Špatné mapování.',
                'custom_data' => [
                    'failed_classes' =>  $failedClasses  
                ]
            ];  
        }
    }

    public function checkNameDuplicity(Array $organizationUnits, Array $importedUsers){
        $dbOrganizationUnitUsers = User::select('first_name','last_name','organization_unit')->whereIn('organization_unit', $organizationUnits)->get()->toArray();  
        
        $seenUsers = [];
        $failedUsers = [];
    
        foreach ($dbOrganizationUnitUsers as $dbUser){
            $seenUsers[] = $dbUser['first_name']."-".$dbUser['last_name']."-".$dbUser['organization_unit'];
        }

        foreach ($importedUsers as $importedUser){
            $user = $importedUser['first_name']."-".$importedUser['last_name']."-".$importedUser['organization_unit'];
            if(in_array($user, $seenUsers)){
                $failedUsers[] = $importedUser;
            } else {
                $seenUsers[] = $user;
            }
        }

        if(count($failedUsers) == 0){
            return [
                'success' => true,
                'status_code' => 200,
                'message' => 'Oveření bylo úspeěné.',
                'custom_data' => null
            ]; 
        } else {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Duplicitní záznam.',
                'custom_data' => [
                    'duplicity_users' =>  $failedUsers  
                ]
            ];  
        }
    }

    public function checkNameDuplicityForOu(Int $organizationUnit, Array $importedUsers){
            // vytahnu si vsechny existujici uzivatele OU
            // a udelam pole JMENO,PRIJMENI
            $dbOrganizationUnitUsers = User::select('first_name','last_name')->where('organization_unit', $organizationUnit)->get()->toArray();

            $seenUsers = [];
            $failedUsers = [];
        
            foreach ($dbOrganizationUnitUsers as $dbUser){
                $seenUsers[] = $dbUser['first_name']."-".$dbUser['last_name'];
            }

            foreach ($importedUsers as $importedUser){
                $user = $importedUser['first_name']."-".$importedUser['last_name'];
                if(in_array($user, $seenUsers)){
                    $failedUsers[] = $importedUser;
                } else {
                    $seenUsers[] = $user;
                }
            }

            if(count($failedUsers) == 0){
                return [
                    'success' => true,
                    'status_code' => 200,
                    'message' => 'Oveření bylo úspeěné.',
                    'custom_data' => null
                ]; 
            } else {
                return [
                    'success' => false,
                    'status_code' => null,
                    'message' => 'Duplicitní záznam.',
                    'custom_data' => [
                        'duplicity_users' =>  $failedUsers  
                    ]
                ];  
            }
    }

    public function convertToInputArray(Array $userData, Int $organizationUnit, Int $accountControlCodeId){
        $data = [
            'first_name' =>  $userData['first_name'],
            'middle_name' =>  null,
            'last_name' =>  $userData['last_name'],
            'email' =>  null,
            'phone' =>  null,
            'password' =>  null,
            'organization_unit' => $organizationUnit,
            'expire' =>  null,
            'visitor'  =>  0,
            'account_control_code_id'  => $accountControlCodeId,
            'profile_path' => null,
            'script_path' => null,
            'home_directory' => null,
            'home_drive' => null,
            //'roles' => $request->input('roles')
        ];

        return $data;
    }
}

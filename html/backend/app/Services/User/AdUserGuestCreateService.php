<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;
use App\Models\Group;
use App\Models\OrganizationUnit;
use App\Models\AccountControlCode;

use Adldap;
use Carbon\Carbon;

class AdUserGuestCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'first_name' =>  $request->input('first_name'),
            'last_name' =>  $request->input('last_name'),
            'email' =>  $request->input('email'),
            'phone' =>  $request->input('phone'),
            'password' =>  $request->input('password'),
            'expire' =>  $request->input('expire')
        ];


        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $accountControlCode = AccountControlCode::where('code',512)->firstOrfail();

        $data['account_control_code_id'] = $accountControlCode->id;
        $data['groups'] = explode(",", config('system.active_directory.guest_default_groups'));
        $data['visitor'] = 1;
        $data['organization_unit'] = config('system.active_directory.guest_default_organization_unit');

        if($data['organization_unit'] == "" or $data['organization_unit'] == null){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Není zvolena výchozí organizační jednotka pro účet hosta.'
            ]; 
        }

        $service = New AdUserCreateService;
        $serviceData = $service->process($data, false, true);

        return $serviceData;

    }
}

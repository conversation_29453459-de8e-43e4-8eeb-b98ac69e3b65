<?php namespace App\Services\User;


use App\Models\User;
use App\Services\File\GeneratePdfService;
use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;

class UserGeneratePropertyPdfService {
    public function processPostRequest(Request $request): Array {
        
        $userId =  $request->input('user_id');

        return $this->process($userId);  
    }

    public function process(Int $userId) : Array{     

        $service = New GeneratePdfService;

        $user = User::where('id', $userId)->first();

        $data['user'] = $this->convertData($user);
        $data['responsible_user'] = Auth::user();
        $data['now'] = Carbon::now();

        $filePath = $service->process('pdf.user.inventory_list', $data);

        $fileName = "Seznam položek uživatele";
        $headers = [
            'File-Name' => urlencode($fileName)
        ];

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Dokument vytvořen',
            'custom_data' => null,
            'file_data' => [
                'path' => storage_path('/app/temp/'. $filePath),
                'file_name' => $fileName,
                'headers' => $headers
            ]
        ]; 

    }

    public function convertData($user){
        $data = [];

        $data = [
            'first_name' => $user->first_name,
            'middle_name' => $user->middle_name,
            'last_name' => $user->last_name,
            'degree_before' => $user->degree_before,
            'degree_after' => $user->degree_after,
            'items' => $user->items->toArray()
        ];

        return $data;
    }
}

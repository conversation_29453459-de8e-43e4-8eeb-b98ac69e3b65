<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;
use App\Services\User\AdUserDeleteService;

class UserDeleteService {
    public function processPostRequest(User $user): Array {

        return $this->process($user);  
    }

    public function process(User $user) : array {

        if($user->active_directory){
            $service = New AdUserDeleteService;
            $serviceData = $service->processPostRequest($user);       
            if($serviceData['success']){
                $user->delete();
                return [
                    'success' => true,
                    'status_code' => 200,
                    'message' => 'Uživatel odebrán',
                    'custom_data' => null
                ]; 
            } else {
                return [
                    'success' => false,
                    'status_code' => null,
                    'message' => 'Uživatel nebyl odebrán.',
                ]; 
            }  
        }

        $user->delete();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Uživatel odebrán',
            'custom_data' => null
        ]; 

    }    

}

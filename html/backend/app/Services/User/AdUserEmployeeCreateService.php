<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;
use App\Models\Group;
use App\Models\OrganizationUnit;
use App\Models\AccountControlCode;

use Adldap;
use Carbon\Carbon;

class AdUserEmployeeCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'first_name' =>  $request->input('first_name'),
            'last_name' =>  $request->input('last_name'),
            'email' =>  $request->input('email'),
            'phone' =>  $request->input('phone'),
            'password' =>  $request->input('password'),
            'organization_unit' =>  $request->input('organization_unit'),
            'groups' =>  $request->input('groups'),
        ];


        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $accountControlCode = AccountControlCode::where('code',512)->firstOrfail();

        $data['account_control_code_id'] = $accountControlCode->id;
        $data['visitor'] = 0;

        $defaultGroups = explode(",", config('system.active_directory.employee_default_groups'));
        $mergetUniquegroups = array_unique(array_merge($defaultGroups, $data['groups']));
        $data['groups'] = $mergetUniquegroups;

        $service = New AdUserCreateService;
        $serviceData = $service->process($data, false, true);

        return $serviceData;

    }
}

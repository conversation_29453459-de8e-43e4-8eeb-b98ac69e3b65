<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;

use Adldap;

class AdUserDeleteService {
    public function processPostRequest(User $user): Array {
        return $this->process($user);  
    }

    public function process(User $user) : array {
        $adUser = Adldap::search()->where('samaccountname',$user->account_name)->firstOrFail();

        if ($adUser->delete()) {
            return [
                'success' => true,
                'status_code' => 200,
                'message' => 'Uživatel byl smazán.',
                'custom_data' => null
            ]; 
        } else {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Nezdařilo se smazat uživatele. Chyba AD.'
            ]; 
        }
    }    
}

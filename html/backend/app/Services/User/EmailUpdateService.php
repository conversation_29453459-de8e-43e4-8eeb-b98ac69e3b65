<?php namespace App\Services\User;
use Illuminate\Http\Request;
use Hash;

use App\Models\User;

class EmailUpdateService {
    public function processPostRequest(Request $request, User $user): Array {
        $data = [
           'password' =>  $request->input('password'),
           'new_email' =>  $request->input('new_email'),
        ];

        return $this->process($data, $user);  
    }

    public function process(Array $data, User $user) : array {
        if (Hash::check($data['password'], $user->password))
        {

            $user->email = $data['new_email'];
            $user->save();

            return [
                'success' => true,
                'status_code' => 200,
                'message' => 'Email byl změněn.'
            ]; 

        } else {
            return [
                'success' => false,
                'status_code' => null,
                'message' => '<PERSON><PERSON> zadáno š<PERSON>tn<PERSON> he<PERSON>lo.'
            ]; 
        }


    }
}

<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;

class ProfilUpdateService {
    public function processPostRequest(Request $request, User $user): Array {
        $data = [
            'first_name' => $request->input('first_name'),
            'middle_name' => $request->input('middle_name'),
            'last_name' => $request->input('last_name'),
            'degree_before' => $request->input('degree_before'),
            'degree_after' => $request->input('degree_after')
        ];

        return $this->process($data, $user);  
    }

    public function process(Array $data, User $user) : array {

        $user->first_name = $data['first_name'];
        $user->middle_name = $data['middle_name'];
        $user->last_name = $data['last_name'];
        $user->degree_before = $data['degree_before'];
        $user->degree_after = $data['degree_after'];

        $user->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny byly uloženy.',
            'custom_data' => [
                'user' => $user
            ]
        ]; 
    }
}

<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;
use Carbon\Carbon;

use Adldap;
use Adldap\Utilities;
use App\Models\OrganizationUnit;
use App\Models\Group;
use App\Models\AccountControlCode;

use App\Services\User\UserUsernameService;

class UserUpdateService {
    public function processPostRequest(Request $request, User $user): Array {
        $data = [
            'first_name' => $request->input('first_name'),
            'middle_name' => $request->input('middle_name'),
            'last_name' => $request->input('last_name'),
            'degree_before' => $request->input('degree_before'),
            'degree_after' => $request->input('degree_after'),
            'email' => $request->input('email'),
            'phone' => $request->input('phone'),
            'email_verified' => $request->input('email_verified'),
            'expire' => $request->input('expire'),
            'organization_unit' => $request->input('organization_unit'),
            'groups' => $request->input('groups'),
            'visitor'  =>  $request->input('visitor'),
            //'account_control_code_id'  =>  $request->input('account_control_code_id'),
            'profile_path' => $request->input('profile_path'),
            'script_path' => $request->input('script_path'),
            'home_directory' => $request->input('home_directory'),
            'home_drive' => $request->input('home_drive')
        ];

        return $this->process($data, $user);  
    }

    public function process(Array $data, User $user) : array {

        if($user->active_directory){

            if($user->first_name != $data['first_name'] or $user->last_name != $data['last_name']){
                $service = New UserUsernameService;
                $generatedUsernameData = $service->process($data['first_name'], $data['last_name']);

                if($generatedUsernameData['success']){
                    $generatedUsername = $generatedUsernameData['custom_data']['username'];
                }else{
                    return [
                        'success' => false,
                        'status_code' => null,
                        'message' => 'Změny nebyly uloženy, chyba generování uživatelského jména.'
                    ]; 
                }
            } else {
                $generatedUsername = $user->account_name;
            }


            $adUser = Adldap::search()->where('samaccountname',$user->account_name)->first();

            $adUser->setFirstName($data['first_name']);
            $adUser->setLastName($data['last_name']);
            $adUser->setEmail($data['email']);
            $adUser->setTelephoneNumber($data['phone']);
            $adUser->setDisplayName($data['last_name']." ".$data['first_name']);
            $adUser->setAccountName($generatedUsername);
            $adUser->setUserPrincipalName($generatedUsername . config('ldap.connections.default.settings.account_suffix'));
            $adUser->setAccountExpiry( (isset($data['expire'])) ? Carbon::create($data['expire'])->timestamp : null);
            /* $adUser->setUserAccountControl($this->getAccControlCode($data['account_control_code_id'])); */
            $adUser->setProfilePath($data['profile_path']);
            $adUser->setScriptPath($data['script_path']);
            $adUser->setHomeDirectory($data['home_directory']);
            $adUser->setHomeDrive($data['home_drive']);
            

            $result = $adUser->save();
            
            if($user->first_name != $data['first_name'] or $user->last_name != $data['last_name']){
                $newRdn = 'cn='.$data['last_name'].' '.$data['first_name'];
                $adUser->rename($newRdn);
            }     

            if(isset($data['organization_unit'])){
                if($data['organization_unit'] != $user->organization_unit){
                    $organizationUnit = OrganizationUnit::where('id', $data['organization_unit'])->first();
                    $newOrganizationUnitDn = $organizationUnit->distinguished_name;
    
                    if($adUser->move($newOrganizationUnitDn, true)){
                        $user->organization_unit = $data['organization_unit'];
                    }
                }
            }
            /*
            Zmenime Groups v AD
            */
            $dbUserGroupsIds = $user->groups->pluck('id')->toArray();
            //Pridat do skupin:
            $addToIds = array_diff($data['groups'], $dbUserGroupsIds);
            foreach($addToIds as $id){
                $dbGroup = Group::where('id', $id)->first();
                $adGroup = Adldap::search()->groups()->where('distinguishedname', '=', $dbGroup->distinguished_name)->first();

                if(!is_null($dbGroup)){
                    if($adUser->inGroup($dbGroup->distinguished_name)){
                    } else {
                        $adGroup->addMember($user->distinguished_name);   
                    }
                }

            }
            //Odebrat ze skupin:
            $removeFromIds = array_diff($dbUserGroupsIds, $data['groups']);
            foreach($removeFromIds as $id){
                $dbGroup = Group::where('id', $id)->whereNotIn('name', [
                    "Builtin",
                    "Computers",
                    "Domain Controllers",
                    "ForeignSecurityPrincipals",
                    "Keys",
                    "LostAndFound",
                    "Managed Service Accounts",
                    "Program Data",
                    "System",
                    "NTDS Quotas",
                    "TPM Devices"
                ])->first();
                $adGroup = Adldap::search()->groups()->where('distinguishedname', '=', $dbGroup->distinguished_name)->first();

                if(!is_null($dbGroup)){
                    if($adUser->inGroup($dbGroup->distinguished_name)){
                        $adGroup->removeMember($user->distinguished_name);
                    }
                } 
                
            }



            
  
            if($result){
                $user->account_name = $generatedUsername;
                $user->expire = (isset($data['expire'])) ? Carbon::create($data['expire'])->timestamp : null;
                $user->groups()->sync($data['groups']);
            } else {
                return [
                    'success' => false,
                    'status_code' => null,
                    'message' => 'Změny nebyly uloženy, neočekávaná chyba.',
                    'custom_data' => null
                ]; 
            }  
        }

        if($user->active_directory){
            $user->first_name = $data['first_name'];
            $user->middle_name = $data['middle_name'];
            $user->last_name = $data['last_name'];
            $user->degree_before = $data['degree_before'];
            $user->degree_after = $data['degree_after'];
            $user->email = $data['email'];
            $user->phone = $data['phone'];
            $user->visitor = $data['visitor'];
            $user->expire = (isset($data['expire'])) ? Carbon::create($data['expire']) : null;
            //$user->account_control_code = $data['account_control_code_id'];
            $user->profile_path = $data['profile_path'];
            $user->script_path = $data['script_path'];
            $user->home_directory = $data['home_directory'];
            $user->home_drive = $data['home_drive'];
        } else {
            $user->first_name = $data['first_name'];
            $user->middle_name = $data['middle_name'];
            $user->last_name = $data['last_name'];
            $user->degree_before = $data['degree_before'];
            $user->degree_after = $data['degree_after'];
            $user->email = $data['email'];
            $user->phone = $data['phone'];
        }

        if($data['email_verified']){
            if($user->email_verified_at == Null){
                $user->email_verified_at = Carbon::now();
            }
        } else {
            $user->email_verified_at = Null;
        }

        $user->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny byly uloženy.',
            'custom_data' => [
                'user' => $user
            ]
        ]; 
    }

    public function getAccControlCode($id){
        $accountControlCode = AccountControlCode::findOrfail($id);
        return $accountControlCode->code;
    }

}

<?php namespace App\Services\User;
use Illuminate\Http\Request;
use Hash;

use App\Models\User;

class PasswordUpdateService {
    public function processPostRequest(Request $request, User $user): Array {
        $data = [
           'password' =>  $request->input('password'),
           'new_password' =>  $request->input('new_password')
        ];

        return $this->process($data, $user);  
    }

    public function process(Array $data, User $user) : array {
        if (Hash::check($data['password'], $user->password))
        {

            $user->password = Hash::make($data['new_password']);
            $user->save();

            return [
                'success' => true,
                'status_code' => 200,
                'message' => 'Heslo bylo změněno.'
            ]; 

        } else {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'By<PERSON> zadáno špatné heslo.'
            ]; 
        }


    }
}

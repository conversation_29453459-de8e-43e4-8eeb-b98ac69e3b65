<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;
use App\Jobs\DeleteUser;
use Auth;

class UserMultipleDeleteService {
    public function processPostRequest(Request $request): Array {      
        $users = $request->input('users');
        return $this->process($users);  
    }

    public function process(Array $users) : array {

        $userTopPriority =  Auth::user()->getTopRolePriority();
        foreach($users as $user){
            $dbUser = User::findOrFail($user);
            $targetUserTopPriority = $dbUser->getTopRolePriority();

            if($userTopPriority < $targetUserTopPriority){             
                DeleteUser::dispatch($dbUser)->onQueue('low'); 
            }
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => '<PERSON><PERSON><PERSON><PERSON> ú<PERSON>tů bylo zadáno do systému.',
            'custom_data' => null
        ]; 

    }    

}

<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;

use App\Jobs\AdUserGroupDataUpdate;

use Auth;

class AdUserMultipleGroupUpdateService {
    public function processPostRequest(Request $request): Array {

        $users = $request->input('users');
        $data = [
            'groups' => $request->input('groups'),
        ];
        return $this->process($users, $data);  
    }

    public function process(Array $users, Array $data) : array {
        $userTopPriority =  Auth::user()->getTopRolePriority();
        foreach($users as $user){
            $dbUser = User::findOrFail($user);
            $targetUserTopPriority = $dbUser->getTopRolePriority();

            if($userTopPriority < $targetUserTopPriority){
                if($dbUser->active_directory == 1){
                    AdUserGroupDataUpdate::dispatch($dbUser->id, $dbUser->account_name, $data['groups'])->onQueue('medium'); 
                }    
            }
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny byly zadány.',
            'custom_data' => null
        ]; 
    }     
}

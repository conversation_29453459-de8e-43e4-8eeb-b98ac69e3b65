<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;

use App\Http\Resources\User\UserCollection;

class UserCheckPhoneService {
    public function processPostRequest(Request $request): Array {

        $users = $request->input('users');

        return $this->process($users);  
    }

    public function process(Array $users) : array {
        $failed = false;
        $failedData = [];

        $usersWithoutPhone = User::whereIn('id', $users)->whereNull('phone')->get();
        $usersCount = $usersWithoutPhone->count();

        if($usersCount != 0){
            $failed = true;
            $failedData = new UserCollection($usersWithoutPhone);
        }


        if($failed){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Některý z uživatelů nemá nastavené tel.číslo.',
                'custom_data' => $failedData
            ]; 
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Kontrola tel.čísel proběhla úspěšně.',
            'custom_data' => null
        ]; 

    }    

}

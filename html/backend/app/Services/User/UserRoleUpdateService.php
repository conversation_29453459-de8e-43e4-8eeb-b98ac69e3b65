<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;

class UserRoleUpdateService {
    public function processPostRequest(Request $request, User $user): Array {
        $data = [
           'roles' =>  $request->input('roles')
        ];

        return $this->process($data, $user);  
    }

    public function process(Array $data, User $user) : array {

        $user->syncRoles($data['roles']);

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Role uloženy.'
        ]; 
    }
}

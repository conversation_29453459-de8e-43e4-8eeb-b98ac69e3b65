<?php namespace App\Services\User;
use Illuminate\Http\Request;

use App\Models\User;

use App\Jobs\AdUserEnable;
use Auth;

class AdUserEnableService {
    public function processPostRequest(Request $request): Array {

        $users = $request->input('users');
        return $this->process($users);  
    }

    public function process(Array $users) : array {
        $userTopPriority =  Auth::user()->getTopRolePriority();

        foreach($users as $user){
            $dbUser = User::findOrFail($user);
            $targetUserTopPriority = $dbUser->getTopRolePriority();

            if($userTopPriority < $targetUserTopPriority){
            //if($dbUser->getEditable() and $dbUser->active_directory == 1){
                if($dbUser->active_directory == 1){
                    AdUserEnable::dispatch($dbUser->account_name)->onQueue('medium'); 
                }   
            } 
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Povolení účtů bylo zadáno.',
            'custom_data' => null
        ]; 
    }     
}

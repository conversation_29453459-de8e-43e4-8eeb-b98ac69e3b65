<?php namespace App\Services\Timetable;
use Illuminate\Http\Request;

use App\Models\Timetable;

use Illuminate\Support\Arr;

class TimetableUpdateService {
    public function processPostRequest(Request $request): Array {

        $timetables = $request->timetables;
        $timetables = json_decode($timetables,true);

        return $this->process($timetables);  
    }

    public function process(Array $timetables) : array {

        //Array of IDs from API
        $timetablesIds = Arr::pluck($timetables, 'id');

        //Array of Hour Number
        $hourNumbers = Arr::pluck($timetables, 'teaching_hour_number');
        if(count(array_unique($hourNumbers)) != count($hourNumbers)){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Čísla vyučovacích hodin nejsou unikátní',
                'custom_data' => null
            ]; 
        }

        //Check rows for delete
        $dbTimetables = Timetable::all();
        foreach($dbTimetables as $dbTimetable){
            if(!in_array($dbTimetable->id, $timetablesIds)){
                $dbTimetable->delete();
            }
        }

        //Create or update rows
        foreach($timetables as $timetable){
            Timetable::updateOrCreate(
                ['id' => $timetable['id']],
                $timetable
            );
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny uloženy.',
            'custom_data' => null
        ]; 

    }
}

<?php namespace App\Services\Item;
use Illuminate\Http\Request;

use App\Models\Item;


class ItemUpdateService {
    public function processPostRequest(Request $request, Item $item): Array {
        $data = [
            'name' =>  $request->input('name'),
            'invoice_number' =>  $request->input('invoice_number'),
            'buyed_at' =>  $request->input('buyed_at'),
            'price' =>  $request->input('price'),
            'purchased_from' => $request->input('purchased_from'),
            'description' => $request->input('description'),
            'accounting_category_id' => $request->input('accounting_category_id'),
            'custom_1' => $request->input('custom_1'),
            'custom_2' => $request->input('custom_2'),
            'custom_3' => $request->input('custom_3'),
            'custom_4' => $request->input('custom_4'),
            'custom_5' => $request->input('custom_5'),
            'custom_6' => $request->input('custom_6'),
            'custom_7' => $request->input('custom_7'),
            'custom_8' => $request->input('custom_8'),
            'custom_9' => $request->input('custom_9'),
            'custom_10' => $request->input('custom_10'),
        ];

        return $this->process($data, $item);  
    }

    public function process(Array $data, Item $item) : array {
        $item->accounting_category_id = $data['accounting_category_id'];
        $item->name = $data['name'];
        $item->invoice_number = $data['invoice_number'];
        $item->buyed_at = $data['buyed_at'];
        $item->price = $data['price'];
        $item->purchased_from = $data['purchased_from'];
        $item->description = $data['description'];
        $item->custom_1 = $data['custom_1'];
        $item->custom_2 = $data['custom_2'];
        $item->custom_3 = $data['custom_3'];
        $item->custom_4 = $data['custom_4'];
        $item->custom_5 = $data['custom_5'];
        $item->custom_6 = $data['custom_6'];
        $item->custom_7 = $data['custom_7'];
        $item->custom_8 = $data['custom_8'];
        $item->custom_9 = $data['custom_9'];
        $item->custom_10 = $data['custom_10'];
        $item->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Položka upravena.'
        ]; 

    }
}

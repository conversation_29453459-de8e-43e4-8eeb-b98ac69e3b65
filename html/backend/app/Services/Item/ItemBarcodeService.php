<?php namespace App\Services\Item;

use Illuminate\Http\Request;

use \Mpdf\Mpdf as PDF; 
use \Mpdf\QrCode\QrCode;
use \Mpdf\QrCode\Output;
use Illuminate\Support\Facades\Storage;

use App\Models\Item;
use App\Services\File\GeneratePdfService;

class ItemBarcodeService {


    public function processPostRequest(Request $request) : Array {
        $itemsId = $request->input('items');

        return $this->process($itemsId); 
    }

    public function process(Array $itemsId) : Array {
        $items = [];

        foreach($itemsId as $item){
            $dbItem = Item::find($item);
            if(!is_null($dbItem)){
                $items[] = $dbItem;
            }
        }
        $html = $this->getHtml($items);

        $filePath = generateRandomString(32).'.pdf';
        $fileName = "Evidenční čísla.pdf";

        // Create the mPDF document
        $document = new PDF( [
            'mode' => 'utf-8',
            'default_font' => 'frutiger',
            'format' => [75, 38]
        ]);     
 
        // Set some header informations for output
        $header = [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="'.$fileName.'"'
        ];
 
        // Write some simple Content
        $document->WriteHTML($html);

         
        // Save PDF on your public storage 
        Storage::disk('temp')->put($filePath, $document->Output($filePath, "S"));

        $headers = [
            'File-Name' => urlencode($fileName)
        ];

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Štítky vytvořeny',
            'custom_data' => null,
            'file_data' => [
                'path' => storage_path('/app/temp/'. $filePath),
                'file_name' => $fileName,
                'headers' => $headers
            ]
        ]; 


    }

    public function getHtml($items){
        $html = '<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <style>
        *{font-family: sans-serif; margin: 0px; padding: 0px;}
        @page { margin: 10px 10px 0 10px; }
        .text{font-size: 16px; line-height: 16px; overflow: hidden; height: 40px; margin-bottom: 4px; width: 150px;float: left; }
        .evidencenumber{font-size: 22px; line-height: 22px;}
        .name{font-size: 16px; line-height: 16px;}
        .code{text-align: center; display: block; font-size: 15px; padding: 5px 0; float: right; width: 105px;}
        </style>';
        
        foreach($items as $item){
            $shortName = \Illuminate\Support\Str::limit($item->name, 50);

            $qrCode = new QrCode($item->evidence_number);
            $output = new Output\Svg();
            $qrCodeSvg = $output->output($qrCode, 100, 'white', 'black');
            $qrCodeSvg = str_replace('<?xml version="1.0"?>', '', $qrCodeSvg);


            $itemHtml = '<div class="item">
                    <div class="text"> 
                    <strong><span class="evidencenumber">'.$item->evidence_number.'<span></strong><br />
                    <hr /><strong><span class="name">
                    '.$shortName.'
                    </span></strong></div>
                    <div class="code">
                        '.$qrCodeSvg.'
                    </div>
                </div>';
            $html =  $html.$itemHtml;
        }
        

        return $html;
    }
}

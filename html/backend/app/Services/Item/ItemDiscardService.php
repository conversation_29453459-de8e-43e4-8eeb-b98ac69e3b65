<?php namespace App\Services\Item;
use Illuminate\Http\Request;

use App\Models\Item;
use Carbon\Carbon;

class ItemDiscardService {
    public function processPostRequest(Request $request, Item $item): Array {

        return $this->process($item);  
    }

    public function process(Item $item) : array {

        if($item->state == "DISCARDED"){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Tato položka je již vyřazena.'
            ]; 
        } 

        if($item->state != "NOT_ASSIGNED"){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => '<PERSON>to polož<PERSON> ne<PERSON> v<PERSON>, je přiřazená uživateli nebo místnosti.'
            ]; 
        } 

        $item->state = 'DISCARDED';
        $item->discarded_at = Carbon::now();
        $item->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Položka vyřazena.'
        ]; 

    }
}

<?php namespace App\Services\Item;
use Illuminate\Http\Request;

use App\Models\Item;


class ItemCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'name' =>  $request->input('name'),
            'invoice_number' =>  $request->input('invoice_number'),
            'buyed_at' =>  $request->input('buyed_at'),
            'price' =>  $request->input('price'),
            'purchased_from' => $request->input('purchased_from'),
            'description' => $request->input('description'),
            'accounting_category_id' => $request->input('accounting_category_id'),
            'custom_1' => $request->input('custom_1'),
            'custom_2' => $request->input('custom_2'),
            'custom_3' => $request->input('custom_3'),
            'custom_4' => $request->input('custom_4'),
            'custom_5' => $request->input('custom_5'),
            'custom_6' => $request->input('custom_6'),
            'custom_7' => $request->input('custom_7'),
            'custom_8' => $request->input('custom_8'),
            'custom_9' => $request->input('custom_9'),
            'custom_10' => $request->input('custom_10'),
        ];

        $count = (Int)$request->input('count');

        return $this->process($data, $count);  
    }

    public function process(Array $data, Int $count) : array {

        $data = [
            'name' => $data['name'],
            'invoice_number' => $data['invoice_number'],
            'buyed_at' => $data['buyed_at'],
            'price' => $data['price'],
            'purchased_from' => $data['purchased_from'],
            'description' => $data['description'],
            'accounting_category_id' => $data['accounting_category_id'],
            'custom_1' => $data['custom_1'],
            'custom_2' => $data['custom_2'],
            'custom_3' => $data['custom_3'],
            'custom_4' => $data['custom_4'],
            'custom_5' => $data['custom_5'],
            'custom_6' => $data['custom_6'],
            'custom_7' => $data['custom_7'],
            'custom_8' => $data['custom_8'],
            'custom_9' => $data['custom_9'],
            'custom_10' => $data['custom_10']
        ];

        for ($i = 1; $i <= $count; $i++) {
            $data['evidence_number'] = $this->generateEvidenceNumber();
            $item = Item::create($data); 
        }

        

        $message = ($count > 1) ? 'Položky byly vytvořena.' : 'Položka byla vytvořena.';

        return [
            'success' => true,
            'status_code' => 200,
            'message' => $message 
        ]; 

    }

    public function generateEvidenceNumber(){
        $lastEvidenceNumber = 0;
        $lastItem = Item::orderBy('evidence_number', 'DESC')->first();
        if(is_null($lastItem)){
            return $evidenceNumber = 100001;
        } else {    
            $lastEvidenceNumber = $lastItem->evidence_number;
            return $evidenceNumber = $lastEvidenceNumber+1;
        }
    }
}

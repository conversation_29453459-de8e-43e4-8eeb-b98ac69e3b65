<?php namespace App\Services\File;
use Rap2hpoutre\FastExcel\FastExcel;


class GenerateTempXmlService {
    public function __construct(){}

    public function process($data, $name = null){
        if($name == null){
            $name = generateRandomString(32);
            $name = $name.'.xlsx';
        } else {
            $name = $name.'.xlsx';
        }

        $file = (new FastExcel($data))->export($name);
        return $name;
    }

}
<?php namespace App\Services\File;

use \Mpdf\Mpdf as PDF; 
use Illuminate\Support\Facades\Storage;

class GeneratePdfService {
    public function __construct(){}

    public function process($view, $data, $path = null , $name = null, $type = null){
        if($name == null){
            $name = generateRandomString(32);
            $name = $path.$name.'.pdf';
        } else {
            $name = $path.$name.'.pdf';
        }

        // Create the mPDF document
        $document = new PDF( [
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_header' => '3',
            'margin_top' => '20',
            'margin_bottom' => '20',
            'margin_footer' => '2',
        ]);     
 
        // Set some header informations for output
        $header = [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="'.$name.'"'
        ];
 
        // Write some simple Content
        $document->WriteHTML(view($view, ['data' => $data]));

         
        // Save PDF on your public storage 
        Storage::disk('temp')->put($name, $document->Output($name, "S"));

        return $name;
    }

}
<?php namespace App\Services\Group;
use Illuminate\Http\Request;

use App\Models\Group;
use App\Models\User;

use Adldap;

class GroupRemoveUsersService {
    public function processPostRequest(Request $request, Group $group): Array {
        $data = [
            'users' =>  $request->input('users'),
        ];

        return $this->process($data, $group);  
    }

    public function process(Array $data, Group $group) : array {
        $adGroup = Adldap::search()->groups()->where('distinguishedname', '=', $group->distinguished_name)->first();

        foreach($data['users'] as $user){
            $dbUser = User::where('id',$user)->first();
            $adUser = Adldap::search()->where('samaccountname',$dbUser->account_name)->first();  
            
            if($adUser->inGroup($group->distinguished_name)){
                $adGroup->removeMember($dbUser->distinguished_name); 
                $dbUser->groups()->detach($group->id);
            } else {     
                $dbUser->groups()->detach($group->id);
            }
        }

        if(count($data['users']) > 1){
            $message = "Uživatelé odebráni."; 
        } else {
            $message = "Uživatel odebrán";
        }
        
        return [
            'success' => true,
            'status_code' => 200,
            'message' => $message,
            'custom_data' => null
        ]; 

    }    

}

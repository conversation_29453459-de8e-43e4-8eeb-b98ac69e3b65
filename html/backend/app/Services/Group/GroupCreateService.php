<?php namespace App\Services\Group;
use Illuminate\Http\Request;

use App\Models\Group;
use App\Models\OrganizationUnit;

use Adldap;

class GroupCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'organization_unit_id' =>  $request->input('organization_unit_id'),
            'name' =>  $request->input('name'),
            'description' =>  $request->input('description'),
            'visitors' =>  $request->input('visitors'),
            'default' =>  $request->input('default'),
            'groups' =>  $request->input('groups')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {
        //Zarazeni pod OU
        $parentOrganizationUnit = OrganizationUnit::findOrFail($data['organization_unit_id']);

        //Vytvorime objekt AD
        $adGroup = Adldap::make()->group();

        $dn = 'CN='.$data['name'].','.$parentOrganizationUnit->distinguished_name;
        $adGroup->setDescription($data['description']);
        $adGroup->setDn($dn);

        // Save the user.
        if($adGroup->save()){
            $createData = [];
            $createData = [
                'guid' => $adGroup->getConvertedGuid(), 
                'distinguished_name' => $dn,
                'description' => $data['description'],
                'name' => $data['name']
            ];

            $createdDbGroup = Group::create($createData);

            if($data['default'] == 1){
                $dbOldDefault = Group::where('default', 1)->first();
                $dbOldDefault->default = 0; 
                $dbOldDefault->save();

                $createdDbGroup->default = 1; 
                $createdDbGroup->save();
            }

            if($data['visitors'] == 1){
                $dbOldVisitors = Group::where('visitors', 1)->first();
                $dbOldVisitors->visitors = 0;
                $dbOldVisitors->save();

                $createdDbGroup->visitors= 1; 
                $createdDbGroup->save();
            }

            if(isset($data['groups'])){
                foreach($data['groups'] as $id){
                    $dbGroup = Group::where('id', $id)->first();
                    if(!is_null($dbGroup)){
                        if($adGroup->inGroup($dbGroup->distinguished_name)){

                        } else {
                            $adFamilyGroup = Adldap::search()->groups()->where('distinguishedname', '=', $dbGroup->distinguished_name)->first();
                            $adFamilyGroup->addMember($dn);   
                        }
                    }
                }

                $createdDbGroup->parentGroups()->sync($data['groups']);
            }

        } else {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Vytvoření skupiny se nezdrařilo. Chyba AD.'
            ]; 
        }
        

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Skupina vytvořena.',
            'custom_data' => null
        ]; 

    }    

}

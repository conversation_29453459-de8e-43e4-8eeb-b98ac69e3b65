<?php namespace App\Services\Group;
use Illuminate\Http\Request;

use App\Models\Group;
use App\Models\OrganizationUnit;

use App\Jobs\AdGroupsSync;

use Adldap;

class GroupUpdateService {
    public function processPostRequest(Request $request, Group $group): Array {
        $data = [
            'name' =>  $request->input('name'),
            'description' =>  $request->input('description'),
            'visitors' =>  $request->input('visitors'),
            'default' =>  $request->input('default'),
            'groups' =>  $request->input('groups')
        ];

        return $this->process($data, $group);  
    }

    public function process(Array $data, Group $group) : array {
        $adGroup = Adldap::search()->groups()->where('distinguishedname', '=', $group->distinguished_name)->first(); 

        $adGroup->setDescription($data['description']);

        if($adGroup->save()){
            $updateData = [];
            $updateData = [
                'description' => $data['description']
            ];

            $group->update($updateData);

            if($data['default'] == 1){
                $dbOldDefault = Group::where('default', 1)->first();
                if(!is_null($dbOldDefault)){
                    $dbOldDefault->default = 0; 
                    $dbOldDefault->save();
                }


                $group->default = 1; 
                $group->save();
            }

            if($data['visitors'] == 1){
                $dbOldVisitors = Group::where('visitors', 1)->first();
                if(!is_null($dbOldVisitors)){
                    $dbOldVisitors->visitors = 0;
                    $dbOldVisitors->save();
                }

                $group->visitors= 1; 
                $group->save();
            }

        } else {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Úprava skupiny se nezdrařila. Chyba AD.'
            ]; 
        }

        if($group->name != $data['name']){
            if($adGroup->rename("CN=".$data['name'])){
                $group->name = $data['name'];
                $group->distinguished_name = $adGroup->getDistinguishedName();       
                $group->save();
            } else {
                return [
                    'success' => false,
                    'status_code' => null,
                    'message' => 'Úprava skupiny se nezdrařila.'
                ];  
            }
        }

        
        /*
        Zmenime Group Groups v AD
        */
        $dbGroupGroupsIds = $group->parentGroups->pluck('id')->toArray();
        //Pridat do skupin:
        if(!isset($data['groups'])){
            $data['groups'] = [];
        }

        $addToIds = array_diff($data['groups'], $dbGroupGroupsIds);

        foreach($addToIds as $id){
            $dbGroup = Group::where('id', $id)->first();
            $databaseAdGroup = Adldap::search()->groups()->where('distinguishedname', '=', $dbGroup->distinguished_name)->first();

            if(!is_null($dbGroup)){
                if($adGroup->inGroup($dbGroup->distinguished_name)){
                } else {
                    $databaseAdGroup->addMember($group->distinguished_name);   
                }
             }

        }

        

        //Odebrat ze skupin:
        $removeFromIds = array_diff($dbGroupGroupsIds, $data['groups']);

        foreach($removeFromIds as $id){
            $dbGroup = Group::where('id', $id)->first();
            $databaseAdGroup = Adldap::search()->groups()->where('distinguishedname', '=', $dbGroup->distinguished_name)->first();

            if(!is_null($dbGroup)){
                if($adGroup->inGroup($dbGroup->distinguished_name)){
                    $databaseAdGroup->removeMember($group->distinguished_name);
                }
            }                
        }
     
        $group->parentGroups()->sync($data['groups']);

        AdGroupsSync::dispatch()->onQueue('high');

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Skupina upravena. Změny se projeví do několika minut.',
            'custom_data' => null
        ]; 

    }    

}

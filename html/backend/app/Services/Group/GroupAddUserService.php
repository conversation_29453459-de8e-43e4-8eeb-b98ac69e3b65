<?php namespace App\Services\Group;
use Illuminate\Http\Request;

use App\Models\Group;
use App\Models\User;

use Adldap;

class GroupAddUserService {
    public function processPostRequest(Request $request, Group $group): Array {
        $data = [
            'user_id' =>  $request->input('user_id'),
        ];

        return $this->process($data, $group);  
    }

    public function process(Array $data, Group $group) : array {
        $dbUser = User::where('id',$data['user_id'])->first();
        $adUser = Adldap::search()->where('samaccountname',$dbUser->account_name)->first();

        $adGroup = Adldap::search()->groups()->where('distinguishedname', '=', $group->distinguished_name)->first();
       
        if($adUser->inGroup($group->distinguished_name)){
        } else {
            $adGroup->addMember($dbUser->distinguished_name);   
            $dbUser->groups()->attach($group->id);
        }
        
        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Uživatel přidán.',
            'custom_data' => null
        ]; 

    }    

}

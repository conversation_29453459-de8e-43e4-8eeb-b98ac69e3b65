<?php namespace App\Services\InternetBlock;
use Illuminate\Http\Request;

use App\Models\InternetBlock;
use App\Models\User;
use App\Models\OrganizationUnit;
use Auth;

class InternetBlockOuDatetimeService {
    public function processPostRequest(Request $request): Array {
        $organizationUnits = $request->input('organization_units');
        $data = [
            'from' =>  $request->input('from'),
            'to' =>  $request->input('to')
        ];

        return $this->process($organizationUnits, $data);  
    }

    public function process(Array $organizationUnits, Array $data) : array {
        $userTopPriority =  Auth::user()->getTopRolePriority();

        foreach($organizationUnits as $organizationUnit){
            $dbOrganizationUnit = OrganizationUnit::where('id', $organizationUnit)->first();
            if(!is_null($dbOrganizationUnit)){
                $users = $dbOrganizationUnit->users;
                foreach($users as $user){
                    if(!is_null($user)){
                        if($user->active_directory == 1){
                            $targetUserTopPriority = $user->getTopRolePriority();

                            if($userTopPriority < $targetUserTopPriority){
                                $data = [
                                    'user_id' => $user->id,
                                    'account_name' => $user->account_name,
                                    'from' => $data['from'],
                                    'to' => $data['to']
                                ];
                                $internetBlock = InternetBlock::create($data); 
                            }
                        }
                    }       
                }
            }
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Blokace zadány.'
        ]; 

    }
}

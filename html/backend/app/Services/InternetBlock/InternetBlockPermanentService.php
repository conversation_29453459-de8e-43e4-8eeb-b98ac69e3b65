<?php namespace App\Services\InternetBlock;
use Illuminate\Http\Request;

use App\Models\InternetBlock;
use App\Models\User;
use Auth;

class InternetBlockPermanentService {
    public function processPostRequest(Request $request): Array {
        $users = $request->input('users');


        return $this->process($users);  
    }

    public function process(Array $users) : array {
        $userTopPriority =  Auth::user()->getTopRolePriority();

        foreach($users as $user){
            $dbUser = User::where('id',$user)->first();
            if(!is_null($dbUser)){
                if($dbUser->active_directory == 1){
                    $targetUserTopPriority = $dbUser->getTopRolePriority();

                    if($userTopPriority < $targetUserTopPriority){
                        $data = [
                            'user_id' => $user,
                            'account_name' => $dbUser->account_name,
                            'permanent' => 1
                        ];

                        $internetBlock = InternetBlock::create($data); 
                    }
                }
            }

            
        }

        

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Blokace zadány.'
        ]; 

    }
}

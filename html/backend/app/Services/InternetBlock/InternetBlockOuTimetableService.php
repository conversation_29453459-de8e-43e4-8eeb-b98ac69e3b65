<?php namespace App\Services\InternetBlock;
use Illuminate\Http\Request;

use App\Models\InternetBlock;
use App\Models\User;
use App\Models\Timetable;
use App\Models\OrganizationUnit;
use Auth;

class InternetBlockOuTimetableService {
    public function processPostRequest(Request $request): Array {
        $organizationUnits = $request->input('organization_units');
        $timetables = $request->input('timetables');
        $data = [
            'date' =>  $request->input('date')
        ];

        return $this->process($organizationUnits, $timetables, $data);  
    }

    public function process(Array $organizationUnits, Array $timetables, Array $data) : array {
        $userTopPriority =  Auth::user()->getTopRolePriority();

        $timetableArary = [];
        $timetablesCollection = Timetable::whereIn('id', $timetables)->get();
        foreach($timetablesCollection as $timetableObject){
            $timetableArary[$timetableObject->id] = [
                'start' => $timetableObject->start,
                'end' => $timetableObject->end
            ];
        }
        foreach($organizationUnits as $organizationUnit){
            $dbOrganizationUnit = OrganizationUnit::where('id', $organizationUnit)->first();
            if(!is_null($dbOrganizationUnit)){
                $users = $dbOrganizationUnit->users;
                foreach($users as $user){
                    if(!is_null($user)){
                        if($user->active_directory == 1){
                            $targetUserTopPriority = $user->getTopRolePriority();

                            if($userTopPriority < $targetUserTopPriority){
                                foreach($timetables as $timetable){
                                    $newData = [
                                        'user_id' => $user->id,
                                        'account_name' => $user->account_name,
                                        'from' => $data['date']." ".$timetableArary[$timetable]['start'],
                                        'to' => $data['date']." ".$timetableArary[$timetable]['end'],
                                    ];

                                    $internetBlock = InternetBlock::create($newData); 
                                }
                            }
                        }
                    }      
                }
            }
        }

        

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Blokace zadány.'
        ]; 

    }

}

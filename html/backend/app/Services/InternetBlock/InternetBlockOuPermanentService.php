<?php namespace App\Services\InternetBlock;
use Illuminate\Http\Request;

use App\Models\InternetBlock;
use App\Models\User;
use App\Models\OrganizationUnit;
use Auth;

class InternetBlockOuPermanentService {
    public function processPostRequest(Request $request): Array {
        $organizationUnits = $request->input('organization_units');
        return $this->process($organizationUnits);  
    }

    public function process(Array $organizationUnits) : array {
        $userTopPriority =  Auth::user()->getTopRolePriority();
        foreach($organizationUnits as $organizationUnit){
            $dbOrganizationUnit = OrganizationUnit::where('id', $organizationUnit)->first();
            if(!is_null($dbOrganizationUnit)){
                $users = $dbOrganizationUnit->users;
                foreach($users as $user){
                    if(!is_null($user)){
                        if($user->active_directory == 1){
                            $targetUserTopPriority = $user->getTopRolePriority();

                            if($userTopPriority < $targetUserTopPriority){
                                $data = [
                                    'user_id' => $user->id,
                                    'account_name' => $user->account_name,
                                    'permanent' => 1
                                ];
                                $internetBlock = InternetBlock::create($data); 
                            }
                        }
                    }
                }
            }
        }

        

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Blokace zadány.'
        ]; 

    }
}

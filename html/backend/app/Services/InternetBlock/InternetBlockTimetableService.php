<?php namespace App\Services\InternetBlock;
use Illuminate\Http\Request;

use App\Models\InternetBlock;
use App\Models\User;
use App\Models\Timetable;
use Auth;

class InternetBlockTimetableService {
    public function processPostRequest(Request $request): Array {
        $users = $request->input('users');
        $timetables = $request->input('timetables');
        $data = [
            'date' =>  $request->input('date')
        ];

        return $this->process($users, $timetables, $data);  
    }

    public function process(Array $users, Array $timetables, Array $data) : array {
        $userTopPriority =  Auth::user()->getTopRolePriority();

        $timetableArary = [];
        $timetablesCollection = Timetable::whereIn('id', $timetables)->get();
        foreach($timetablesCollection as $timetableObject){
            $timetableArary[$timetableObject->id] = [
                'start' => $timetableObject->start,
                'end' => $timetableObject->end
            ];
        }

        foreach($users as $user){
            $dbUser = User::where('id',$user)->first();
            if(!is_null($dbUser)){
                if($dbUser->active_directory == 1){
                    $targetUserTopPriority = $dbUser->getTopRolePriority();

                    if($userTopPriority < $targetUserTopPriority){
                        foreach($timetables as $timetable){
                            $newData = [
                                'user_id' => $user,
                                'account_name' => $dbUser->account_name,
                                'from' => $data['date']." ".$timetableArary[$timetable]['start'],
                                'to' => $data['date']." ".$timetableArary[$timetable]['end'],
                            ];

                            $internetBlock = InternetBlock::create($newData); 
                        }
                    }
                }
            }      
        }

        

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Blokace zadány.'
        ]; 

    }

}

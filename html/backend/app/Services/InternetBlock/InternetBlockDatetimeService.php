<?php namespace App\Services\InternetBlock;
use Illuminate\Http\Request;

use App\Models\InternetBlock;
use App\Models\User;
use Auth;

class InternetBlockDatetimeService {
    public function processPostRequest(Request $request): Array {
        $users = $request->input('users');
        $data = [
            'from' =>  $request->input('from'),
            'to' =>  $request->input('to')
        ];

        return $this->process($users, $data);  
    }

    public function process(Array $users, Array $data) : array {

        $userTopPriority =  Auth::user()->getTopRolePriority();
        
        foreach($users as $user){
            $dbUser = User::where('id',$user)->first();
            if(!is_null($dbUser)){
                if($dbUser->active_directory == 1){
                    $targetUserTopPriority = $dbUser->getTopRolePriority();

                    if($userTopPriority < $targetUserTopPriority){
                        $data = [
                            'user_id' => $user,
                            'account_name' => $dbUser->account_name,
                            'from' => $data['from'],
                            'to' => $data['to']
                        ];
                        $internetBlock = InternetBlock::create($data); 
                    }
                }
            }       
        }

        

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Blokace zadány.'
        ]; 

    }
}

<?php namespace App\Services\Permission;
use Illuminate\Http\Request;

use App\Models\Permission;


class PermissionUpdateService {
    public function processPostRequest(Request $request, Permission $permission): Array {
        $data = [
            'name' =>  $request->input('name'),
            'human_name' =>  $request->input('human_name'),
            'position' =>  $request->input('position'),
            'category_id' =>  $request->input('category_id')
        ];

        return $this->process($data, $permission);  
    }

    public function process(Array $data, Permission $permission) : array {


        $permission->name = $data['name'];
        $permission->human_name = $data['human_name'];
        $permission->position = $data['position'];
        $permission->category_id = $data['category_id'];

        $permission->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Oprávnění bylo upraveno.'
        ]; 

    }
}

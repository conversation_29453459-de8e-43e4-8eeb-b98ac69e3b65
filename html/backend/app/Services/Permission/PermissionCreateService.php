<?php namespace App\Services\Permission;
use Illuminate\Http\Request;

use App\Models\Permission;


class PermissionCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'name' =>  $request->input('name'),
            'human_name' =>  $request->input('human_name'),
            'position' =>  $request->input('position'),
            'category_id' =>  $request->input('category_id')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $data = [
            'name' => $data['name'],
            'human_name' => $data['human_name'],
            'position' => $data['position'],
            'category_id' => $data['category_id'],
            'guard_name' => 'web'
        ];

        $role = Permission::create($data); 

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Oprávnění bylo přidáno.'
        ]; 

    }
}

<?php namespace App\Services\Permission;

use App\Models\User;
use App\Models\Permission;

class GetPermissionService {
    public function getPermissionsArray(User $user) : array {
        if($user->hasRole('Super Admin')){
            $permissions = Permission::all()->pluck('name')->toArray();
        } else {
            $permissions = $user->getAllPermissions()->pluck('name')->toArray();
        }

        return $permissions;
    }
}

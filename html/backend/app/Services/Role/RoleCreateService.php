<?php namespace App\Services\Role;
use Illuminate\Http\Request;

use App\Models\Role;


class RoleCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'name' =>  $request->input('name'),
            'role_priority' =>  $request->input('role_priority'),
            'copy' =>  $request->input('copy')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {
        $copyRole = null;
        if(isset($data['copy'])){
            $copyRole = Role::findOrFail($data['copy']);
        }

        $data = [
            'name' => $data['name'],
            'role_priority' => $data['role_priority'],
            'guard_name' => "web"
        ];

        $role = Role::create($data); 

        if(isset($copyRole)){
            $permissions = $copyRole->permissions->pluck('name');
            $role->syncPermissions($permissions);
        }    

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Role byla vytvořena.',
            'custom_data' => [
                'role' => $role
            ]
        ]; 

    }
}

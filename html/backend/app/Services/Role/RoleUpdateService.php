<?php namespace App\Services\Role;
use Illuminate\Http\Request;

use App\Models\Role;


class RoleUpdateService {
    public function processPostRequest(Request $request, Role $role): Array {
        $data = [
            'name' =>  $request->input('name'),
            'role_priority' =>  $request->input('role_priority'),
            'permissions' =>  $request->input('permissions')
        ];

        return $this->process($data, $role);  
    }

    public function process(Array $data, Role $role) : array {
        $role->name = $data['name'];
        $role->role_priority = $data['role_priority'];
        $role->save();

        $role->syncPermissions($data['permissions']);


        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Role byla upravena.',
            'custom_data' => [
                'role' => $role
            ]
        ]; 

    }
}

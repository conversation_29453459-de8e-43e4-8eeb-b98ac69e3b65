<?php namespace App\Services\Building;
use Illuminate\Http\Request;

use App\Models\Building;


class BuildingCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'name' =>  $request->input('name'),
            'code' =>  $request->input('code')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $data = [
            'name' => $data['name'],
            'code' => $data['code']
        ];

        $building = Building::create($data); 

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Budova byla vytvořena.'
        ]; 

    }
}

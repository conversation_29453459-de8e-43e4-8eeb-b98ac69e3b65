<?php namespace App\Services\Building;
use Illuminate\Http\Request;

use App\Models\Building;


class BuildingUpdateService {
    public function processPostRequest(Request $request, Building $building): Array {
        $data = [
            'name' =>  $request->input('name'),
            'code' =>  $request->input('code')
        ];

        return $this->process($data, $building);  
    }

    public function process(Array $data, Building $building) : array {
        $building->name = $data['name'];
        $building->code = $data['code'];
        $building->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Budova byla upravena.'
        ]; 

    }
}

<?php namespace App\Services\Move;


use App\Models\Move;
use App\Services\File\GeneratePdfService;
use Auth;
use Carbon\Carbon;

class MovePdfService {

    public function process(Move $move){     
        if($move->state != 'PROCESSED'){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Tato převodka nebyla zpracována'
            ];   
        }

        $service = New GeneratePdfService;

        $data['move'] = $move->toArray();
        $data['move']['responsible_user'] = Auth::user();
        $data['move']['now'] = Carbon::now();

        $filePath = match ($move->type) {
            'TO_USER' => $fileName = $service->process('pdf.move.to_user', $data),
            'FROM_USER' => $fileName = $service->process('pdf.move.from_user', $data),
            'TO_ROOM' => $fileName = $service->process('pdf.move.room', $data),
            'FROM_ROOM' => $fileName = $service->process('pdf.move.room', $data)
        };

        $fileName = match ($move->type) {
            'TO_USER' => 'Převodka na uživatele',
            'FROM_USER' => 'Převodka z uživatele',
            'TO_ROOM' => 'Změna inventáře místnosti',
            'FROM_ROOM' => 'Změna inventáře místnosti'
        };

        $headers = [
            'File-Name' => urlencode($fileName)
        ];

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Dokument vytvořen',
            'custom_data' => null,
            'file_data' => [
                'path' => storage_path('/app/temp/'. $filePath),
                'file_name' => $fileName,
                'headers' => $headers
            ]
        ]; 

    }
}

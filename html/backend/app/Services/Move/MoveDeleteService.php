<?php namespace App\Services\Move;
use Illuminate\Http\Request;

use App\Models\Move;


class MoveDeleteService {
    public function processPostRequest(Move $move): Array {
        return $this->process($move);  
    }

    public function process(Move $move) : array {
        if($move->state == 'PROCESSED'){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Předávací protokol byl již z<PERSON>ván a nelze ho smazat.'
            ];   
        }

        $items = $move->items;

        foreach($items as $item){
            $item->state = 'NOT_ASSIGNED';
            $item->save();            
        }

        $move->items()->detach();
        $move->delete();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Předávací protokol smazán.'
        ]; 

    }
}

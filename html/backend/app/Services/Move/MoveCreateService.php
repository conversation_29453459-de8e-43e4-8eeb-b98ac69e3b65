<?php namespace App\Services\Move;
use Illuminate\Http\Request;

use App\Models\Move;


class MoveCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'room_id' =>  $request->input('room_id'),
            'user_id' =>  $request->input('user_id'),
            'type' =>  $request->input('type')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $data = [
            'room_id' => $data['room_id'],
            'user_id' => $data['user_id'],
            'type' => $data['type']
        ];

        $move = Move::create($data); 

        $message = $this->message($data['type']);

        return [
            'success' => true,
            'status_code' => 200,
            'message' => $message
        ]; 

    }

    public function message($type){
        // 'TO_USER','FROM_USER','TO_ROOM','FROM_ROOM'
        $message = match ($type) {
            'TO_USER' => 'Přepis na uživatele založen',
            'FROM_USER' => 'Přepis z uživatele založen',
            'TO_ROOM' => 'Přepis na místnost založen',
            'FROM_ROOM' => 'Přepis z místnost založen'
        };
        
        return $message;
    }
}

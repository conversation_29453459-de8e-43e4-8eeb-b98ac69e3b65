<?php namespace App\Services\Move;
use Illuminate\Http\Request;

use App\Models\Move;

class MoveProcessService {
    public function processPostRequest(Move $move): Array {

        return $this->process($move);  
    }

    public function process(Move $move) : array {
        if($move->state == 'PROCESSED'){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Tato převodka již byla zpracována'
            ];   
        }

        $items = $move->items;
        $itemState = $this->newItemState($move->type);

        foreach($items as $item){
            if($move->type == "TO_USER"){
                $item->user_id = $move->user_id;  
            }

            if($move->type == "TO_ROOM"){
                $item->room_id = $move->room_id;  
            }

            if($move->type == "FROM_USER" or $move->type == "FROM_ROOM"){
                $item->user_id = null;  
                $item->room_id = null;  
            }

            $item->state = $itemState;
            $item->save();
        }

        $move->state = 'PROCESSED';
        $move->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Převodka zpracována'
        ]; 

    }

    public function newItemState($type){
        // 'TO_USER','FROM_USER','TO_ROOM','FROM_ROOM'
        $state = match ($type) {
            'TO_USER' => 'ASSIGNED_TO_USER',
            'FROM_USER' => 'NOT_ASSIGNED',
            'TO_ROOM' => 'ASSIGNED_TO_ROOM',
            'FROM_ROOM' => 'NOT_ASSIGNED'
        };
        
        return $state;
    }
}

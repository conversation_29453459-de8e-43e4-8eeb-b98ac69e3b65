<?php namespace App\Services\Move;
use Illuminate\Http\Request;

use App\Models\Move;


class MoveUpdateService {
    public function processPostRequest(Request $request, Move $move): Array {
        $data = [
            'room_id' =>  $request->input('room_id'),
            'user_id' =>  $request->input('user_id'),
            'type' =>  $request->input('type')
        ];

        return $this->process($data, $move);  
    }

    public function process(Array $data, Move $move) : array {
        $move->room_id = $data['room_id'];
        $move->user_id = $data['user_id'];
        $move->type = $data['type'];
        $move->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Přepis upraven.'
        ]; 

    }
}

<?php namespace App\Services\MoveItem;
use Illuminate\Http\Request;

use App\Models\MoveItem;
use App\Models\Move;
use App\Models\Item;

class MoveItemMultipleCheckService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'move_id' =>  $request->input('move_id'),
            'evidence_numbers' =>  $request->input('evidence_numbers')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $move = Move::find($data['move_id']);
        if($move->state == 'PROCESSED'){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Tato převodka byla zpracována. Nelze dělat úpravy.'
            ];   
        }

        $failedItems = [];
        $stringItems = explode(";", $data['evidence_numbers']);

        $errors = [];
        $items = [];

        foreach($stringItems as $stringItem){
            if($stringItem != ""){
                $item = Item::where('evidence_number',$stringItem)->first();
                if($item != null){
                    $items[] = [
                       'id' => $item->id,
                       'name' => $item->name,
                       'evidence_number' => $item->evidence_number,
                       'state' => $item->state,
                       'errors' => $this->checkErrors($move, $item)
                    ];  
                } else {
                    $errors[] = [
                        'message' => 'Položka s evidenčním číslem: '.$stringItem.' nebyla nalezena.'
                    ]; 
                } 
            }
             
        }
        
        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Zkontrolujte prosím načtené položky.',
            'custom_data' => [
                'items' => $items,
                'errors' => $errors
            ]
        ]; 
        

    }

    public function checkErrors($move, $item){
        $error = null;
        if(($move->type == "TO_USER") or ($move->type == "TO_ROOM")){
            if($item->state != 'NOT_ASSIGNED'){
                $error = "Tato položka je již přiřazena nebo vyřazena.";
            }         
        } else {
            if($move->type == "FROM_USER"){
                if(($item->state != 'ASSIGNED_TO_USER') or ($move->user_id != $item->user_id)){
                    $error = "Tato položka není přiřazena k tomuto uživateli.";      
                }
            } else {
                if($move->type == "FROM_ROOM"){
                    if(($item->state != 'ASSIGNED_TO_ROOM') or ($move->room_id != $item->room_id)){
                        $error = "Tato položka není přiřazena k této místnosti.";      
                    }
                }
            }
        }
        return $error;
    }
}

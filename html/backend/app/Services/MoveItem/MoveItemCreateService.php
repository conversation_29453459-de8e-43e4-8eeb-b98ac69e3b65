<?php namespace App\Services\MoveItem;
use Illuminate\Http\Request;

use App\Models\MoveItem;
use App\Models\Move;
use App\Models\Item;

class MoveItemCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'move_id' =>  $request->input('move_id'),
            'item_id' =>  $request->input('item_id')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $move = Move::find($data['move_id']);
        if($move->state == 'PROCESSED'){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Tato převodka byla zpracována. Nelze dělat úpravy.'
            ];   
        }

        $data = [
            'move_id' => $data['move_id'],
            'item_id' => $data['item_id']
        ];

        $item = Item::find($data['item_id']);
        if($move->type == 'TO_USER' or $move->type == 'TO_ROOM'){         
            if($item->state != "NOT_ASSIGNED"){
                return [
                    'success' => false,
                    'status_code' => 400,
                    'message' => 'Tuto položku nelze přiřadit, je již přiřazená nebo vyřazená.'
                ]; 
            } 
            $item->state = 'LOCKED';
            $item->save();
        }

        if($move->type == "FROM_ROOM"){         
            if(($item->state != 'ASSIGNED_TO_ROOM') or ($move->room_id != $item->room_id)){
                return [
                    'success' => false,
                    'status_code' => 400,
                    'message' => 'Tato položka není přiřazena k této místnosti.'
                ]; 
            } 
        }

        if($move->type == "FROM_USER"){         
            if(($item->state != 'ASSIGNED_TO_USER') or ($move->user_id != $item->user_id)){
                return [
                    'success' => false,
                    'status_code' => 400,
                    'message' => 'Tato položka není přiřazena k tomuto uživateli.'
                ]; 
            } 
        }

        $move = MoveItem::create($data); 

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Položka přidána.'
        ]; 

    }
}

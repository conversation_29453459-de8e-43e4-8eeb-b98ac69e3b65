<?php namespace App\Services\MoveItem;
use Illuminate\Http\Request;

use App\Models\MoveItem;
use App\Models\Move;
use App\Models\Item;

class MoveItemMultipleCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'move_id' =>  $request->input('move_id'),
            'items' =>  $request->input('items')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $move = Move::find($data['move_id']);
        if($move->state == 'PROCESSED'){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Tato převodka byla zpracována. Nelze dělat úpravy.'
            ];   
        }

        $errors = [];
        $items = $data['items'];

        //Tady to vsechno jeste jednou checku protoze mezi checkem a insertem se muze neco zmenit ze
        foreach($items as $item){
            $dbItem = Item::find($item);  
            if($dbItem != null){
                $error = $this->checkErrors($move,$dbItem);
                if($error != null){
                    $errors[] = $error; 
                }     
            } else {
                $errors[] = "Item id: '".$item."' nenalezen."; 
            }  
        }

        //Pokud check v klidu tak to prote zalozim
        if(count($errors) == 0){
            $itemData = [];
            foreach($items as $item){
                $itemData = [
                    'move_id' => $data['move_id'],
                    'item_id' => $item
                ];
                $dbItem = Item::find($item);
                $dbItem->state = 'LOCKED';
                $dbItem->save();

                MoveItem::create($itemData);  
            }
        } else {
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Položky se nepodařilo přidat.',
                'custom_data' => [
                    'errors' => $errors
                ]
            ]; 
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Položky přidány.'
        ]; 
    }

    public function checkErrors($move, $item){
        $error = null;
        if(($move->type == "TO_USER") or ($move->type == "TO_ROOM")){
            if($item->state != 'NOT_ASSIGNED'){
                $error = "Položka ". $item->name ." (".$item->evidence_number.") je již přiřazena nebo vyřazena.";
            }         
        } else {
            if($move->type == "FROM_USER"){
                if(($item->state != 'ASSIGNED_TO_USER') or ($move->user_id != $item->user_id)){
                    $error = "Položka ". $item->name ." (".$item->evidence_number.") není přiřazena k tomuto uživateli.";      
                }
            } else {
                if($move->type == "FROM_ROOM"){
                    if(($item->state != 'ASSIGNED_TO_ROOM') or ($move->room_id != $item->room_id)){
                        $error = "Položka ". $item->name ." (".$item->evidence_number.") není přiřazena k této místnosti.";      
                    }
                }
            }
        }
        return $error;
    }
}

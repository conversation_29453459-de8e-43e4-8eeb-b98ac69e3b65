<?php namespace App\Services\MoveItem;
use Illuminate\Http\Request;

use App\Models\MoveItem;
use App\Models\Move;
use App\Models\Item;

class MoveItemDeleteService {
    public function processPostRequest(MoveItem $moveItem): Array {

        return $this->process($moveItem);  
    }

    public function process(MoveItem $moveItem) : array {

        $move = $moveItem->move;
        if($move->state == 'PROCESSED'){
            return [
                'success' => false,
                'status_code' => 400,
                'message' => 'Tato převodka byla zpracována. Nelze dělat úpravy.'
            ];   
        }

        if($move->type == 'TO_USER' or $move->type == 'TO_ROOM'){
            $item = $moveItem->item;
            $item->state = 'NOT_ASSIGNED';
            $item->save();
        }


        $moveItem->delete(); 

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Polo<PERSON>ka odebrána'
        ]; 

    }
}

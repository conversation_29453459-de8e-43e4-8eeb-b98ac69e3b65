<?php namespace App\Services\Settings;
use Illuminate\Http\Request;

use App\Services\Settings\EnvUpdateService;

class SettingsDefaultGroupsOusUpdateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'student_default_groups'  =>  $request->input('student_default_groups'),
            'employee_default_groups' =>  $request->input('employee_default_groups'),
            'guest_default_groups'    =>  $request->input('guest_default_groups'),
            'guest_default_organization_unit'    =>  $request->input('guest_default_organization_unit')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {
        $envService = New EnvUpdateService;
        $serviceData = $envService->process($data);
        if(!$serviceData['success']){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Nepovedlo se uložit všechny změny.'
            ]; 
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny uloženy.',
            'custom_data' => null 
        ]; 

    }
}

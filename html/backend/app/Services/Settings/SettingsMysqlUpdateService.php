<?php namespace App\Services\Settings;
use Illuminate\Http\Request;

use App\Services\Settings\EnvUpdateService;

class SettingsMysqlUpdateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'db_host'           =>  $request->input('db_host'),
            'db_port'           =>  $request->input('db_port'),
            'db_database'       =>  $request->input('db_database'),
            'db_username'       =>  $request->input('db_username'),   
            'db_password'       =>  $request->input('db_password')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {
        $envService = New EnvUpdateService;
        $serviceData = $envService->process($data);
        if(!$serviceData['success']){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Nepovedlo se uložit všechny změny.'
            ]; 
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny uloženy.',
            'custom_data' => null 
        ]; 

    }
}

<?php namespace App\Services\Settings;
use Illuminate\Http\Request;

use App\Services\Settings\EnvUpdateService;

class SettingsFortinetUpdateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'fortinet_ip'      =>  $request->input('fortinet_ip'),
            'fortinet_token'   =>  $request->input('fortinet_token')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {
        $envService = New EnvUpdateService;
        $serviceData = $envService->process($data);
        if(!$serviceData['success']){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Nepovedlo se uložit všechny změny.'
            ]; 
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny uloženy.',
            'custom_data' => null 
        ]; 

    }
}

<?php namespace App\Services\Settings;
use Illuminate\Http\Request;

use App\Services\Settings\EnvUpdateService;

class SettingsGsmGateUpdateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'gsm_gate_ip'      =>  $request->input('gsm_gate_ip'),
            'gsm_gate_account'   =>  $request->input('gsm_gate_account'),
            'gsm_gate_password'   =>  $request->input('gsm_gate_password'),
            'gsm_gate_sim_port'   =>  $request->input('gsm_gate_sim_port'),
            'gsm_send_sms'   =>  $request->input('gsm_send_sms')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {
        $envService = New EnvUpdateService;
        $serviceData = $envService->process($data);
        if(!$serviceData['success']){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Nepovedlo se uložit všechny změny.'
            ]; 
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny uloženy.',
            'custom_data' => null 
        ]; 

    }
}

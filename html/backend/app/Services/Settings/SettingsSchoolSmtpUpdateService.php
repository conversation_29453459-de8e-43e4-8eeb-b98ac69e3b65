<?php namespace App\Services\Settings;
use Illuminate\Http\Request;

use App\Services\Settings\EnvUpdateService;

class SettingsSchoolSmtpUpdateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'school_mail_host'      =>  $request->input('school_mail_host'),
            'school_mail_port'      =>  $request->input('school_mail_port'),
            'school_mail_username'  =>  $request->input('school_mail_username'),
            'school_mail_password'  =>  $request->input('school_mail_password'),   
            'school_mail_encryption'=>  $request->input('school_mail_encryption')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {
        $envService = New EnvUpdateService;
        $serviceData = $envService->process($data);
        if(!$serviceData['success']){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Nepovedlo se uložit všechny změny.'
            ]; 
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny uloženy.',
            'custom_data' => null 
        ]; 

    }
}

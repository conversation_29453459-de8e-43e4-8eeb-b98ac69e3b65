<?php namespace App\Services\TicketAnswer;
use Illuminate\Http\Request;

use App\Models\TicketAnswer;

class TicketAnswerUpdateService {
    public function processPostRequest(TicketAnswer $ticketAnswer, Request $request): Array {
        $data = [
            'text' =>  $request->input('text')
        ];

        return $this->process($ticketAnswer, $data);  
    }

    public function process(TicketAnswer $ticketAnswer,  Array $data) : array {

        if($ticketAnswer->ticket->state->key == "CLOSED"){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Ticket je uzavřen.',
            ];   
        }

        $ticketAnswer->text = $data['text'];

        $ticketAnswer->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny uloženy.',
            'custom_data' => null
        ]; 

    }
}

<?php namespace App\Services\TicketAnswer;
use Illuminate\Http\Request;

use App\Models\User;
use App\Models\Ticket;
use App\Models\TicketAnswer;
use App\Models\TicketState;

use Illuminate\Support\Facades\Mail;
use App\Mail\Ticket\TicketAnswerCreateMail;

class TicketAnswerCreateService {
    public function processPostRequest(Ticket $ticket, Request $request): Array {
        $data = [
            'text' =>  $request->input('text')
        ];

        return $this->process($ticket, $data);  
    }

    public function process(Ticket $ticket, Array $data) : array {

        if($ticket->state->key == "CLOSED"){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Ticket je uzavřen.',
            ];   
        }
        
        $loggedUser = auth()->user();
        $ticketState = $ticket->state->key;

        if($ticket->user->id == $loggedUser->id){
            $ticketState = TicketState::where('key',"WAITING_FOR_ADMIN")->first();  
        } else {
            $ticketState = TicketState::where('key',"WAITING_FOR_USER")->first(); 
        }

        $loggedUser = auth()->user();

        $data = [
            'user_id' => $loggedUser->id,
            'state_id' => $ticketState->id,
            'text' => $data['text']

        ];

        $ticketAnswer = $ticket->answers()->create($data);

        if($ticket->user->id == $loggedUser->id){
            $adminEmails = $this->getEmails(role: 'Super Admin', permission: 'tickets.master');

            foreach($adminEmails as $email){
                Mail::to($email)->queue(new TicketAnswerCreateMail($ticket, $ticketAnswer));
            } 
        } else {
            Mail::to($ticket->user->email)->queue(new TicketAnswerCreateMail($ticket, $ticketAnswer));
        }


        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Odpověd k ticketu byla uložena.',
            'custom_data' => null
        ]; 

    }

    public function getEmails(string $role = null, string $permission = null) : array{
        $usersWithPermission = User::permission($permission)->get();
        $usersWithRole = User::role($role)->get();

        $emailArray1 = $usersWithRole->pluck('email')->toArray();
        $emailArray2 =$usersWithPermission->pluck('email')->toArray(); 

        $emailArray = array_unique (array_merge ($emailArray1, $emailArray2));

        return $emailArray;
    }
}

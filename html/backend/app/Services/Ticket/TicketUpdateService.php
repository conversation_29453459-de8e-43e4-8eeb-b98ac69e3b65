<?php namespace App\Services\Ticket;
use Illuminate\Http\Request;

use App\Models\Ticket;

class TicketUpdateService {
    public function processPostRequest(Ticket $ticket, Request $request): Array {
        $data = [
            'subject' =>  $request->input('subject'),
            'text' =>  $request->input('text'),
            'category_id' =>  $request->input('category_id'),
        ];

        return $this->process($ticket, $data);  
    }

    public function process(Ticket $ticket,  Array $data) : array {

        if($ticket->state->key == "CLOSED"){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Ticket je uzavřen.',
            ];   
        }

        $ticket->subject = $data['subject'];
        $ticket->text = $data['text'];
        $ticket->category_id = $data['category_id'];

        $ticket->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změny uloženy.',
            'custom_data' => null
        ]; 

    }
}

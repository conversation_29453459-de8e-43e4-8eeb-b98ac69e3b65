<?php namespace App\Services\Ticket;
use Illuminate\Http\Request;

use App\Models\Ticket;
use App\Models\TicketState;

class TicketCloseService {
    public function processPostRequest(Ticket $ticket): Array {
        return $this->process($ticket);  
    }

    public function process(Ticket $ticket) : array {

        $ticketState = TicketState::where('key',"CLOSED")->first();
        $ticket->state_id = $ticketState?->id;

        $ticket->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Ticket byl uzavřen.',
            'custom_data' => null
        ]; 

    }
}

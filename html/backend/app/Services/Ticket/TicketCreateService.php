<?php namespace App\Services\Ticket;
use Illuminate\Http\Request;

use App\Models\User;
use App\Models\Ticket;
use App\Models\TicketState;

use Illuminate\Support\Facades\Mail;
use App\Mail\Ticket\TicketCreateMail;

class TicketCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'subject' =>  $request->input('subject'),
            'text' =>  $request->input('text'),
            'category_id' =>  $request->input('category_id'),
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {
        $ticketState = TicketState::where('key',"NEW")->first();
        $loggedUser = auth()->user();

        $data = [
            'user_id' => $loggedUser->id,
            'state_id' => $ticketState->id,
            'category_id' => $data['category_id'],
            'subject' => $data['subject'],
            'text' => $data['text']

        ];

        $ticket = Ticket::create($data); 

        $adminEmails = $this->getEmails(role: 'Super Admin', permission: 'tickets.master');

        //$ticketWithRelationship = $ticket->load(['user','state','category']);

        foreach($adminEmails as $email){
            Mail::to($email)->queue(new TicketCreateMail($ticket));
        }


        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Ticket byl vytvořen.',
            'custom_data' => null
        ]; 

    }

    public function getEmails(string $role = null, string $permission = null) : array{
        $usersWithPermission = User::permission($permission)->get();
        $usersWithRole = User::role($role)->get();

        $emailArray1 = $usersWithRole->pluck('email')->toArray();
        $emailArray2 =$usersWithPermission->pluck('email')->toArray(); 

        $emailArray = array_unique (array_merge ($emailArray1, $emailArray2));

        return $emailArray;
    }
}

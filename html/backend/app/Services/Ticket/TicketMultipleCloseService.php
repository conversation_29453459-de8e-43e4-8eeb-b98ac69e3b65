<?php namespace App\Services\Ticket;
use Illuminate\Http\Request;

use App\Models\Ticket;

class TicketMultipleCloseService {
    public function processPostRequest(Request $request): Array {
        $data = $request->input('tickets');
        return $this->process($data);  
    }

    public function process(Array $data) : array {
        $tickets = Ticket::whereIn('id', $data)->get();

        $closeService = new TicketCloseService;
        foreach($tickets as $ticket){
            $closeService->process($ticket);
        }


        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Tickety byly uzavřeny.',
            'custom_data' => null
        ]; 

    }
}

<?php namespace App\Services\AccountingCategory;
use Illuminate\Http\Request;

use App\Models\AccountingCategory;


class AccountingCategoryUpdateService {
    public function processPostRequest(Request $request, AccountingCategory $accountingCategory): Array {
        $data = [
            'name' =>  $request->input('name'),
            'code' =>  $request->input('code')
        ];

        return $this->process($data, $accountingCategory);  
    }

    public function process(Array $data, AccountingCategory $accountingCategory) : array {
        $accountingCategory->name = $data['name'];
        $accountingCategory->code = $data['code'];
        $accountingCategory->save();

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Účetní druh majetku upraven.'
        ]; 

    }
}

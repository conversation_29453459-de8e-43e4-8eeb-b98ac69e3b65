<?php namespace App\Services\AccountingCategory;
use Illuminate\Http\Request;

use App\Models\AccountingCategory;


class AccountingCategoryCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'name' =>  $request->input('name'),
            'code' =>  $request->input('code')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $data = [
            'name' => $data['name'],
            'code' => $data['code']
        ];

        $accountingCategory = AccountingCategory::create($data); 

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Účetní druh majetku vytvořen.'
        ]; 

    }
}

<?php namespace App\Services\OrganizationUnit;
use Illuminate\Http\Request;

use App\Models\OrganizationUnit;

use Adldap;

class OrganizationUnitCreateService {
    public function processPostRequest(Request $request): Array {
        $data = [
            'parent_id' =>  $request->input('parent_id'),
            'name' =>  $request->input('name'),
            'promotion' => $request->input('promotion'),
            'is_class' => $request->input('is_class'),
            'map_bakalari' =>  $request->input('map_bakalari'),
            'map_skola_online' =>  $request->input('map_skola_online')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {
        //Najdeme rodice
        $parentOrganizationUnit = OrganizationUnit::findOrFail($data['parent_id']);

        //Vytvorime objekt AD
        $adOu = Adldap::make()->ou();

        //
        $dn = 'OU='.$data['name'].','.$parentOrganizationUnit->distinguished_name;
        $adOu->setDn($dn);

        // Save the user.
        if($adOu->save()){
            $createData = [];
            $createData = [
                'guid' => $adOu->getConvertedGuid(), 
                'parent' => $parentOrganizationUnit->distinguished_name,
                'distinguished_name' => $dn,
                'name' => $data['name'],
                'promotion' => $data['promotion'],
                'is_class' => $data['is_class'],
                'map_bakalari' => $data['map_bakalari'],
                'map_skola_online' => $data['map_skola_online']
            ];

            $dbOu = OrganizationUnit::create($createData);

        } else {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Vytvoření organizační jednotky se nezdrařilo. Chyba AD.'
            ]; 
        }
        

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Organizační jednotka vytvořena.',
            'custom_data' => null
        ]; 

    }    

}

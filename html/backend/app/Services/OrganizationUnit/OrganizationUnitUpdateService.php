<?php namespace App\Services\OrganizationUnit;
use Illuminate\Http\Request;

use App\Models\OrganizationUnit;
use App\Services\OrganizationUnit\AdOrganizationUnitsSyncService;

use Adldap;
use DB;

class OrganizationUnitUpdateService {
    public function processPostRequest(Request $request, OrganizationUnit $organizationUnit): Array {
        $data = [
            'name' =>  $request->input('name'),
            'promotion' => $request->input('promotion'),
            'is_class' => $request->input('is_class'),
            'map_bakalari' =>  $request->input('map_bakalari'),
            'map_skola_online' =>  $request->input('map_skola_online')
        ];

        return $this->process($data, $organizationUnit);  
    }

    public function process(Array $data, OrganizationUnit $organizationUnit) : array {
        $parent = OrganizationUnit::where(DB::raw('BINARY `name`'), $organizationUnit->parent)->first();

        $dn = $organizationUnit->distinguished_name;
        $adOu = Adldap::search()->ous()->where('distinguishedname', '=', $dn)->first();   

        $newName = "OU=".$data['name'];

        if($adOu->rename($newName)) {
            $organizationUnit->name = $data['name'];
            $organizationUnit->promotion = $data['promotion'];
            $organizationUnit->is_class = $data['is_class'];
            $organizationUnit->map_bakalari = $data['map_bakalari'];
            $organizationUnit->map_skola_online = $data['map_skola_online'];
            $organizationUnit->distinguished_name =  $adOu->getDistinguishedName().$parent?->distinguished_name;
            $organizationUnit->save();

            $service = New AdOrganizationUnitsSyncService();
            $service->process();

            return [
                'success' => true,
                'status_code' => 200,
                'message' => 'Změny byly uloženy.',
                'custom_data' => [
                    'organization_unit' => $organizationUnit
                ]
            ]; 

        } else {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Úprava Organizační jednotky se nezdařila.'
            ]; 
        }
    }    

}

<?php namespace App\Services\OrganizationUnit;
use Illuminate\Http\Request;

use App\Models\OrganizationUnit;
use App\Models\User;

use App\Services\OrganizationUnit\OrganizationUnitCheckPhoneService;
use App\Services\File\GeneratePdfService;

use App\Jobs\AdUserSetPassword;
use App\Jobs\SendSms;

class OrganizationUnitGeneratePasswordService {
    public function processPostRequest(Request $request): Array {

        $data = [
            'organization_units' => $request->input('organization_units'),
            'send_sms' => $request->input('send_sms'),
            'must_change_password' => $request->input('must_change_password')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : array {

        $service = New OrganizationUnitCheckPhoneService;
        $serviceData = $service->process($data['organization_units']);
 
        //Kontrola jestli mjaí všichni uživatele v daných OU nastavené tel.čísla - jen pokud se posílá SMS
        if($data['send_sms']){
            if(!$serviceData['success']){
                return $serviceData;
            }  
        }

        //Generování pole uživatelů s heslama - potřebujem hesla ve viditelné podobě pro pdf/sms, vytáhneme rovnou i číslo ať je kdyžtak ready
        $dataArary = $this->generateDataArray($data['organization_units']);

        //Projdu pole dat, at uz nemusim kukat znova do DB
        foreach($dataArary as $organizationUnit){
            foreach($organizationUnit['users'] as $user){
                //Nasypání změn hesel v AD do fronty
                AdUserSetPassword::dispatch($user['account_name'], $user['password'], $data['must_change_password'])->onQueue('medium'); 
                //Nasypání SMS do fronty - pokud posíláme sms
                if($data['send_sms']){
                    $text = "Nové heslo do školního systému: ".$user['password'];
                    SendSms::dispatch($user['phone'], $text)->onQueue('sms'); 
                }
            }
        }

        //Vytvoření PDFka
        $service = New GeneratePdfService;
        $fileName = $service->process(view: 'pdf.organization_units_passwords', data: $dataArary, name: "Vygenerovaná hesla"); 
        $fullfilename = $fileName;
         
        $headers = [
            'File-Name' => urlencode($fullfilename),
        ];

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Změna hesla byla navedena. Hesla budou v systému změněna v průběhu následujících minut.',
            'custom_data' => null,
            'file_data' => [
                'path' => storage_path('/app/temp/'. $fileName),
                'file_name' => $fileName,
                'headers' => $headers
            ]
        ]; 
    }  
    
    public function generateDataArray(Array $organizationUnits) : array {
        $data = [];

        foreach($organizationUnits as $organizationUnit){
            $organizationUnitArray = [];
            $dbOrganizationUnit = OrganizationUnit::findOrFail($organizationUnit);
            $organizationUnitArray = [
                'id' => $dbOrganizationUnit->id,
                'name' => $dbOrganizationUnit->name,
                'users' => null
            ];

            $users = $dbOrganizationUnit->users;

            foreach($users as $user){
                if($user->getEditable() and $user->active_directory == 1){
                    $usersArray = [];
                    $usersArray = [
                        'full_name' => $user->getFullname(),
                        'password' => $this->generatePassword(),
                        'phone' => $user->phone,
                        'account_name' => $user->account_name
                    ];
                }

                $organizationUnitArray['users'][] = $usersArray;
            }

            $data[] = $organizationUnitArray;
        }

        return $data;    
    }

    public function generatePassword($lengh = 8): string{
        $password = '';
        $characters = '**********abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        for ($i = 0; $i < $lengh; $i++) {
            $password .= $characters[rand(0, $charactersLength - 1)];
        }
        $characters = '**********';
        $charactersLength = strlen($characters);
        for ($i = 0; $i < 2; $i++) {
            $password .= $characters[rand(0, $charactersLength - 1)];
        }
        return $password.'@';
    }


}

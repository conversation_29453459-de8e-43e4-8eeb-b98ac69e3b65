<?php namespace App\Services\OrganizationUnit;
use Illuminate\Http\Request;

use App\Models\OrganizationUnit;
use App\Models\User;

use App\Http\Resources\User\UserCollection;

class OrganizationUnitCheckPhoneService {
    public function processPostRequest(Request $request): Array {

        $organizationUnits = $request->input('organization_units');

        return $this->process($organizationUnits);  
    }

    public function process(Array $organizationUnits) : array {
        $failed = false;
        $failedData = [];

        foreach($organizationUnits as $organizationUnit){
            $usersCount = 0;
            $usersWithoutPhone = User::where('organization_unit','=',$organizationUnit)->whereNull('phone')->get();
            $usersCount = $usersWithoutPhone->count();

            if($usersCount != 0){
                $failed = true;
                $dbOrganizationUnit = OrganizationUnit::findOrFail($organizationUnit);
                $organizationUnitArray = [];
                $organizationUnitArray = [
                    'id' => $dbOrganizationUnit->id,
                    'name' => $dbOrganizationUnit->name,
                    'users' => new UserCollection($usersWithoutPhone)
                ];

                $failedData[] = $organizationUnitArray;
            }

        }

        if($failed){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Některý z uživatelů nemá nastavené tel.číslo.',
                'custom_data' => $failedData
            ]; 
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Kontrola tel.čísel proběhla úspěšně.',
            'custom_data' => null
        ]; 

    }    

}

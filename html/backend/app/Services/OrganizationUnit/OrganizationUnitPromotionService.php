<?php namespace App\Services\OrganizationUnit;
use Illuminate\Http\Request;

use App\Models\OrganizationUnit;
use App\Services\OrganizationUnit\AdOrganizationUnitsSyncService;

use Adldap;
use DB;

class OrganizationUnitPromotionService {
    public function processPostRequest(Request $request): Array {

        $organizationUnits = $request->input('organization_units');

        return $this->process($organizationUnits);  
    }

    public function process(Array $organizationUnits) : array {

    //udelam si seznam IDcek z requestu
    $organizationUnitIds = [];
    foreach($organizationUnits as $organizationUnit){
        $organizationUnitIds[] = $organizationUnit['id'];
    }

    //vyberu objekkty podle IDcek z requestu
    $organizationUnitsSelectedCollection = OrganizationUnit::whereIn('id', $organizationUnitIds)->get();

    //Projdu jestli vsechny zvolene jsou povysitelne
    if($this->checkPromotion($organizationUnitsSelectedCollection)){
        return [
            'success' => false,
            'status_code' => null,
            'custom_code' => null,
            'message' => 'Některá z organižačních jendotek není povýšitelná.',
            'custom_data' => null,
        ]; 
    }


    //Vytahnu vsechny do pole
    $organizationUnitsAllArray = OrganizationUnit::get()->toArray();

    //Nastavim poli budouci hodnoty
    $ouWithNewNameArray = $this->setNewName($organizationUnitsAllArray, $organizationUnits);

    //Projdu pole jestli by vznikly budouci duplicity, kdyz ano vratim ID tech ktere by to byly
    $duplicityData = $this->checkDuplicity($ouWithNewNameArray);

    if(count($duplicityData) != 0){
        return [
            'success' => false,
            'status_code' => null,
            'custom_code' => 42201,
            'message' => 'Povýšení se nezdařilo s důvodu duplicity. Zkontrolujte prosím nové názvy.',
            'custom_data' => [
                'failed' => $duplicityData
            ]
        ]; 
    }

    // Nastavmie vsechny s novym jmenem ale TEMP prefixem kvuli duplicit
    foreach($organizationUnits as $organizationUnit){
        $dbOrganizationUnit = null;
        $dbOrganizationUnit = OrganizationUnit::where('id', $organizationUnit['id'])->first();

        $dn = $dbOrganizationUnit->distinguished_name;
        $adOu = Adldap::search()->ous()->where('distinguishedname', '=', $dn)->first();   
        $newName = "OU=TEMP_".$organizationUnit['new_name'];

        if($adOu->rename($newName)) {
            $dbOrganizationUnit->name = "TEMP_".$organizationUnit['new_name'];
            $dbOrganizationUnit->save();

        } else {
            $service = New AdOrganizationUnitsSyncService();
            $service->process();

            return [
                'success' => false,
                'status_code' => null,
                'message' => 'V průběhu povýšení organizačnich jednotek se vyskytla chyba.',
                'custom_code' => null,
                'custom_data' => null
            ]; 
        }  
    }

    // resyncneme kvuli DB
    $service = New AdOrganizationUnitsSyncService();
    $service->process();

    // zmena na final name
    foreach($organizationUnits as $organizationUnit){
        $dbOrganizationUnit = null;
        $dbOrganizationUnit = OrganizationUnit::where('id', $organizationUnit['id'])->first();

        $dn = $dbOrganizationUnit->distinguished_name;
        $adOu = Adldap::search()->ous()->where('distinguishedname', '=', $dn)->first();   
        $newName = "OU=".$organizationUnit['new_name'];

        if($adOu->rename($newName)) {
            $dbOrganizationUnit->name = $organizationUnit['new_name'];
            $dbOrganizationUnit->save();

        } else {
            $service = New AdOrganizationUnitsSyncService();
            $service->process();

            return [
                'success' => false,
                'status_code' => null,
                'message' => 'V průběhu povýšení organizačnich jednotek se vyskytla chyba.',
                'custom_code' => null,
                'custom_data' => null
            ]; 
        }   
    }

    // a zase Resync
    $service = New AdOrganizationUnitsSyncService();
    $service->process();

    return [
        'success' => true,
        'status_code' => 200,
        'message' => 'Organizační jednotky povýšeny.',
        'custom_data' => null
    ]; 

    }  
    
    public function checkPromotion($organizationUnits){
        $error = false;
        foreach($organizationUnits as $organizationUnit){
            if($organizationUnit->promotion == 0){
                $error = true;
                break;
            }
        }

        return $error;
    }

    public function setNewName($organizationUnitsAllArray, $organizationUnits){
        $i = 0;
        foreach($organizationUnitsAllArray as $item){
            foreach($organizationUnits as $newItem){
                if($item['id'] == $newItem['id']){
                    $organizationUnitsAllArray[$i]['name'] = $newItem['new_name'];
                }
            }
            $i++;
        }

        return $organizationUnitsAllArray;
    }

    /*
    public function checkDuplicity($array){
        $helperArray = $array;
        $failedIdArray = [];
        foreach($array as $item){
            foreach($helperArray as $checkedItem){
                if(($item['name'] == $checkedItem['name']) and ($item['parent'] == $checkedItem['parent']) and ($item['id'] != $checkedItem['id'])){
                    $failedIdArray[] = $item['id']; 
                }
            }
        }

        return $failedIdArray;
    }
    */

    public function checkDuplicity($array) {
        $seenItems = array();
        $failedItems = array();
        $promotionFailedItemsId = array();
    
        foreach ($array as $item) {
            $key = $item['name'] . '|' . $item['parent'];
    
            if (isset($seenItems[$key])) {
                $existingItem = $seenItems[$key];
                if (!in_array($existingItem, $failedItems)) {
                    $failedItems[] = $existingItem;
                }
                if (!in_array($item, $failedItems)) {
                    $failedItems[] = $item;
                }
            } else {
                $seenItems[$key] = $item;
            }
        }
    
        /*
            Ted vyselektuju jen ty co se promotujou, ty ostatni stejne nemuzu ovlivnit z promotions
        */

        foreach($failedItems as $item){
            if($item['promotion'] == 1){
                $promotionFailedItemsId[] = $item['id'];
            }
        }
            
        return $promotionFailedItemsId;
    }
}

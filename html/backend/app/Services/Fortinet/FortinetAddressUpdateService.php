<?php namespace App\Services\Fortinet;

use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;

use App\Models\InternetBlock;

use Carbon\Carbon;

use App\Jobs\FortinetAddressUpdate;

class FortinetAddressUpdateService {
    public function process() {     
        $now = Carbon::now()->toDateTimeString();
        $macAddressesArray = [];

        $blockedAccounts = InternetBlock::with(['macAddresses'])
                    ->where('from','<',$now)
                    ->where('to','>',$now)
                    ->orWhere('permanent',1)
                    ->get();

        foreach($blockedAccounts as $blockedAccount){
            foreach ($blockedAccount?->macAddresses as $macAddresses) {
                $macAddressesArray[] = $macAddresses?->mac_address;
            };

        }

        FortinetAddressUpdate::dispatch($macAddressesArray)->onQueue('medium');

    }
}

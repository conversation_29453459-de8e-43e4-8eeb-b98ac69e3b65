<?php namespace App\Services\Fortinet;

use SimpleXMLElement;

use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;

use App\Models\FortinetLog;
use App\Models\AccountNameMacAddress;

class FortinetLogService {
    public function processPostRequest(Request $request): Array {
        $data = $request->getContent();
        return $this->process($data );  
    }

    public function process($data ) : array {       
        $xml = new SimpleXMLElement($data);
        $data = [
            'account_name' => (string)$xml->{'User-Name'},
            'calling_station_id' => (string)$xml->{'Calling-Station-Id'},
            'acct_status_type' => (string)$xml->{'Acct-Status-Type'},
        ];       
        
        if($data['account_name'] != "" and $data['calling_station_id'] != ""){
            $replace = array(":", "-");
            $data['calling_station_id'] = str_replace($replace, "", $data['calling_station_id']);
            $parsed = explode("\\", $data['account_name']);
            $data['account_name'] = end($parsed);

            FortinetLog::create($data);

            $this->checkFortinetData($data['account_name'], $data['calling_station_id']);
        }

        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Blokace zadány.'
        ]; 

    }

    public function checkFortinetData($accountName, $macAddress){
        /*
            Overuju jestli existuje MAC adresa u nas v DB,
            kdyz neexistuje, pridam ji tomuto uzivateli.

            Kdyz exituje resim dalsi podminku.
        */
        $existingAccountNameMacAddress = AccountNameMacAddress::where('mac_address', $macAddress)->first();
        if(is_null($existingAccountNameMacAddress)){
            AccountNameMacAddress::create(['account_name' => $accountName, 'mac_address' => $macAddress]);

        } else{
            /*
                Když MAC adresa existuje už v naší DB ale není u tohoto uživatele
                tak přidáme tomuto uživateli,
                odstraníme u toho kdo ji má

                Kdyz existuje a je u tohoto uzivatele, neni treba nic delat
            */
           if($existingAccountNameMacAddress->account_name != $accountName){
                $existingAccountNameMacAddress->delete();
                AccountNameMacAddress::create(['account_name' => $accountName, 'mac_address' => $macAddress]);
           }
        }

    }
}

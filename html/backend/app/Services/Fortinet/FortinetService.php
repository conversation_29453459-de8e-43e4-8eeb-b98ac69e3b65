<?php namespace App\Services\Fortinet;

use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class FortinetService {

    private $ip = null;
    private $token = null;

    function __construct() {
        $this->ip = config('system.fortinet.ip');
        $this->token = config('system.fortinet.token');
    }

    public function get($url) : array {
        try {
            $response = Http::withToken($this->token)
            ->withOptions(['verify' => false])
            ->get("https://".$this->ip.$url);

            $data = $response->body();
            $dataJson = json_decode($data);
  
            if(!isset($dataJson->http_status) || $dataJson->http_status != 200){
                return [
                    'success' => false,
                    'status_code' => null,
                    'custom_code' => 40099,
                    'message' => 'Získaní dat z Frotinet selhalo.',
                ]; 
            }
            
            return [ 
                'success' => true,
                'status_code' => 200,
                'message' => 'Data vrácena.',
                'custom_data' => json_decode($data)
            ]; 


        } catch(\Illuminate\Http\Client\ConnectionException $e)  {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Připojení k Fortinet selhalo.',
                'custom_code' => 40099,
                'custom_data' => null,
            ]; 
        } catch (\Throwable $e) {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Získaní dat z Frotinet selhalo.',
                'custom_code' => 40099,
                'custom_data' => null,
            ]; 
        }
    }

    public function post($url, $params = null, $body = null) : array {
        try {
            $response = Http::withToken($this->token)
            ->withOptions(['verify' => false])
            ->withUrlParameters($params)
            ->withBody($body)
            ->post("https://".$this->ip.$url);

            $data = $response->body();
            return [
                'success' => true,
                'status_code' => 200,
                'message' => 'Data vrácena.',
                'custom_data' => json_decode($data)
            ]; 


        } catch(\Illuminate\Http\Client\ConnectionException $e)  {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Připojení k Fortinet selhalo.',
                'custom_code' => null,
                'custom_data' => null,
            ]; 
        } catch(\Throwable $e)  {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Zapsání dat do Fortinet selhalo.',
                'custom_code' => null,
                'custom_data' => null,
            ]; 
        }
    }

    public function put($url, $params = null, $body = null) : array {
        try {
            $response = Http::withToken($this->token)
            ->withOptions(['verify' => false])
            ->withUrlParameters($params)
            ->withBody($body)
            ->put("https://".$this->ip.$url);

            $data = $response->body();
            $dataJson = json_decode($data);
  
            if(!isset($dataJson->http_status) || $dataJson->http_status != 200){
                return [
                    'success' => false,
                    'status_code' => null,
                    'custom_code' => 40099,
                    'message' => 'Zapsání dat do Fortinet selhalo.',
                ]; 
            }
            
            return [ 
                'success' => true,
                'status_code' => 200,
                'message' => 'Zapsání dat bylo úspěšné.',
                'custom_data' => json_decode($data)
            ]; 


        } catch(\Illuminate\Http\Client\ConnectionException $e)  {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Připojení k Fortinet selhalo.',
                'custom_code' => null,
                'custom_data' => null
            ]; 
        } catch(\Throwable $e)  {
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Zapsání dat do Fortinet selhalo.',
                'custom_code' => null,
                'custom_data' => null
            ]; 
        }
    }
}

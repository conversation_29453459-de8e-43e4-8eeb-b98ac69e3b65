<?php namespace App\Services\Auth;

use Illuminate\Http\Request;

use App\Models\VerifyEmailToken;
use App\Models\User;

use Illuminate\Support\Facades\Mail;
use App\Mail\Auth\EmailVerifyMail;

class SendVerifyEmailService {
    public function processPostRequest(Request $request, Bool $mail = false): Array {
        $data = [
           'email' =>  $request->input('email')
        ];

        return $this->process($data, $mail);  
    }

    public function process(Array $data, Bool $mail = false) : Array {
        $user = User::where('email', $data['email'])->first();

        if($user != null){
            $token = generateRandomString(64);

            $tokenData = ['email' => $user->email, 'token' => $token];
            VerifyEmailToken::create($tokenData);
    
            if($mail){
                Mail::to($user->email)->queue(new EmailVerifyMail($user,$token));
            }
        }

        return [
            'success' => true,
            'message' => 'Ověřovací email byl odeslán.',
            'custom_data' => [
                'token' => $token
            ]
        ]; 

    }
}


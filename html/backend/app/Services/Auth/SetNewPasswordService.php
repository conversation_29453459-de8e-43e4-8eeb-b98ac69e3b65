<?php namespace App\Services\Auth;

use Illuminate\Http\Request;

use App\Models\PasswordResetToken;

use Carbon\Carbon;
use Hash;

class SetNewPasswordService {
    public function processPostRequest(Request $request) : Array {
        $data = [
           'token'          =>  $request->input('token'),
           'email'          =>  $request->input('email'),
           'new_password'   =>  $request->input('new_password')
        ];

        return $this->process($data);  
    }

    public function process(Array $data) : Array {

        $resetPasswordToken = PasswordResetToken::where('token', $data['token'])->where('email', $data['email'])->first();
        if(is_null($resetPasswordToken)){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Neplatný token.',
            ];   
        }

        $user = $resetPasswordToken->user;
        $newerResetPasswordToken  = PasswordResetToken::where('email', $user->email)->where('id','>',$resetPasswordToken->id)->first();
        if(!is_null($newerResetPasswordToken)){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Neplatný token.',
            ];   
        }

        if(config('system.RESET_PASSWORD_TOKEN_LIFETIME') != 0){
            $createdDateTime = Carbon::create($resetPasswordToken->created_at);
            $createdDateTime = $createdDateTime->addMinutes(config('system.RESET_PASSWORD_TOKEN_LIFETIME'));
            if($createdDateTime < Carbon::now()){
                return [
                    'success' => false,
                    'status_code' => null,
                    'message' => 'Token vypršel.',
                ];   
            }
        }

        
        $user->password = Hash::make($data['new_password']);
        $user->save();

        PasswordResetToken::where('email', $resetPasswordToken->email)->delete();
        
        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Heslo bylo nastaveno, můžete se přihlásit.',
        ]; 


    }
}


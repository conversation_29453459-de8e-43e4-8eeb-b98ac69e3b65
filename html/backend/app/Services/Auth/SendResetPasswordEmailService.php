<?php namespace App\Services\Auth;

use Illuminate\Http\Request;

use App\Models\PasswordResetToken;
use App\Models\User;

use Illuminate\Support\Facades\Mail;
use App\Mail\Auth\ResetPasswordMail;

class SendResetPasswordEmailService {
    public function processPostRequest(Request $request, Bool $mail = false): Array {
        $data = [
           'email' =>  $request->input('email')
        ];

        return $this->process($data, $mail);  
    }

    public function process(Array $data, Bool $mail = false) : Array {
        $user = User::where('email', $data['email'])->first();

        if($user != null){
            $token = generateRandomString(64);

            $tokenData = ['email' => $user->email, 'token' => $token];
            PasswordResetToken::create($tokenData);
    
            if($mail){
                Mail::to($user->email)->queue(new ResetPasswordMail($user,$token));
            }
        }

        return [
            'success' => true,
            'message' => 'Email k resetování hesla byl odeslán.'
        ]; 

    }
}


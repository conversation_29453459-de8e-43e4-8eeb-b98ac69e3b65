<?php namespace App\Services\Auth;

use Illuminate\Http\Request;

use App\Models\VerifyEmailToken;

use Carbon\Carbon;

class VerifyEmailService {

    public function process(String $token) : Array {

        $verifyToken = VerifyEmailToken::where('token', $token)->first();
        if(is_null($verifyToken)){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Neplatný token.',
            ];   
        }

        $user = $verifyToken->user;
        $newerVerifyEmailToken = VerifyEmailToken::where('email', $user->email)->where('id','>',$verifyToken->id)->first();

        if(!is_null($newerVerifyEmailToken)){
            return [
                'success' => false,
                'status_code' => null,
                'message' => 'Neplatný token.',
            ];  
        }


        if(config('system.EMAIL_VERIFICATION_TOKEN_LIFETIME') != 0){
            $createdDateTime = Carbon::create($verifyToken->created_at);
            $createdDateTime = $createdDateTime->addMinutes(config('system.EMAIL_VERIFICATION_TOKEN_LIFETIME'));
            if($createdDateTime < Carbon::now()){
                return [
                    'success' => false,
                    'status_code' => null,
                    'message' => 'Token vypršel.',
                ];   
            }
        }


        $user->email_verified_at = Carbon::now();
        $user->save();

        VerifyEmailToken::where('email', $verifyToken->email)->delete();
        
        return [
            'success' => true,
            'status_code' => 200,
            'message' => 'Email byl oveřen, můžete se přihlásit.',
        ]; 


    }
}


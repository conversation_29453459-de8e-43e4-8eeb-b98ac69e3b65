<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

use Illuminate\Auth\AuthenticationException;
use Symfony\Component\ErrorHandler\ErrorRenderer\HtmlErrorRenderer;
use Symfony\Component\ErrorHandler\Exception\FlattenException;

use Illuminate\Support\Facades\Http;
 

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function report(Throwable $exception)
    {
  
        //Log::error($exception); // zapsat do logu

        if(config('app.debug') == false){
            $content = $exception;
            $url = \Request::fullUrl();
 
            if ($this->shouldReport($exception)) {
                $this->sendEmail($exception, $content, $url, config('system.control_system.private')); // sends an email
            }
        }

        return parent::report($exception);

    }

    protected function unauthenticated($request, AuthenticationException $exception)
    {
        /*
        return $request->expectsJson()
                    ? response()->json(['message' => "unauthenticated"], 401)
                    : redirect()->guest(route('login'));
        */
        return response()->json(['message' => "unauthenticated"], 401);
    }

    public function sendEmail(Throwable $exception, $content, $url, $private)
    {

        \Mail::mailer('smtp')->send('mail.exception', compact('content','url','private'), function ($message) {
            $message->to(['<EMAIL>'])
                ->subject('Error: školní systém AD zaznamenal chybu.');
        });

    }

}

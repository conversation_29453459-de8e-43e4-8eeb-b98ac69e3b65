<?php

namespace App\Policies;

use App\Models\Room;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class RoomPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function store(User $user): bool
    {
        if($user->can('property.master')){
            return true;
        }

        if ($user->can('rooms.create')) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view any models.
     */
    public function read(User $user): bool
    {
        if($user->can('property.master')){
            return true;
        }

        if ($user->can('rooms.read')) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Room $room): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('rooms.read')){
                if($user->id === $room->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Room $room): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('rooms.edit')){
                if($user->id === $room->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Room $room): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('rooms.delete')){
                if($user->id === $room->user_id){
                    return true;
                }
            }    
        }
        return false;
    }
}

<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

class TimetablePolicy
{
    /**
     * Profilove
     */
    public function read(User $user): bool
    {
        if ($user->can('timetables.read')) {
            return true;
        }
        return false;
    }

    public function update(User $user): bool
    {
        if ($user->can('timetables.edit')) {
            return true;
        }
        return false;
    }
   
}

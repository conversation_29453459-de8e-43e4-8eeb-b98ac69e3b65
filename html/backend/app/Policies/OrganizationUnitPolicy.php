<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

class OrganizationUnitPolicy
{
    /**
     * Profilove
     */
    public function read(User $user): bool
    {
        if ($user->can('active_directory_ou.read')) {
            return true;
        }
        return false;
    }

    public function store(User $user): bool
    {
        if ($user->can('active_directory_ou.create')) {
            return true;
        }
        return false;
    }

    public function update(User $user): bool
    {
        if ($user->can('active_directory_ou.edit')) {
            return true;
        }
        return false;
    }

    public function destroy(User $user): bool
    {
        if ($user->can('active_directory_ou.delete')) {
            return true;
        }
        return false;
    }

    public function promotion(User $user): bool
    {
        if ($user->can('active_directory_ou.promotion')) {
            return true;
        }
        return false;
    }

    public function userPasswordUpdate(User $user): bool
    {
        if($user->can('users.set_password')) {
            return true;
        }
        return false;
    }


    public function sync(User $user): bool
    {
        if ($user->can('active_directory.sync')) {
            return true;
        }
        return false;
    }
   
}

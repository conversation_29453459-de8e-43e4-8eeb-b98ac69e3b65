<?php

namespace App\Policies;

use App\Models\Ticket;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class TicketPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function store(User $user): bool
    {
        if($user->can('tickets.master')){
            return true;
        }

        if ($user->can('tickets.create')) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view any models.
     */
    public function read(User $user): bool
    {
        if($user->can('tickets.master')){
            return true;
        }

        if ($user->can('tickets.read')) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Ticket $ticket): bool
    {
        if($user->can('tickets.master')){
            return true;
        } else {
            if($user->can('tickets.read')){
                if($user->id === $ticket->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Ticket $ticket): bool
    {
        if($user->can('tickets.master')){
            return true;
        } else {
            if($user->can('tickets.edit')){
                if($user->id === $ticket->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Ticket $ticket): bool
    {
        if($user->can('tickets.master')){
            return true;
        } else {
            if($user->can('tickets.delete')){
                if($user->id === $ticket->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

        /**
     * Determine whether the user can delete the model.
     */
    public function close(User $user, $ticket = null): bool
    {
        if($user->can('tickets.master')){
            return true;
        } else {
            if($user->can('tickets.close')){
                if(!is_null($ticket)){
                    if($user->id === $ticket->user_id){
                        return true;
                    }
                } else {
                    return true;
                }
            }    
        }
        return false;
    }
}

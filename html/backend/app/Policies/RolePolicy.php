<?php

namespace App\Policies;

use App\Models\Role;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class RolePolicy
{

    public function read(User $user): bool
    {
        if ($user->can('roles.read')) {
            return true;
        }
        return false;
    }

    public function store(User $user): bool
    {
        if ($user->can('roles.create')) {
            return true;
        }
        return false;
    }

    public function update(User $user): bool
    {
        if ($user->can('roles.edit')) {
            return true;
        }
        return false;
    }

    public function destroy(User $user): bool
    {
        if ($user->can('roles.delete')) {
            return true;
        }
        return false;
    }

   
}

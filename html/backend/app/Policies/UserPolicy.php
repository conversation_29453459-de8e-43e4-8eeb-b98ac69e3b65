<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

class UserPolicy
{
    /**
     * Profilove
     */
    public function profil(User $user): bool
    {
        if ($user->can('profil.read')) {
            return true;
        }
        return false;
    }

    public function profilUpdate(User $user): bool
    {
        if ($user->can('profil.edit')) {
            return true;
        }
        return false;
    }

    public function profilPasswordUpdate(User $user): bool
    {
        if ($user->can('profil.change_password')) {
            return true;
        }
        return false;
    }

    public function profilEmailUpdate(User $user): bool
    {
        if ($user->can('profil.change_email')) {
            return true;
        }
        return false;
    }

    /**
     * User - admin
     */

    public function read(User $user): bool
    {
        if ($user->can('users.read')) {
            return true;
        }
        return false;
    }

    public function rolePermissionShow(User $user): bool
    {
        if ($user->can('users.read_roles_permissions')) {
            return true;
        }
        return false;
    }

    

    public function store(User $user): bool
    {
        if ($user->can('users.create')) {
            return true;
        }
        return false;
    }

    public function update(User $user, $targetUser = null): bool
    {
        if ($user->can('users.edit')) {

            if(!is_null($targetUser)){
                $userTopPriority = $user->getTopRolePriority();
                $targetUserTopPriority = $targetUser->getTopRolePriority();

                if($userTopPriority < $targetUserTopPriority){
                    return true;
                } else {
                    return false;
                }
            }

            return true;
        }
        return false;
    }

    public function destroy(User $user, User $targetUser): bool
    {
        if ($user->can('users.delete')) {

            $userTopPriority = $user->getTopRolePriority();
            $targetUserTopPriority = $targetUser->getTopRolePriority();

            if($userTopPriority < $targetUserTopPriority){
                return true;
            } else {
                return false;
            }

            return true;
        }
        return false;
    }

    public function userPasswordUpdate(User $user, $targetUser = null): bool
    {
        if ($user->can('users.set_password')) {

            if(!is_null($targetUser)){
                $userTopPriority = $user->getTopRolePriority();
                $targetUserTopPriority = $targetUser->getTopRolePriority();
    
                if($userTopPriority < $targetUserTopPriority){
                    return true;
                } else {
                    return false;
                }
            }

            return true;
        }
        return false;
    }

    public function rolesUpdate(User $user, User $targetUser): bool
    {
        if ($user->can('users.update_roles')) {
            $userTopPriority = $user->getTopRolePriority();
            $targetUserTopPriority = $targetUser->getTopRolePriority();

            if($userTopPriority < $targetUserTopPriority){
                return true;
            } else {
                return false;
            }

            return true;
        }
        return false;
    }

    public function permissionsUpdate(User $user, User $targetUser): bool
    {
        if ($user->can('users.update_permissions')) {
            $userTopPriority = $user->getTopRolePriority();
            $targetUserTopPriority = $targetUser->getTopRolePriority();

            if($userTopPriority < $targetUserTopPriority){
                return true;
            } else {
                return false;
            }

            return true;
        }
        return false;
    }

    public function sync(User $user): bool
    {
        if ($user->can('active_directory.sync')) {
            return true;
        }
        return false;
    }

   
}

<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

class BackupPolicy
{
    /**
     * Profilove
     */
    public function read(User $user): bool
    {
        if ($user->can('backup.read')) {
            return true;
        }
        return false;
    }

    public function download(User $user): bool
    {
        if ($user->can('backup.download')) {
            return true;
        }
        return false;
    }
   
}
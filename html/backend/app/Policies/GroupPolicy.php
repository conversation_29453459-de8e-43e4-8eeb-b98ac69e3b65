<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

class GroupPolicy
{
    /**
     * Profilove
     */
    public function read(User $user): bool
    {
        if ($user->can('active_directory_group.read')) {
            return true;
        }
        return false;
    }

    public function sync(User $user): bool
    {
        if ($user->can('active_directory.sync')) {
            return true;
        }
        return false;
    }
   
}

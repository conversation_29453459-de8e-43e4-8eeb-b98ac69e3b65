<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

class InternetBlockPolicy
{
    /**
     * Profilove
     */
    public function read(User $user): bool
    {
        if ($user->can('internet_blocks.read')) {
            return true;
        }
        return false;
    }

    public function store(User $user): bool
    {
        if ($user->can('internet_blocks.create')) {
            return true;
        }
        return false;
    }

    public function destroy(User $user): bool
    {
        if ($user->can('internet_blocks.delete')) {
            return true;
        }
        return false;
    }
   
}

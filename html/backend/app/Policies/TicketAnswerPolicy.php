<?php

namespace App\Policies;

use App\Models\TicketAnswer;
use App\Models\Ticket;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class TicketAnswerPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function store(User $user, Ticket $ticket): bool
    {
        if($user->can('tickets.master')){
            return true;
        }

        if ($user->can('tickets.create')) {
            if($ticket->user->id == $user->id){
                return true;
            }
        }
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TicketAnswer $ticketAnswer): bool
    {
        if($user->can('tickets.master')){
            return true;
        } else {
            if($user->can('tickets.edit')){
                if($user->id === $ticketAnswer->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TicketAnswer $ticketAnswer): bool
    {
        if($user->can('tickets.master')){
            return true;
        } else {
            if($user->can('tickets.delete')){
                if($user->id === $ticketAnswer->user_id){
                    return true;
                }
            }    
        }
        return false;
    }
}

<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

class SettingsPolicy
{
    /**
     * Profilove
     */
    public function read(User $user): bool
    {
        if ($user->can('settings.read')) {
            return true;
        }
        return false;
    }

    public function update(User $user): bool
    {
        if ($user->can('settings.edit')) {
            return true;
        }
        return false;
    }
   
}

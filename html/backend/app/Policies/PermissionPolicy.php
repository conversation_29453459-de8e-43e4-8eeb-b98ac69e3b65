<?php

namespace App\Policies;

use App\Models\Permission;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class PermissionPolicy
{

    public function read(User $user): bool
    {
        if ($user->can('permissions.read')) {
            return true;
        }
        return false;
    }

    public function store(User $user): bool
    {
        if ($user->can('permissions.create')) {
            return true;
        }
        return false;
    }

    public function update(User $user): bool
    {
        if ($user->can('permissions.edit')) {
            return true;
        }
        return false;
    }

    public function destroy(User $user): bool
    {
        if ($user->can('permissions.delete')) {
            return true;
        }
        return false;
    }

   
}

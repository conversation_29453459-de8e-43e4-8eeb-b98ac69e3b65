<?php

namespace App\Policies;

use App\Models\Item;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ItemPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function store(User $user): bool
    {
        if($user->can('property.master')){
            return true;
        }

        if ($user->can('items.create')) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view any models.
     */
    public function read(User $user): bool
    {
        if($user->can('property.master')){
            return true;
        }

        if ($user->can('items.read')) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Item $item): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('items.read')){
                if($user->id === $item->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Item $item): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('items.edit')){
                if($user->id === $item->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Item $item): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('items.delete')){
                if($user->id === $item->user_id){
                    return true;
                }
            }    
        }
        return false;
    }
}

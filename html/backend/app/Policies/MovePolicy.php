<?php

namespace App\Policies;

use App\Models\Move;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class MovePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function store(User $user): bool
    {
        if($user->can('property.master')){
            return true;
        }

        if ($user->can('moves.create')) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view any models.
     */
    public function read(User $user): bool
    {
        if($user->can('property.master')){
            return true;
        }

        if ($user->can('moves.read')) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Move $move): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('moves.read')){
                if($user->id === $move->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Move $move): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('moves.edit')){
                if($user->id === $move->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Move $move): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('moves.delete')){
                if($user->id === $move->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    public function process(User $user, Move $move): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('moves.process')){
                if(!is_null($move)){
                    if($user->id === $move->user_id){
                        return true;
                    }
                } else {
                    return true;
                }
            }    
        }
        return false;
    }

    public function items(User $user): bool
    {
        if($user->can('property.master')){
            return true;
        }

        if ($user->can('moves.items')) {
            return true;
        }
        return false;
    }
}

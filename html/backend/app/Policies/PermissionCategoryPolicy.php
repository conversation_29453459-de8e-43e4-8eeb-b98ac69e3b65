<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

class PermissionCategoryPolicy
{

    public function read(User $user): bool
    {
        if ($user->can('permissions_categories.read')) {
            return true;
        }
        return false;
    }

    public function store(User $user): bool
    {
        if ($user->can('permissions_categories.create')) {
            return true;
        }
        return false;
    }

    public function update(User $user): bool
    {
        if ($user->can('permissions_categories.edit')) {
            return true;
        }
        return false;
    }

    public function destroy(User $user): bool
    {
        if ($user->can('permissions_categories.delete')) {
            return true;
        }
        return false;
    }

   
}

<?php

namespace App\Policies;

use App\Models\Inventory;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class InventoryPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function store(User $user): bool
    {
        if($user->can('property.master')){
            return true;
        }

        if ($user->can('inventories.create')) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view any models.
     */
    public function read(User $user): bool
    {
        if($user->can('property.master')){
            return true;
        }

        if ($user->can('inventories.read')) {
            return true;
        }
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Inventory $inventory): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('inventories.read')){
                if($user->id === $inventory->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Inventory $inventory): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('inventories.edit')){
                if($user->id === $inventory->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Inventory $inventory): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('inventories.delete')){
                if($user->id === $inventory->user_id){
                    return true;
                }
            }    
        }
        return false;
    }

    public function process(User $user, Inventory $inventory): bool
    {
        if($user->can('property.master')){
            return true;
        } else {
            if($user->can('inventories.process')){
                if(!is_null($inventory)){
                    if($user->id === $inventory->user_id){
                        return true;
                    }
                } else {
                    return true;
                }
            }    
        }
        return false;
    }

    public function items(User $user): bool
    {
        if($user->can('property.master')){
            return true;
        }

        if ($user->can('inventories.items')) {
            return true;
        }
        return false;
    }
}

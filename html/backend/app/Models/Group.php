<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\Builder;

class Group extends Model
{
    use HasFactory;

    protected $fillable = [
        'guid',
        'name',
        'distinguished_name',
        'description',
        'default',
        'visitors'
    ];

    public function parentGroups()
    {
        return $this->belongsToMany(Group::class,'group_groups','group_id','parent_group_id')->withTimestamps();
    }

    public function childrenGroups()
    {
        return $this->belongsToMany(Group::class,'group_groups','parent_group_id','group_id')->withTimestamps();
    }

    public function scopeSearch(Builder $query, $search = null): void
    {
        if(isset($search)){
            $query->where(function ($query) use ($search){
                $query
                ->where('name', 'LIKE', '%' . $search . '%');
            });
        }
    }

    public function scopeExcludedName(Builder $query): void
    {
        $excludedNames = [
            "Access Control Assistance Operators",
            "Account Operators",
            "Administrators",
            "Allowed RODC Password Replication",
            "Allowed RODC Password Replication Group",
            "Backup Operators",
            "Certificate Service DCOM Access",
            "Cert Publishers",
            "Cloneable Domain Controllers",
            "Cryptographic Operators",
            "Denied RODC Password Replication",
            "Device Owners",
            "DHCP Administrators",
            "DHCP Users",
            "Distributed COM Users",
            "DnsUpdateProxy",
            "DnsAdmins",
            "Domain Admins",
            "Domain Computers",
            "Domain Controllers",
            "Domain Guests",
            "Domain Users",
            "Enterprise Admins",
            "Enterprise Key Admins",
            "Enterprise Read-only Domain Controllers",
            "Event Log Readers",
            "Group Policy Creator Owners",
            "Guests",
            "Hyper-V Administrators",
            "IIS_IUSRS",
            "Incoming Forest Trust Builders",
            "Key Admins",
            "Network Configuration Operators",
            "Performance Log Users",
            "Performance Monitor Users",
            "Pre–Windows 2000 Compatible Access",
            "Print Operators",
            "Protected Users",
            "RAS and IAS Servers",
            "RDS Endpoint Servers",
            "RDS Management Servers",
            "RDS Remote Access Servers",
            "Read-only Domain Controllers",
            "Remote Desktop Users",
            "Remote Management Users",
            "Replicator",
            "Schema Admins",
            "Server Operators",
            "Storage Replica Administrators",
            "System Managed Accounts",
            "Terminal Server License Servers",
            "Users",
            "Windows Authorization Access",
            "WinRMRemoteWMIUsers_",
            "Správci technologie Hyper-V",
            "Pre-Windows 2000 Compatible Access",
            "Windows Authorization Access Group",
            "Allowed RODC Password Replication Group",
            "Denied RODC Password Replication Group",
            "Klonovatelné řadiče domény"
        ];


        $query->whereNotIn('name', $excludedNames);

    }
}

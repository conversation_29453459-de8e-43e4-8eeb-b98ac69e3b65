<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Models\Role;
use App\Models\OrganizationUnit;

use Carbon\Carbon;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;
    use HasRoles;
    use SoftDeletes;

    protected $table = 'users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'middle_name',
        'last_name',
        'degree_before',
        'degree_after',
        'password',
        'email',
        'phone',
        'email_verified_at',
        'active_directory',
        'guid',
        'account_name',
        'distinguished_name',
        'organization_unit',
        'expire',
        'visitor',
        'account_control_code',
        'profile_path', 
        'script_path',
        'home_directory',
        'home_drive'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    public function getTopRolePriority(){
        $count = count($this->roles);
        if($count == 0){
            $dbRoleLowPriority = Role::max('role_priority');
            return $dbRoleLowPriority+1;
        } else {
            return $this->roles->min('role_priority');
        }

    }

    public function organizationUnit(): BelongsTo{
        return $this->belongsTo(OrganizationUnit::class, 'organization_unit');
    }

    public function groups(): BelongsToMany{
        return $this->belongsToMany(Group::class, 'user_groups', 'user_id', 'group_id')->withTimestamps();
    }

    public function accountControlCode(): BelongsTo{
        return $this->belongsTo(AccountControlCode::class, 'account_control_code');
    }

    public function internetBlocks(): HasMany{
        return $this->hasMany(InternetBlock::class, 'user_id', 'id');
    }

    public function activeInternetBlocks(): HasMany{
        $now = Carbon::now()->toDateTimeString();
        return $this->hasMany(InternetBlock::class, 'user_id', 'id')->where(function ($query) use($now) {
            $query->where('from','<',$now)
            ->where('to','>',$now)
            ->orWhere('permanent',1);
        });

    }

    public function rooms(): HasMany
    {
        return $this->hasMany(Room::class, 'user_id', 'id');
    }

    public function moves(): HasMany
    {
        return $this->hasMany(Move::class, 'user_id', 'id');
    }

    public function inventories(): HasMany
    {
        return $this->hasMany(Inventory::class, 'user_id', 'id');
    }

    public function items(): HasMany
    {
        return $this->hasMany(Item::class, 'user_id', 'id');
    }


    public function isEmailVerified(){
        if(!is_null($this->email_verified_at)){
            return true;
        } else {
            return false;
        }
    }

    public function getFullName(){
        $fullName = "";

        if(isset($this->middle_name)){
            $fullName = $this->first_name." ".$this->middle_name." ".$this->last_name;
        } else {
            $fullName = $this->first_name." ".$this->last_name;
        } 

        if(isset($this->degree_before)){
            $fullName = $this->degree_before." ".$fullName;
        }

        if(isset($this->degree_after)){
            $fullName = $fullName." ".$this->degree_after;
        }

        return $fullName;
    }

    public function getBlockInternetStatus(){
        $now = Carbon::now()->toDateTimeString();

        if((count($this->activeInternetBlocks) != 0)){
            return 1;
        } else {
            return 0;
        }
    }

    public function getEditable(){
        $exists = $this->belongsToMany(Group::class, 'user_groups', 'user_id', 'group_id')->whereIn('groups.name', ['Domain Users', 'Domain Guests'])->exists();
        if($exists){
            return 1;
        } else {
            return 0;
        }
    }

    public function scopeSearch(Builder $query, $search = null): void
    {
        if(isset($search)){
            $query->where(function ($query) use ($search){
                $query
                ->where('first_name', 'LIKE', '%' . $search . '%')
                ->orWhere('last_name', 'LIKE', '%' . $search . '%')
                ->orWhere('email', $search);
            });
        }
    }

    public function scopeOrganizationUnits(Builder $query, $organizationUnits = null): void
    {
        if(isset($organizationUnits)){
            $query->whereIn('organization_unit', $organizationUnits);
        }
    }

    public function scopeByOrganizationUnit(Builder $query, $organizationUnit = null): void
    {
        if(isset($organizationUnit)){
            $organizationUnitArray = [];
            $selectedOrganizationUnit = OrganizationUnit::where('id', $organizationUnit)->first();   

            if($selectedOrganizationUnit){
                $organizationUnitArray = $selectedOrganizationUnit->getChildsId($selectedOrganizationUnit);
                $organizationUnitArray = array_merge($organizationUnitArray, [$organizationUnit]);
                $query->whereIN('organization_unit', $organizationUnitArray);
            }

        }



    }

    public function scopeByMissingTelNumber(Builder $query, $missingTelNumber = 0): void
    {
        if($missingTelNumber == 1){
            $query->whereNull('phone');
        }
    }

    public function scopeByDeleted(Builder $query, $deleted = null): void
    {
        if(isset($deleted)){
            if($deleted == 1){
                $query->whereNotNull('deleted_at');
            }
        }
    }

    public function scopeByWithDeleted(Builder $query, $withDeleted = null): void
    {
        if(isset($withDeleted)){
            if($withDeleted == 1){
                $query->withTrashed();
            }
        }
    }

    public function scopeByWithItems(Builder $query): void
    {
        $query->whereHas('items');
    }

    public function scopeByWithGroup(Builder $query, $group = null): void
    {
        if(isset($group)){
            $query->whereHas('groups', function (Builder $query) use ($group) {
                $query->where('group_id', $group->id);
            });
        }
    }

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime'
    ];
}

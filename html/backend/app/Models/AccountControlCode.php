<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\Builder;

class AccountControlCode extends Model
{
    use HasFactory;

    protected $table = 'account_control_codes';

    protected $fillable = [
        'code',
        'name',
        'listing'
    ];

    public function scopeByListing(Builder $query, $listing = null): void
    {
        if(isset($listing) && $listing == 1){
            $query->where('listing', '=', 1);
        }
    }
}

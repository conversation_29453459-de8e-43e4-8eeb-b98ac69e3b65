<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Builder;

class Move extends Model
{
    use HasFactory;

    protected $table = 'moves';

    protected $with = array('items', 'user','room');

    protected $fillable = [
        'user_id',
        'room_id',
        'type',
        'state',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withTrashed();
    }

    public function room(): BelongsTo
    {
        return $this->belongsTo(Room::class, 'room_id', 'id');
    }

    /*
    public function items(): HasManyThrough
    {
        return $this->hasManyThrough(
            Item::class,
            MoveItem::class,
            'move_id', // Foreign key on the environments table...
            'id', // Foreign key on the deployments table...
            'id', // Local key on the projects table...
            'item_id' // Local key on the environments table...
        )->withPivot();
    }
    */

    public function items(): BelongsToMany
    {
        return $this->belongsToMany(Item::class, 'move_items', 'move_id', 'item_id')->withPivot('id');
    }

    public function scopeByState(Builder $query, $state = null): void
    {
        if(isset($state)){
            $query->where('state', $state);
        }
    }

    public function scopeByType(Builder $query, $type = null): void
    {
        if(isset($type)){
            $query->where('type', $type);
        }
    }

    public function scopeByUserId(Builder $query, $userId = null): void
    {
        if(isset($userId)){
            $query->where('user_id', $userId);
        }
    }

    public function scopeByRoomId(Builder $query, $roomId = null): void
    {
        if(isset($roomId)){
            $query->where('room_id', $roomId);
        }
    }

}

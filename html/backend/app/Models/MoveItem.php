<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MoveItem extends Model
{
    use HasFactory;

    protected $table = 'move_items';

    protected $fillable = [
        'move_id',
        'item_id'
    ];

    public function move(): BelongsTo
    {
        return $this->belongsTo(Move::class, 'move_id', 'id');
    }

    public function item(): BelongsTo
    {
        return $this->belongsTo(Item::class, 'item_id', 'id');
    }

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrganizationUnit extends Model
{
    use HasFactory;

    protected $fillable = [
        'guid',
        'parent',
        'name',
        'distinguished_name',
        'promotion',
        'is_class',
        'map_bakalari',
        'map_skola_online'
    ];

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'organization_unit', 'id');
    }

    public function childs(): HasMany
    {
        return $this->hasMany(OrganizationUnit::class, 'parent', 'distinguished_name')->with('childs');
    }

    public function scopeByPromotion(Builder $query, $promotion = null): void
    {
        if(isset($promotion)){
            $query->where('promotion', '=', $promotion);
        }
    }

    public function scopeByIsClass(Builder $query, $isClass = null): void
    {
        if(isset($isClass)){
            $query->where('is_class', '=', $isClass);
        }
    }


    public function getChildsId($parrent, &$result = []){
        $childs = $parrent->childs;

        foreach($childs as $child){
            $result[] = $child->id;
            $this->getChildsId($child, $result);
        }

        return $result;
    }
}

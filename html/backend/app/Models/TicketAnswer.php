<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TicketAnswer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'ticket_id',
        'text'
    ];

    protected $with = ['user'];

    protected $table = 'ticket_answers';

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function ticket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class, 'ticket_id');
    }
}

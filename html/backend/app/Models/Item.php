<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class Item extends Model
{
    use HasFactory;

    protected $table = 'items';

    protected $guarded = [];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withTrashed();
    }

    public function room(): BelongsTo
    {
        return $this->belongsTo(Room::class, 'room_id', 'id');
    }

    public function accountingCategory(): BelongsTo
    {
        return $this->belongsTo(AccountingCategory::class, 'accounting_category_id', 'id');
    }

    public function scopeSearch(Builder $query, $search = null): void
    {
        if(isset($search)){
            $query->where(function ($query) use ($search){
                $query
                ->where('name', 'LIKE', '%' . $search . '%')
                ->orWhere('invoice_number', 'LIKE', '%' . $search . '%')
                ->orWhere('evidence_number', 'LIKE', '%' . $search . '%')
                ->orWhere('custom_1', 'LIKE', '%' . $search . '%')
                ->orWhere('custom_2', 'LIKE', '%' . $search . '%')
                ->orWhere('custom_3', 'LIKE', '%' . $search . '%')
                ->orWhere('custom_4', 'LIKE', '%' . $search . '%')
                ->orWhere('custom_6', 'LIKE', '%' . $search . '%')
                ->orWhere('custom_7', 'LIKE', '%' . $search . '%')
                ->orWhere('custom_8', 'LIKE', '%' . $search . '%')
                ->orWhere('custom_9', 'LIKE', '%' . $search . '%')
                ->orWhere('custom_10', 'LIKE', '%' . $search . '%');
            });
        }
    }

    public function scopeByState(Builder $query, $state = null): void
    {
        if(isset($state)){
            $query->where('state', $state);
        }
    }

    public function scopeByUserId(Builder $query, $userId = null): void
    {
        if(isset($userId)){
            $query->where('user_id', $userId);
        }
    }

    public function scopeByRoomId(Builder $query, $roomId = null): void
    {
        if(isset($roomId)){
            $query->where('room_id', $roomId);
        }
    }

    public function scopeByAccountingCategoryId(Builder $query, $accountingCategoryId = null): void
    {
        if(isset($accountingCategoryId)){
            $query->where('accounting_category_id', $accountingCategoryId);
        }
    }

    
}

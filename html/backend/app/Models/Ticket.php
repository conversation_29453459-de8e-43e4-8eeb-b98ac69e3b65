<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

use Carbon\Carbon;

class Ticket extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'state_id',
        'category_id',
        'subject',
        'text'
    ];

    protected $table = 'tickets';

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function state(): BelongsTo
    {
        return $this->belongsTo(TicketState::class, 'state_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(TicketCategory::class, 'category_id');
    }

    public function answers(): HasMany
    {
        return $this->hasMany(TicketAnswer::class, 'ticket_id')->orderBy('created_at', 'ASC');
    }

    public function scopeSearch(Builder $query, $search = null): void
    {
        if(isset($search)){
            $query->where(function ($query) use ($search){
                $query
                ->where('subject', 'LIKE', '%' . $search . '%')
                ->orWhere('text', 'LIKE', '%' . $search . '%');
            });
        }
    }

    public function scopeByUser(Builder $query, $user = null): void
    {
        if(isset($user)){
            $query->where('user_id', $user);
        }
    }

    public function scopeByCategory(Builder $query, $category = null): void
    {
        if(isset($category)){
            $query->where('category_id', $category);
        }
    }

    public function scopeByState(Builder $query, $state = null): void
    {
        if(isset($state)){
            $query->where('state_id', $state);
        }
    }

    public function scopeByDateRange(Builder $query, $dateFrom = null, $dateTo = null): void
    {
        if(isset($dateFrom) and isset($dateTo)){
            $start = Carbon::create($dateFrom)->startOfDay();
            $end = Carbon::create($dateTo)->endOfDay();
            $query->whereBetween('created_at', [$start, $end]);
        }
    }
}

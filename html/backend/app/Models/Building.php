<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class Building extends Model
{
    use HasFactory;

    protected $table = 'buildings';

    protected $fillable = [
        'name',
        'code'
    ];

    public function rooms(): HasMany
    {
        return $this->hasMany(Room::class, 'building_id', 'id');
    }

    public function scopeSearch(Builder $query, $search = null): void
    {
        if(isset($search)){
            $query->where(function ($query) use ($search){
                $query
                ->where('name', 'LIKE', '%' . $search . '%')
                ->orWhere('code', 'LIKE', '%' . $search . '%');
            });
        }
    }

}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Builder;

class Inventory extends Model
{
    use HasFactory;

    protected $table = 'inventories';

    protected $fillable = [
        'user_id',
        'room_id',
        'state',
    ];

    public function items(): BelongsToMany
    {
        return $this->belongsToMany(Item::class, 'inventory_items', 'inventory_id', 'item_id')->withPivot(['id','state']);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withTrashed();
    }

    public function room(): BelongsTo
    {
        return $this->belongsTo(Room::class, 'room_id', 'id');
    }

    public function scopeByState(Builder $query, $state = null): void
    {
        if(isset($state)){
            $query->where('state', $state);
        }
    }

    public function scopeByUserId(Builder $query, $userId = null): void
    {
        if(isset($userId)){
            $query->where('user_id', $userId);
        }
    }

    public function scopeByRoomId(Builder $query, $roomId = null): void
    {
        if(isset($roomId)){
            $query->where('room_id', $roomId);
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InternetBlock extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'account_name',
        'permanent',
        'from',
        'to'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function macAddresses(): HasMany
    {
        return $this->hasMany(AccountNameMacAddress::class, 'account_name', 'account_name');
    }

    public function scopeSearch(Builder $query, $search = null): void
    {
        if(isset($search)){
            $query->whereHas('user', function($q) use ($search){
                $q
                ->where('first_name', 'LIKE', '%' . $search . '%')
                ->orWhere('last_name', 'LIKE', '%' . $search . '%')
                ->orWhere('account_name', $search);
            })->get();
        }
    }

    public function scopeByOrganizationUnit(Builder $query, $organizationUnit = null): void
    {
        if(isset($organizationUnit)){
            $query->whereHas('user', function($q) use ($organizationUnit){
                $q
                ->where('organization_unit', $organizationUnit);
            })->get();
        }
    }

}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FortinetLog extends Model
{
    use HasFactory;

    protected $table = 'fortinet_logs';

    protected $fillable = [
        'account_name',
        'calling_station_id',
        'acct_status_type'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'account_name', 'account_name');
    }
}

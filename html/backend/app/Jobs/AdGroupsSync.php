<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Adldap;

use App\Models\Group;

class AdGroupsSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        //Group::query()->update(['active_directory_found' => 0]);
        $adGuids = [];
        $groups = Adldap::search()->groups()->get();
        foreach($groups as $group){
            $data = [];
            $data = [
                'guid' => $group->getConvertedGuid(), 
                'name' => $group->getName(),
                'distinguished_name' => $group->getDistinguishedName(),
                'description' => $group->getDescription(),
                //'active_directory_found' =>  1
            ];

            Group::updateOrCreate(
                ['guid' => $group->getConvertedGuid()], $data
            );

            $adGuids[] = $group->getConvertedGuid();
        }

        //Pole DB guidu 
        $dbGroups = Group::get();
        $dbGuids = $dbGroups->pluck('guid')->toArray();

        //Rozidl mezi AD a DB
        $removedGuids = array_diff($dbGuids, $adGuids);

        //mazani tech co uz nejsou v AD
        if(count($groups) > 0){
            if(count($removedGuids) != 0){
                Group::whereIn('guid', $removedGuids)->delete();
            }
        }


        //Overeni jestli je nastavena default Group, kdyz ne tak nastavit na Domain Users
        $default = Group::where('default', 1)->exists();
        if(!$default){
            $defaultGroup = Group::where('name', "Domain Users")->orderBy('id',"asc")->first();
            $defaultGroup->default = 1;
            $defaultGroup->save();
        }

        //Overeni jestli je nastavena default Visitors, kdyz ne tak nastavit na Domain Guests
        $visitors = Group::where('visitors', 1)->exists();
        if(!$default){
            $visitorsGroup = Group::where('name', "Domain Guests")->orderBy('id',"asc")->first();
            $visitorsGroup->visitors = 1;
            $visitorsGroup->save();
        }

        //Projdu znova kvuli nalinkovani Group Groups
        $groups = Adldap::search()->groups()->get();

        foreach($groups as $adGroup){        
            $dbChildGroup = Group::where('guid', $adGroup->getConvertedGuid())->first();
            $adGroupGroups = $adGroup->getMemberof();

            $oldFamilyGroupsIds = $dbChildGroup->parentGroups->pluck('id')->toArray();

            $newFamilyGroupsIds = Group::whereIn('distinguished_name', $adGroupGroups)->pluck('id')->toArray();

            $addToIds = array_diff($newFamilyGroupsIds, $oldFamilyGroupsIds);
            $removeToIds = array_diff($oldFamilyGroupsIds, $newFamilyGroupsIds);

            $dbChildGroup->parentGroups()->detach($removeToIds);
            $dbChildGroup->parentGroups()->attach($addToIds);
        }
    }
}

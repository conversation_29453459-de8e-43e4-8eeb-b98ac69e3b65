<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Adldap;
use Adldap\Utilities;

class AdUserDisable implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $accountName;
    /**
     * Create a new job instance.
     */
    public function __construct($accountName)
    {
        $this->accountName = $accountName;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $adUser = Adldap::search()->where('samaccountname', $this->accountName)->first();
        // Get a new account control object for the user.
        $ac = $adUser->getUserAccountControlObject();
        // Mark the account as disabled.
        $ac->accountIsDisabled();
        // Set the account control on the user and save it.
        $adUser->setUserAccountControl($ac);

        if(!$adUser->save()){
            $this->fail('Účet: '.$this->accountName.' se nepodařilo zakázat.');
        }
    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Adldap;
use Adldap\Utilities;

use App\Models\OrganizationUnit;

class AdOrganizationUnitsSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        //OrganizationUnit::query()->update(['active_directory_found' => 0]);
        $adGuids = [];
        $units = Adldap::search()->ous()->get();
        foreach($units as $unit){
            $data = [];
            $data = [
                'guid' => $unit->getConvertedGuid(), 
                'parent' => $this->getParent($unit->getDistinguishedName()),
                'name' => $unit->getName(),
                'distinguished_name' =>  $unit->getDistinguishedName(),
                //'active_directory_found' =>  1
            ];

            OrganizationUnit::updateOrCreate(
                ['guid' => $unit->getConvertedGuid()], $data
            );

            //Udelat pole guidu z AD
            $adGuids[] = $unit->getConvertedGuid();
        }

        //Pole DB guidu 
        $dbOrganizationUnits = OrganizationUnit::get();
        $dbGuids = $dbOrganizationUnits->pluck('guid')->toArray();

        //Rozidl mezi AD a DB
        $removedGuids = array_diff($dbGuids, $adGuids);

        //mazani tech co uz nejsou v AD
        if(count($units) > 0){
            if(count($removedGuids) != 0){
                OrganizationUnit::whereIn('guid', $removedGuids)->delete();
            }
        }
    }

    public function getParent($distinguishedName){
        $generatedParent = null;

        $explodedNames = explode(",", $distinguishedName, 2);
        $explodedCount = count($explodedNames);

        if($explodedCount != 1){
            $generatedParent = $explodedNames[1];
        } else {
            $generatedParent = null;
        }

        return $generatedParent;

        /*
        $exploded = Utilities::explodeDn($distinguishedName);
        if(isset($exploded[1])){
            return $exploded[1];
        } else {
            return null;
        }
        */
    }
}

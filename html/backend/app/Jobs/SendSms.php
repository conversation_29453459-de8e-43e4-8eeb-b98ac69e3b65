<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Illuminate\Support\Facades\Http;

class SendSms implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;

    protected $number;
    protected $text;
    /**
     * Create a new job instance.
     */
    public function __construct($number, $text)
    {
        $this->number = $number;
        $this->text = $text;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if(config('system.gsm_gate.send_sms')){
            $url = 'http://'.config('system.gsm_gate.ip').'/cgi/WebCGI?1500101=account='.config('system.gsm_gate.account').'&password='.config('system.gsm_gate.password').'&port='.config('system.gsm_gate.sim_port').'&destination='.$this->number.'&content='.$this->text;

            $response = Http::get($url);
            $body = $response->body();
            $lines = explode("\n", $body);
            $line = $lines[1];
            $statusData = explode(":", $line);
            if(trim($statusData[1]) == "Success"){} 
            else {
                $this->fail('Sms na tel. číslo: '.$this->number.' nebyla odeslána. Chyba: '.$response);
            }
        } else {
            $this->fail('Odesílání SMS je vypnuto. tel.číslo: '.$this->number.', '.$this->text);
        }
    }

}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Adldap;
use Adldap\Utilities;

class AdUserSetPassword implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $accountName;
    protected $password;
    protected $mustChangePassword;

    /**
     * Create a new job instance.
     */
    public function __construct($accountName, $password, $mustChangePassword)
    {
        $this->accountName = $accountName;
        $this->password = $password;
        $this->mustChangePassword = $mustChangePassword;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $adUser = Adldap::search()->where('samaccountname', $this->accountName)->first();
        $adUser->setPassword($this->password);

        if(1 == $this->mustChangePassword){
            $adUser->pwdlastset = 0;
        } else {
            $adUser->pwdlastset = -1;
        }

        if(!$adUser->save()){
            $this->fail('Heslo pro uživatele: '.$this->accountName.' se nepodařilo změnit.');
        }

    }
}

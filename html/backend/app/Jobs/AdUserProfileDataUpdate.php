<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Adldap;
use Adldap\Utilities;
use App\Models\User;

class AdUserProfileDataUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userId;
    protected $accountName;
    protected $profilPath;
    protected $scriptPath;
    protected $homeDirectory;
    protected $homeDrive;
    /**
     * Create a new job instance.
     */

     //$dbUser->id, $dbUser->account_name, $profilPath, $data['script_path'], $homeDirectory, $data['home_drive']

    public function __construct($userId, $accountName, $profilPath, $scriptPath, $homeDirectory, $homeDrive)
    {
        $this->userId = $userId;
        $this->accountName = $accountName;
        $this->profilPath = $profilPath;
        $this->scriptPath = $scriptPath;
        $this->homeDirectory = $homeDirectory;
        $this->homeDrive = $homeDrive;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $adUser = Adldap::search()->where('samaccountname', $this->accountName)->first();


        $adUser->setProfilePath($this->profilPath);
        $adUser->setScriptPath($this->scriptPath);
        $adUser->setHomeDirectory($this->homeDirectory);
        $adUser->setHomeDrive($this->homeDrive);


        if($adUser->save()){
            $dbUser = User::where('id', $this->userId)->first();
            $dbUser->profile_path = $this->profilPath;
            $dbUser->script_path = $this->scriptPath;
            $dbUser->home_directory = $this->homeDirectory;
            $dbUser->home_drive = $this->homeDrive;
            $dbUser->save();
        } else {
            $this->fail('Účet: '.$this->accountName.' se nepodařilo povolit.');
        }
    }
}

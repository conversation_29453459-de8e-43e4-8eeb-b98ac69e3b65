<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Adldap;
use App\Models\User;
use App\Models\OrganizationUnit;

class AdUserOuDataUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userId;
    protected $accountName;
    protected $organizationUnit;

    /**
     * Create a new job instance.
     */

     //$dbUser->id, $dbUser->account_name, $profilPath, $data['script_path'], $homeDirectory, $data['home_drive']

    public function __construct($userId, $accountName, $organizationUnit)
    {
        $this->userId = $userId;
        $this->accountName = $accountName;
        $this->organizationUnit = $organizationUnit;

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $adUser = Adldap::search()->where('samaccountname', $this->accountName)->first();

        $organizationUnit = OrganizationUnit::where('id', $this->organizationUnit)->first();
        $newOrganizationUnitDn = $organizationUnit->distinguished_name;

        if($adUser->move($newOrganizationUnitDn, true)){
            $dbUser = User::findOrFail($this->userId);
            $dbUser->organization_unit = $this->organizationUnit;
            $dbUser->save();
        } else {
            $this->fail('Účet: '.$this->accountName.' se nepodařilo převést do jiné OU.');
        }
    }
}

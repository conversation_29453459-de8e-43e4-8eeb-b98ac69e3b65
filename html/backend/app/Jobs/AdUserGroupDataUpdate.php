<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Adldap;
use App\Models\User;
use App\Models\Group;

class AdUserGroupDataUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userId;
    protected $accountName;
    protected $groups;

    /**
     * Create a new job instance.
     */

     //$dbUser->id, $dbUser->account_name, $profilPath, $data['script_path'], $homeDirectory, $data['home_drive']

    public function __construct($userId, $accountName, $groups)
    {
        $this->userId = $userId;
        $this->accountName = $accountName;
        $this->groups = $groups;

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        $adUser = Adldap::search()->where('samaccountname', $this->accountName)->first();
        $dbUser = User::findOrFail($this->userId);

        $adUserGroups = $adUser->getMemberof();
        foreach($adUserGroups as $dbUserGroup){
            $adGroup = Adldap::search()->groups()->where('distinguishedname', '=', $dbUserGroup)->first();
            $adGroup->removeMember($dbUser->distinguished_name);
        }

        foreach($this->groups as $group){
            $dbGroup = Group::where('id', $group)->first();  
            $adGroup = Adldap::search()->groups()->where('distinguishedname', '=', $dbGroup->distinguished_name)->first();
            $adGroup->addMember($dbUser->distinguished_name);
        }

        $dbUser->groups()->sync($this->groups);

    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use Adldap;
use Adldap\Utilities;
use App\Models\User;
use App\Models\OrganizationUnit;
use App\Models\AccountControlCode;
use App\Models\Group;
use Carbon\Carbon;

class AdUsersSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $organizationUnits = OrganizationUnit::all();
        $dbGroups = Group::all();
 
        $users = Adldap::search()->users()->get();

        $defaultDomainUsersGroup = $dbGroups->where('default', 1)->first();

        $adGuids = [];
        foreach($users as $user){
            
            $ou = $this->parseOrganizationUnit($user->getDistinguishedName());
            $organizationUnit = $organizationUnits->where('distinguished_name', $ou)->first();

            $data = [];
            $data = [
                'guid' => $user->getConvertedGuid(), 
                'organization_unit' => $organizationUnit?->id,
                'first_name' => $user->getFirstName(),
                'last_name' => $user->getLastName(),
                'email' => $user->getEmail(),
                'phone' => $user->getTelephoneNumber(),
                'account_name' => $user->getAccountName(),
                'distinguished_name' =>  $user->getDistinguishedName(),
                'active_directory' =>  1,
                'expire' =>  $this->covertDateTime($user->getAccountExpiry()),
                'account_control_code' =>  $this->getAccountControlCode($user->getUserAccountControlObject()),
                'profile_path' => $user->getProfilePath(),
                'script_path' => $user->getScriptPath(),
                'home_directory' => $user->getHomeDirectory(),
                'home_drive' => $user->getHomeDrive()
                //'active_directory_found' =>  1
            ];

            

            //Ulozime nebo pridame usera do DB
            $dbUser = User::updateOrCreate(
                ['guid' => $user->getConvertedGuid()], $data
            );

            $adGuids[] = $user->getConvertedGuid();

            //Nasyncujeme k uzivatelum AD skupiny
            $adGroups = $user->getMemberof();
            $dbGroupsId = [];
            if(count($adGroups) != 0){
                foreach($adGroups as $adGroup){
                    $dbGroup = $dbGroups->where('distinguished_name', $adGroup)->first();
                    $dbGroupsId[] = $dbGroup->id;   
                }   
            } else {
                if($user->getObjectCategory() == "Person"){
                    $dbGroupsId[] = $defaultDomainUsersGroup->id;
                }
            }


            


            $dbUser->groups()->sync($dbGroupsId);

        }

        //Pole DB guidu 
        $dbUsers = User::get();
        $dbGuids = $dbUsers->pluck('guid')->toArray();

        //Rozidl mezi AD a DB
        $removedGuids = array_diff($dbGuids, $adGuids);

        //mazani tech co uz nejsou v AD
        if(count($users) > 0){
            if(count($removedGuids) != 0){
                User::whereIn('guid', $removedGuids)->delete();
            }
        }


        //User::where('active_directory', 1)->where('active_directory_found', 0)->delete();
    }

    public function parseOrganizationUnit($distinguishedName){
        $exploded = explode(",",$distinguishedName);
        unset($exploded[0]);
        $organizationUnitDN = implode(",",$exploded);
        return $organizationUnitDN;
    }

    public function covertDateTime($windowsTime){
        if("9223372036854775807" != $windowsTime){      
            $date = date("d-m-Y H:i:s", $windowsTime/********-***********);
            $dateTime = Carbon::create($date);
            return $dateTime;
        } else {
            return null;
        }
        
    }

    public function getAccountControlCode($ac){
        $accountControlCode = AccountControlCode::where('code','=',$ac)->first();
        return (is_null($accountControlCode)) ? null : $accountControlCode->id;
    }
}

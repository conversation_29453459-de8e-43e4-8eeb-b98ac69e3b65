<?php

return [
    'NEED_EMAIL_VERIFICATION' => 1, // 0 - už<PERSON>l ne<PERSON> bít <PERSON> email, 1 - mus<PERSON> mít o<PERSON> email
    'EMAIL_VERIFICATION_TOKEN_LIFETIME' => 120, // životnost tokenu v minutách, 0 = neomezeno
    'RESET_PASSWORD_TOKEN_LIFETIME' => 120, // životnost tokenu v minutách, 0 = neomezeno
    'FRONTEND_URL' => 'http://localhost:5173', // url kterou používáme třeba u tlačítek v emailech jako root frontu

    'gsm_gate' => [
        'send_sms' => env("GSM_SEND_SMS", 0), // když je 0, sms se neodešlou, a failne to jobsu, tam jdou videt na jake data by byla sms odeslana
        'ip' => env("GSM_GATE_IP", "127.0.0.1"),
        'account' => env("GSM_GATE_ACCOUNT", "account"),
        'password' => env("GSM_GATE_PASSWORD", "password"),
        'sim_port' => env("GSM_GATE_SIM_PORT", 1)
    ],

    'fortinet' => [
        'ip' => env("FORTINET_IP", "127.0.0.1"),
        'token' => env("FORTINET_TOKEN", "abcdefg1234567"),
    ],

    'control_system' => [
        'host' => env("CONTROL_SYSTEM_HOST", "127.0.0.1:8080"),
        'secret' => env("CONTROL_SECRET", "tajnyklic"),
        'private' => env("CONTROL_PRIVATE", "tajnyklic2")
    ],

    'active_directory' => [
        'scheme' => env("LDAP_USERNAME_SCHEME", "{JMENO}.{PRIJMENI}"),
        'student_default_groups' => env("STUDENT_DEFAULT_GROUPS", ""),
        'employee_default_groups' => env("EMPLOYEE_DEFAULT_GROUPS", ""),
        'guest_default_groups' => env("GUEST_DEFAULT_GROUPS", ""),
        'guest_default_organization_unit' => env("GUEST_DEFAULT_ORGANIZATION_UNIT", ""),
        'user_password_complexity' => env("LDAP_USER_PASSWORD_COMPLEXITY", 1),
        'user_password_length' => env("LDAP_USER_PASSWORD_LENGTH", 8),
    ],

    'license' => [
        'license_key' => env("LICENSE_KEY", null)
    ]

];

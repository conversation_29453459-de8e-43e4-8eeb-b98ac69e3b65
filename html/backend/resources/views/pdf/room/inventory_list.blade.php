
<!--

@forelse ($data['rooms'] as $room)
    <h2><PERSON><PERSON><PERSON><PERSON>nos<PERSON></h2>
    <h3>{{ $room['name'] }} {{ $room['code'] }}</h3>
    <p><PERSON><PERSON><PERSON><PERSON>na osoba za mistnost:
    @if($room['user'])
        {{ $room['user']['first_name'] }} {{ $room['user']['last_name'] }}
    @endif 
    </p>

    Items
    @forelse ($room['items'] as $item)
        <ul><li>{{ $item['name'] }} {{ $item['evidence_number'] }}</li></ul>
    @empty
        <p>Maj<PERSON><PERSON> nenalezen</p>
    @endforelse

    Tady je ten kdo to generuje (zodpovedna osoba)
    <p>Předal: {{ $data['responsible_user']['first_name'] }} {{ $data['responsible_user']['last_name'] }}</p>

    <p>{{ format_human_date_time($data['now']) }}

    <pagebreak/>
@empty
@endforelse
-->


<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Přehled položek</title>


    <style>
        @page {
            margin: 0px;
        }

        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 11px;
        }

        .content {
            padding: 0px 15px;
        }

        h1 {
            font-weight: 400;
        }

        h2 {
            text-align: center;
            font-weight: 400;
        }

        th {
            text-align: left;
        }

        td {
            line-height: 1;
        }
    </style>


</head>

<body>
    <div class="header-top">
        <table style="width: 100%; border-spacing: 0px !important; padding-left: 15px; padding-right: 15px; padding-top: 20px; padding-bottom: 40px; margin-top: 0;">
            <tr>
                <td style="text-align: left; width: 100%;">
                    <h1 style="margin: 0px; padding: 0px;">Soupiska majetku místností</h1>
                </td>
            </tr>
        </table>
    </div>

    @forelse ($data['rooms'] as $room)
        <div class="content">
            <header>
                <div class="header-info">
                    <table style="width: 100%; border-collapse: collapse; border: 1px solid #000;">
                        <tr>
                            <td style="border: 1px solid #000; width: 25%; padding: 10px 20px;">
                                <span style="padding-right: 15px">Název místnosti:</span><strong> {{ $room['name'] }}</strong>
                            </td>
                            <td style="border: 1px solid #000; width: 25%; padding: 10px 20px;">
                                <span style="padding-right: 15px;">Kód místnosti:</span><strong> {{ $room['code'] }}</strong>
                            </td>
                            <td style="border: 1px solid #000; width: 50%; padding: 10px 20px;">
                                <span style="padding-right: 15px;">Zodpovědná osoba:</span><strong> @if($room['user']) {{ $room['user']['degree_before'] }} {{ $room['user']['first_name'] }} {{ $room['user']['middle_name'] }} {{ $room['user']['last_name'] }} {{ $room['user']['degree_after'] }} @endif </strong>
                            </td>
                        </tr>
                    </table>
                </div>
                <h2 style="padding: 10px 0px;">Přehled položek</h2>
            </header>

            <table style="width: 100%; border-spacing: 0px !important; padding-left: 0px; padding-right: 0px; padding-top: 0; margin-top: 0;">
                <thead>
                    <tr style="background-color: #ececec;">
                        <th style="padding: 10px 15px;">Evidenční číslo</th>
                        <th style="padding: 10px 15px;">Název položky</th>
                    </tr>
                </thead>
                <tbody>

                @forelse ($room['items'] as $item)
                    <tr>
                        <td style="padding: 10px 15px; border-bottom: 1px solid #ececec;">{{ $item['evidence_number'] }}</td>
                        <td style="padding: 10px 15px; border-bottom: 1px solid #ececec;">{{ $item['name'] }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="4" style="text-align: center; padding-top: 20px">Žádné položky</td>
                    </tr>
                @endforelse
                </tbody>
            </table>
            <p>Soupis vystavil(a): <strong>{{ $data['responsible_user']['degree_before'] }} {{ $data['responsible_user']['first_name'] }} {{ $data['responsible_user']['middle_name'] }} {{ $data['responsible_user']['last_name'] }} {{ $data['responsible_user']['degree_after'] }}</strong></p>
            <p style="margin-bottom: 40px;">Soupis je platný ke dni <strong>{{ format_human_date_time($data['now']) }}</strong></p>
        </div>
    @empty
    @endforelse
</body>
</html>

import{a as i,z as N,r as E,o as j,b as P,A as b,B as h,d as t,e as s,w as n,u as o,F,k,s as v,v as u,E as c,y as _,h as p}from"./index-c2402147.js";const B={class:"flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8"},C={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},S={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10"},U={class:"mt-2"},z={class:"mt-8 text-center text-sm text-gray-600"},R={class:"mt-4 text-center text-sm text-gray-600"},T={class:"flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8"},Z={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},K={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10"},Y={class:"mt-2"},$={class:"mt-2"},A={class:"mt-2"},D={class:"mt-8 text-center text-sm text-gray-600"},M={class:"mt-4 text-center text-sm text-gray-600"},H={__name:"ForgotPassword",setup(O){const x=i(""),g=i(""),r=i(""),f=i(""),y=N(),m=i(!1);y.query.token?m.value=!0:m.value=!1;function V(){k.post("/api/auth/forgot-password",{email:x.value}).then(l=>{v.success(l.data.message)}).catch(l=>{v.error(l.response.data.message)})}function q(){k.post("/api/auth/set-new-password",{token:y.query.token,email:g.value,new_password:r.value,new_password_confirmation:f.value}).then(l=>{v.success(l.data.message)}).catch(l=>{})}return(l,e)=>{const d=E("router-link");return j(),P(F,null,[b(t("div",B,[e[10]||(e[10]=t("div",{class:"sm:mx-auto sm:w-full sm:max-w-md"},[t("img",{class:"mx-auto h-12 w-auto",src:"https://tailwindui.com/img/logos/mark.svg?color=main-color&shade=600",alt:"Your Company"}),t("h2",{class:"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900"},"Zapomenuté heslo"),t("p",{class:"mt-2 text-center text-sm text-gray-600"}," Zadejte email, na který bude odeslán odkaz k resetování Vašeho hesla. ")],-1)),t("div",C,[t("div",S,[s(o(_),{onSubmit:e[1]||(e[1]=w=>V()),class:"space-y-6",action:"#",method:"POST"},{default:n(({values:w})=>[t("div",null,[e[6]||(e[6]=t("label",{for:"email",class:"block text-sm font-medium leading-6 text-gray-900"},"Email",-1)),t("div",U,[s(o(u),{rules:"required|email",modelValue:x.value,"onUpdate:modelValue":e[0]||(e[0]=a=>x.value=a),id:"email",name:"email",type:"email",autocomplete:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},null,8,["modelValue"]),s(o(c),{name:"email",class:"text-rose-400 text-sm block pt-1"})])]),e[7]||(e[7]=t("div",null,[t("button",{type:"submit",class:"flex w-full justify-center rounded-md bg-main-color-600 py-2 px-3 text-sm font-semibold text-white shadow-sm hover:bg-main-color-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-main-color-600"},"Resetovat heslo")],-1))]),_:1})]),t("p",z,[s(d,{to:{name:"login"},class:"font-medium text-main-color-600 hover:text-main-color-500"},{default:n(()=>e[8]||(e[8]=[p(" Přihlásit se ")])),_:1})]),t("p",R,[s(d,{to:{name:"contact-support"},class:"text-sm font-semibold text-gray-900"},{default:n(()=>e[9]||(e[9]=[p(" Kontaktovat podporu "),t("span",{"aria-hidden":"true"},"→",-1)])),_:1})])])],512),[[h,!m.value]]),b(t("div",T,[e[17]||(e[17]=t("div",{class:"sm:mx-auto sm:w-full sm:max-w-md"},[t("img",{class:"mx-auto h-12 w-auto",src:"https://tailwindui.com/img/logos/mark.svg?color=main-color&shade=600",alt:"Your Company"}),t("h2",{class:"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900"},"Nastavení nového hesla"),t("p",{class:"mt-2 text-center text-sm text-gray-600"}," Zadejte email k účtu ke kterému měníte heslo a nové heslo. ")],-1)),t("div",Z,[t("div",K,[s(o(_),{onSubmit:e[5]||(e[5]=w=>q()),class:"space-y-6"},{default:n(({values:w})=>[t("div",null,[e[11]||(e[11]=t("label",{for:"email",class:"block text-sm font-medium leading-6 text-gray-900"},"Email",-1)),t("div",Y,[s(o(u),{rules:"required|email",modelValue:g.value,"onUpdate:modelValue":e[2]||(e[2]=a=>g.value=a),id:"email",name:"email",type:"email",autocomplete:"email",required:"",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},null,8,["modelValue"]),s(o(c),{name:"email",class:"text-rose-400 text-sm block pt-1"})])]),t("div",null,[e[12]||(e[12]=t("label",{for:"new_password",class:"block text-sm font-medium leading-6 text-gray-900"},"Nové heslo",-1)),t("div",$,[s(o(u),{rules:"required|password",modelValue:r.value,"onUpdate:modelValue":e[3]||(e[3]=a=>r.value=a),id:"new_password",name:"new_password",type:"password",autocomplete:"email",required:"",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},null,8,["modelValue"]),s(o(c),{name:"new_password",class:"text-rose-400 text-sm block pt-1"})])]),t("div",null,[e[13]||(e[13]=t("label",{for:"new_password_confirmation",class:"block text-sm font-medium leading-6 text-gray-900"},"Nové heslo (znovu)",-1)),t("div",A,[s(o(u),{rules:"required|password|isEqual:"+r.value,modelValue:f.value,"onUpdate:modelValue":e[4]||(e[4]=a=>f.value=a),id:"new_password_confirmation",name:"new_password_confirmation",type:"password",autocomplete:"email",required:"",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},null,8,["rules","modelValue"]),s(o(c),{name:"new_password_confirmation",class:"text-rose-400 text-sm block pt-1"})])]),e[14]||(e[14]=t("div",null,[t("button",{type:"submit",class:"flex w-full justify-center rounded-md bg-main-color-600 py-2 px-3 text-sm font-semibold text-white shadow-sm hover:bg-main-color-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-main-color-600"},"Nastavit nové heslo")],-1))]),_:1})]),t("p",D,[s(d,{to:{name:"login"},class:"font-medium text-main-color-600 hover:text-main-color-500"},{default:n(()=>e[15]||(e[15]=[p(" Přihlásit se ")])),_:1})]),t("p",M,[s(d,{to:{name:"contact-support"},class:"text-sm font-semibold text-gray-900"},{default:n(()=>e[16]||(e[16]=[p(" Kontaktovat podporu "),t("span",{"aria-hidden":"true"},"→",-1)])),_:1})])])],512),[[h,m.value]])],64)}}};export{H as default};

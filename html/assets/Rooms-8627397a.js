import{a as c,j as Z,r as T,o as t,c as N,w as i,e as s,h as O,d as e,u as a,x as J,s as D,E as B,b as o,t as R,T as X,f as E,n as H,F as P,C,v as z,k as j,q as A,M as L,O as te,B as oe,z as se,R as ae,S as ne}from"./index-bfe6943f.js";import{_ as le}from"./AppTopbar-1fff46f6.js";import{l as U,k as re,g as ie,h as de,a4 as ce,$ as me,a3 as ue,s as pe}from"./index-5b4677b6.js";import{_ as ve}from"./pagination-7270df9d.js";import{c as w}from"./checkPermission.service-4ccc1117.js";import{_ as F}from"./basicModal-efb13c60.js";import{g as Q,S as W,b as Y,M as ee}from"./menu-40a0975a.js";import{S as G}from"./transition-78618ab0.js";import"./listbox-d7028c3f.js";import"./hidden-bb6f5784.js";import"./use-tracked-pointer-c6b2df1d.js";import"./use-resolve-button-type-122dc2ec.js";import"./use-controllable-21042f24.js";import"./dialog-abb0eee4.js";import"./use-tree-walker-d2a209d4.js";const ge={class:"p-6 gap-4 space-y-4"},_e=e("label",{for:"room-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název místnosti:",-1),he={class:"mt-2"},xe=e("label",{for:"item-count",class:"block text-sm font-normal leading-6 text-gray-900"},"Kód místnosti:",-1),ye={class:"mt-2"},fe=e("span",{class:"block text-sm font-normal leading-6 text-gray-900"},"Přiřazený uživatel (nepovinné):",-1),be={class:"flex items-center gap-3"},ke={class:"w-44 text-left"},we={key:0,class:"text-gray-900"},$e={key:1,class:"text-gray-400"},Re=["onClick"],Ce={key:0,class:"mt-2 flex"},ze={class:"border-t p-5"},Me={class:"text-right space-x-3"},Ve=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"}," Vytvořit ",-1),je={__name:"createRoomModal",emits:["reloadItems"],setup(S,{expose:M,emit:V}){const $=c(!1);Z("debugModeGlobalVar");const h=c(""),n=c(""),g=c(!1),u=c({id:""}),f=c({});function k(m){u.value=m}function _(){$.value=!1}function r(){x(),$.value=!0,h.value="",n.value="",u.value={id:""}}async function x(){g.value=!0,await j.get("/api/users?page=1&search=").then(m=>{f.value=m.data.data}).catch(m=>{console.log(m)})}async function b(){w.check("rooms.create")||w.check("property.master")?await j.post("api/rooms",{name:h.value,code:n.value,user_id:u.value.id}).then(m=>{A.success(m.data.message)}).catch(m=>{console.log(m)}):g.value=!1,_(),V("reloadItems",!0)}return M({openModal:r}),(m,l)=>{const I=T("ChevronDownIcon");return t(),N(a(G),{appear:"",show:$.value,as:"template",onClose:l[6]||(l[6]=y=>_())},{default:i(()=>[s(F,{size:"xs"},{"modal-title":i(()=>[O("Založení nové místnosti")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:l[0]||(l[0]=y=>_())},[s(a(U),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[s(a(J),{onSubmit:l[5]||(l[5]=y=>b())},{default:i(({values:y})=>[e("div",ge,[e("div",null,[_e,e("div",he,[s(a(D),{rules:"required",modelValue:h.value,"onUpdate:modelValue":l[1]||(l[1]=d=>h.value=d),type:"text",name:"room-name",id:"room-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název místnosti..."},null,8,["modelValue"]),s(a(B),{name:"room-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[xe,e("div",ye,[s(a(D),{rules:"required",modelValue:n.value,"onUpdate:modelValue":l[2]||(l[2]=d=>n.value=d),type:"text",name:"item-code",id:"item-code",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte kód místnosti..."},null,8,["modelValue"]),s(a(B),{name:"item-code",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[fe,e("div",be,[s(a(Q),{as:"div",class:"relative inline-block text-left mt-2 z-10"},{default:i(()=>[e("div",null,[s(a(W),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm text-white border border-gray-300"},{default:i(()=>[e("div",ke,[u.value&&u.value.first_name?(t(),o("span",we,R(u.value.first_name+" "+u.value.last_name),1)):(t(),o("span",$e,"Přiřazený uživatel..."))]),s(I,{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]),_:1})]),s(X,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:i(()=>[s(a(Y),{class:"absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:i(()=>[(t(!0),o(P,null,E(f.value,d=>(t(),o("div",{key:d.id,class:"px-1 py-1"},[s(a(ee),null,{default:i(({active:p})=>[e("button",{type:"button",onClick:q=>k(d),class:H([p?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},R(d.first_name+" "+d.last_name),11,Re)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1}),u.value.id?(t(),o("div",Ce,[e("button",{onClick:l[3]||(l[3]=C(d=>k({id:""}),["prevent"]))},[s(a(U),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})])])):z("",!0)])])]),e("div",ze,[e("div",Me,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:l[4]||(l[4]=C(d=>_(),["prevent"]))}," Zavřít "),Ve])])]),_:1})]),_:1})]),_:1},8,["show"])}}},Se={key:0,class:"p-6 space-y-4"},Ie=e("label",{for:"room-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název místnosti:",-1),Ue={class:"mt-2"},Ne=e("label",{for:"room-code",class:"block text-sm font-normal leading-6 text-gray-900"},"Kód místnosti:",-1),Pe={class:"mt-2"},Ze=e("span",{class:"block text-sm font-normal leading-6 text-gray-900"},"Přiřazený uživatel (nepovinné):",-1),qe={class:"flex items-center gap-3"},De={class:"w-44 text-left"},Be={key:0,class:"text-gray-900"},Oe={key:1,class:"text-gray-400"},Ee=["onClick"],Fe={key:0,class:"mt-2 flex"},Ge={class:"border-t p-5"},Ke={class:"text-right space-x-3"},Te=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Upravit ",-1),Ae={__name:"editRoomModal",props:{selectedRoom:{type:Object,required:!0}},emits:["reloadRooms"],setup(S,{expose:M,emit:V}){const $=S,h=c(!1);Z("debugModeGlobalVar");const n=c(""),g=c({id:""}),u=c({}),f=c(!1);L(()=>$.selectedRoom,(m,l)=>{n.value={},g.value={id:""},n.value=m,m.user&&(g.value=m.user)});function k(){h.value=!1}async function _(){r(),h.value=!0}async function r(){f.value=!0,await j.get("/api/users?page=1&search=").then(m=>{u.value=m.data.data}).catch(m=>{console.log(m)})}function x(m){g.value=m}async function b(){w.check("items.edit")||w.check("property.master")?(console.log($.selectedRoom),await j.post("api/rooms/"+$.selectedRoom.id+"/update",{name:n.value.name,code:n.value.code,user_id:g.value.id}).then(m=>{A.success(m.data.message)}).catch(m=>{console.log(m)})):f.value=!1,k(),V("reloadRooms",!0)}return M({openModal:_}),(m,l)=>(t(),N(a(G),{appear:"",show:h.value,as:"template",onClose:l[6]||(l[6]=I=>k())},{default:i(()=>[s(F,{size:"sm"},{"modal-title":i(()=>[O("Úprava místnosti")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:l[0]||(l[0]=I=>k())},[s(a(U),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[s(a(J),{onSubmit:l[5]||(l[5]=I=>b())},{default:i(({values:I})=>[n.value?(t(),o("div",Se,[e("div",null,[Ie,e("div",Ue,[s(a(D),{rules:"required",modelValue:n.value.name,"onUpdate:modelValue":l[1]||(l[1]=y=>n.value.name=y),type:"text",name:"room-name",id:"room-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název místnosti..."},null,8,["modelValue"]),s(a(B),{name:"room-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[Ne,e("div",Pe,[s(a(D),{rules:"required",modelValue:n.value.code,"onUpdate:modelValue":l[2]||(l[2]=y=>n.value.code=y),type:"text",name:"room-code",id:"room-code",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte kód místnosti..."},null,8,["modelValue"]),s(a(B),{name:"room-code",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[Ze,e("div",qe,[s(a(Q),{as:"div",class:"relative inline-block text-left mt-2 z-10"},{default:i(()=>[e("div",null,[s(a(W),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm text-white border border-gray-300"},{default:i(()=>[e("div",De,[g.value&&g.value.first_name?(t(),o("span",Be,R(g.value.first_name+" "+g.value.last_name),1)):(t(),o("span",Oe,"Přiřazený uživatel..."))]),s(a(re),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]),_:1})]),s(X,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:i(()=>[s(a(Y),{class:"absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:i(()=>[(t(!0),o(P,null,E(u.value,y=>(t(),o("div",{key:y.id,class:"px-1 py-1"},[s(a(ee),null,{default:i(({active:d})=>[e("button",{type:"button",onClick:p=>x(y),class:H([d?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},R(y.first_name+" "+y.last_name),11,Ee)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1}),g.value.id?(t(),o("div",Fe,[e("button",{onClick:l[3]||(l[3]=C(y=>x({id:""}),["prevent"]))},[s(a(U),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})])])):z("",!0)])])])):z("",!0),e("div",Ge,[e("div",Ke,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:l[4]||(l[4]=C(y=>k(),["prevent"]))}," Zavřít "),Te])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Le={key:0,class:"p-6"},Je=e("span",null,"Opravdu si přejete místnost smazat?",-1),Xe=[Je],He={class:"border-t p-5"},Qe={class:"text-right space-x-3"},We={__name:"deleteRoomModal",props:{selectedRoom:{type:Object,required:!0}},emits:["reloadRooms"],setup(S,{expose:M,emit:V}){const $=S,h=c(!1);Z("debugModeGlobalVar");const n=c(""),g=c(!1);L(()=>$.selectedRoom,(_,r)=>{n.value=_});function u(){h.value=!1}async function f(){h.value=!0}async function k(){w.check("rooms.delete")||w.check("property.master")?await j.post("api/rooms/"+n.value.id+"/delete").then(_=>{A.success(_.data.message)}).catch(_=>{console.log(_)}):g.value=!1,u(),V("reloadRooms",!0)}return M({openModal:f}),(_,r)=>(t(),N(a(G),{appear:"",show:h.value,as:"template",onClose:r[3]||(r[3]=x=>u())},{default:i(()=>[s(F,{size:"sm"},{"modal-title":i(()=>[O("Smazat místnost")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=x=>u())},[s(a(U),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[n.value?(t(),o("div",Le,Xe)):z("",!0),e("div",He,[e("div",Qe,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[1]||(r[1]=C(x=>u(),["prevent"]))}," Zavřít "),e("button",{onClick:r[2]||(r[2]=C(x=>k(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}},Ye={key:0,class:"p-6"},et={key:0},tt={class:"grid grid-cols-2 gap-x-4"},ot={class:"flex items-center gap-2"},st=e("span",null,"Informace o místnosti",-1),at={class:"mt-4 flex gap-10"},nt=e("span",{class:"block text-xs text-gray-400"},"Kód místnosti:",-1),lt={key:0},rt={key:1},it=e("span",{class:"block text-xs text-gray-400"},"Název místnosti:",-1),dt={key:0},ct={key:1},mt={key:0},ut={class:"flex items-center gap-2"},pt=e("span",null,"Správce místnosti",-1),vt={class:"mt-4 flex gap-10"},gt=e("span",{class:"block text-xs text-gray-400"},"Jméno uživatele:",-1),_t={key:0},ht={key:1},xt=e("span",{class:"block text-xs text-gray-400"},"E-mail uživatele:",-1),yt={key:0},ft={key:1},bt={key:0},kt=e("span",{class:"block text-xs text-gray-400"},"Organizace:",-1),wt={key:0},$t={key:1},Rt={class:"pt-8"},Ct={class:"flex items-center gap-2 pb-4"},zt=e("span",null,"Položky místnosti",-1),Mt={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},Vt={class:"sm:-mx-6 lg:-mx-8"},jt={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},St={class:"min-w-full divide-y divide-gray-200"},It=e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Ev. číslo "),e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Název položky ")])],-1),Ut={key:0,class:"divide-y divide-gray-200"},Nt={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},Pt={key:0},Zt={key:1},qt={class:"pl-3 pr-5 text-sm"},Dt={key:0},Bt={key:1},Ot={key:1},Et=e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné položky.")],-1),Ft=[Et],Gt={key:1,class:"py-20"},Kt={class:"border-t p-5"},Tt={class:"text-right space-x-3"},At={__name:"detailRoomModal",props:{selectedRoom:{type:Object,required:!0}},emits:["reloadRooms"],setup(S,{expose:M,emit:V}){const $=S,h=c(!1);Z("debugModeGlobalVar");const n=c(""),g=c(!1);L(()=>$.selectedRoom,(_,r)=>{k(_.id)});function u(){h.value=!1}async function f(){h.value=!0}async function k(_){g.value=!1,await j.get("/api/rooms/"+_).then(r=>{n.value=r.data.data}).catch(r=>{console.log(r)}),g.value=!1}return M({openModal:f}),(_,r)=>{const x=T("VueSpinner");return t(),N(a(G),{appear:"",show:h.value,as:"template",onClose:r[2]||(r[2]=b=>u())},{default:i(()=>[s(F,{size:"sm"},{"modal-title":i(()=>[O("Detail místnosti")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=b=>u())},[s(a(U),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[!g.value&&n.value?(t(),o("div",Ye,[n.value&&n.value.id?(t(),o("div",et,[e("div",tt,[e("div",null,[e("div",ot,[s(a(ie),{class:"h-6 w-6","aria-hidden":"true"}),st]),e("div",at,[e("div",null,[nt,n.value.id?(t(),o("span",lt,R(n.value.code),1)):(t(),o("span",rt,"-"))]),e("div",null,[it,n.value.name?(t(),o("span",dt,R(n.value.name),1)):(t(),o("span",ct,"-"))])])]),n.value.user?(t(),o("div",mt,[e("div",ut,[s(a(de),{class:"h-6 w-6","aria-hidden":"true"}),pt]),e("div",vt,[e("div",null,[gt,n.value.user.full_name?(t(),o("span",_t,R(n.value.user.full_name),1)):(t(),o("span",ht,"-"))]),e("div",null,[xt,n.value.user.email?(t(),o("span",yt,R(n.value.user.email),1)):(t(),o("span",ft,"-"))]),n.value.user.organization_unit?(t(),o("div",bt,[kt,n.value.user.organization_unit.name?(t(),o("span",wt,R(n.value.user.organization_unit.name),1)):(t(),o("span",$t,"-"))])):z("",!0)])])):z("",!0)]),e("div",Rt,[e("div",Ct,[s(a(ce),{class:"h-6 w-6","aria-hidden":"true"}),zt]),e("div",Mt,[e("div",Vt,[e("div",jt,[e("table",St,[It,n.value.items&&n.value.items.length?(t(),o("tbody",Ut,[(t(!0),o(P,null,E(n.value.items,b=>(t(),o("tr",{key:b.id},[e("td",Nt,[b.evidence_number?(t(),o("span",Pt,R(b.evidence_number),1)):(t(),o("span",Zt,"-"))]),e("td",qt,[b.name?(t(),o("span",Dt,R(b.name),1)):(t(),o("span",Bt,"-"))])]))),128))])):(t(),o("tbody",Ot,Ft))])])])])])])):z("",!0)])):(t(),o("div",Gt,[s(x,{class:"mx-auto text-spinner-color",size:"40"})])),e("div",Kt,[e("div",Tt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[1]||(r[1]=C(b=>u(),["prevent"]))}," Zavřít ")])])]),_:1})]),_:1},8,["show"])}}},Lt={class:"space-y-6"},Jt={class:"px-0"},Xt={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Ht={class:"sm:flex justify-between items-center gap-4"},Qt={class:"w-80"},Wt={class:"flex items-center gap-4"},Yt=e("span",null,"Resetovat",-1),eo=[Yt],to={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},oo={class:"sm:-mx-6 lg:-mx-8"},so={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},ao={key:0,class:"min-w-full divide-y divide-gray-200"},no=e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md w-16"}),e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Kód místnosti "),e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Název místnosti "),e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"})])],-1),lo={key:0,class:"divide-y divide-gray-200"},ro={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},io=["onClick"],co={class:"whitespace-nowrap py-4 px-3 text-sm text-gray-600"},mo={key:0},uo={key:1},po={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},vo={key:0},go={key:1},_o={class:"text-end pr-5 w-40"},ho=["onClick"],xo=["onClick"],yo=["onClick"],fo={key:1},bo=e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné místnosti.")],-1),ko=[bo],Do={__name:"Rooms",setup(S){const M=c(""),V=c(""),$=c(""),h=c("");te(),Z("debugModeGlobalVar");const n=c(["rooms"]),g=c(!1),u=c(""),f=c(1),k=c({}),_=c({}),r=c({});oe(()=>{x()});async function x(){g.value=!0,await j.get("/api/rooms?page="+f.value+"&search="+u.value).then(d=>{_.value=d.data.data,k.value=d.data.meta}).catch(d=>{console.log(d)}),g.value=!1}function b(d){r.value=d}function m(d){j.post("api/rooms/generate-pdf",{rooms:[d.id]},{responseType:"blob"}).then(p=>{const q=window.URL.createObjectURL(new Blob([p.data])),v=document.createElement("a");v.href=q,v.setAttribute("download","Položky-místnosti.pdf"),document.body.appendChild(v),v.click()}).catch(p=>{console.error("Chyba při získávání souboru:",p)})}function l(d){f.value=d,x()}function I(){g.value=!0,f.value=1,u.value="",x()}function y(){g.value=!0,f.value=1,x()}return(d,p)=>{const q=T("VueSpinner");return t(),o(P,null,[s(le,{breadCrumbs:n.value},{topbarButtons:i(()=>[a(w).check("rooms.create")||a(w).check("property.master")?(t(),o("button",{key:0,onClick:p[0]||(p[0]=C(v=>d.$refs.createRoomRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nová místnost ")):z("",!0)]),_:1},8,["breadCrumbs"]),e("div",Lt,[e("div",Jt,[e("div",Xt,[e("div",Ht,[e("div",Qt,[se(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":p[1]||(p[1]=v=>u.value=v),onKeyup:p[2]||(p[2]=ne(v=>y(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[ae,u.value]])]),e("div",Wt,[e("button",{onClick:p[3]||(p[3]=v=>I()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},eo),e("button",{onClick:p[4]||(p[4]=v=>y()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",to,[e("div",oo,[e("div",so,[g.value==!1?(t(),o("table",ao,[no,_.value&&_.value.length?(t(),o("tbody",lo,[(t(!0),o(P,null,E(_.value,v=>(t(),o("tr",{key:v.id},[e("td",ro,[a(w).check("rooms.read")||a(w).check("property.master")?(t(),o("button",{key:0,onClick:C(K=>(b(v),d.$refs.detailRoomRef.openModal()),["prevent"]),class:"mr-2"},[s(a(me),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,io)):z("",!0)]),e("td",co,[v.code?(t(),o("span",mo,R(v.code),1)):(t(),o("span",uo,"-"))]),e("td",po,[v.name?(t(),o("span",vo,R(v.name),1)):(t(),o("span",go,"-"))]),e("td",_o,[e("button",{onClick:C(K=>m(v),["prevent"]),class:"mr-2"},[s(a(ue),{class:"h-8 w-8 text-white bg-main-color-600 hover:bg-main-color-700 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,ho),a(w).check("rooms.edit")||a(w).check("property.master")?(t(),o("button",{key:0,onClick:C(K=>(b(v),d.$refs.editRoomRef.openModal()),["prevent"]),class:"mr-2"},[s(a(pe),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,xo)):z("",!0),a(w).check("rooms.delete")||a(w).check("property.master")?(t(),o("button",{key:1,onClick:C(K=>(b(v),d.$refs.deleteRoomRef.openModal()),["prevent"])},[s(a(U),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,yo)):z("",!0)])]))),128))])):(t(),o("tbody",fo,ko))])):(t(),N(q,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),k.value!==null?(t(),N(ve,{key:0,meta:k.value,onSetPage:l,modelValue:f.value,"onUpdate:modelValue":p[5]||(p[5]=v=>f.value=v)},null,8,["meta","modelValue"])):z("",!0)])]),s(je,{ref_key:"createRoomRef",ref:M,onReloadItems:p[6]||(p[6]=v=>x())},null,512),s(Ae,{ref_key:"editRoomRef",ref:V,selectedRoom:r.value,onReloadRooms:p[7]||(p[7]=v=>x())},null,8,["selectedRoom"]),s(We,{ref_key:"deleteRoomRef",ref:$,selectedRoom:r.value,onReloadRooms:p[8]||(p[8]=v=>x())},null,8,["selectedRoom"]),s(At,{ref_key:"detailRoomRef",ref:h,selectedRoom:r.value},null,8,["selectedRoom"])],64)}}};export{Do as default};

import{a as d,j as L,o as n,b as m,d as s,e as l,w as E,u as o,k as u,l as w,m as p,p as k,q as f,s as x,E as v,c as V,t as S,v as M,F as B,f as F,x as I}from"./index-ad469968.js";import{E as N,a as A}from"./index-15865cd0.js";const D="/assets/logo-color-285c21c3.svg",Z={class:"flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8 bg-center bg-[url('/src/assets/login-bg.jpg')]"},G={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},H={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10 relative"},R={key:0},Y={class:"mt-2"},$={key:1},z={class:"mt-2"},J={class:"mt-2 relative"},K={key:0},O={class:"mt-4"},Q={class:"space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0"},W=["for"],se={__name:"Login",setup(X){const r=d("ad"),_=L("debugModeGlobalVar"),y=d(""),b=d(""),c=d(""),g=d(!1),j=()=>{g.value=!g.value},P=[{id:"ad",title:"Účet Active Directory"},{id:"local",title:"Lokální účet"}];async function h(){await u.get("/api/items/custom-columns").then(t=>{console.log(t.data.data),p().setCustomColumns(t.data.data)}).catch(t=>{console.log(t)})}async function U(){r.value=="local"?(await u.get("/sanctum/csrf-cookie"),await u.post("/api/auth/login",{email:y.value,password:c.value}).then(t=>{if(t.status===200){const e=w(),i=p();i.setUser(t.data.data.user),i.setPermissions(t.data.data.permissions),h(),q(),e.setIsLoged(!0),k.push({name:"users"}),f.success("Přihlášení bylo úspešné")}}).catch(t=>{f.error(t.response.data.message)})):r.value=="ad"&&(await u.get("/sanctum/csrf-cookie"),await u.post("/api/auth/login-ad",{username:b.value,password:c.value}).then(t=>{if(t.status===200){const e=w(),i=p();i.setUser(t.data.data.user),i.setPermissions(t.data.data.permissions),e.setIsLoged(!0),h(),k.push({name:"users"}),f.success("Přihlášení bylo úspešné")}}).then(function(){userStore.setUserModules()}).catch(t=>{f.error(t.response.data.message)}))}async function q(){await u.get("/api/modules").then(t=>{p().setModules(t.data.data)}).catch(t=>{console.log(t)})}return(t,e)=>(n(),m("div",Z,[e[11]||(e[11]=s("div",{class:"sm:mx-auto sm:w-full sm:max-w-md"},[s("img",{class:"mx-auto h-12 w-auto",src:D,alt:"Your Company"}),s("h2",{class:"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900"},"Přihlášení do systému"),s("p",{class:"mt-2 text-center text-lg text-gray-600"}," Správa Active Directory ")],-1)),s("div",G,[s("div",H,[l(o(I),{onSubmit:e[4]||(e[4]=i=>U()),class:"space-y-6"},{default:E(({values:i})=>[r.value=="local"?(n(),m("div",R,[e[5]||(e[5]=s("label",{for:"email",class:"block text-sm font-medium leading-6 text-gray-900"},"Email",-1)),s("div",Y,[l(o(x),{rules:"required|email",modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=a=>y.value=a),id:"email",name:"email",type:"email",autocomplete:"email",required:"",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte emailovou adresu..."},null,8,["modelValue"]),l(o(v),{name:"email",class:"text-rose-400 text-sm block pt-1"})])])):(n(),m("div",$,[e[6]||(e[6]=s("label",{for:"username",class:"block text-sm font-medium leading-6 text-gray-900"},"Přihlašovací jméno",-1)),s("div",z,[l(o(x),{rules:"required",modelValue:b.value,"onUpdate:modelValue":e[1]||(e[1]=a=>b.value=a),id:"username",name:"username",type:"text",required:"",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte uživatelké jméno..."},null,8,["modelValue"]),l(o(v),{name:"username",class:"text-rose-400 text-sm block pt-1"})])])),s("div",null,[e[7]||(e[7]=s("label",{for:"password",class:"block text-sm font-medium leading-6 text-gray-900"},"Heslo",-1)),s("div",J,[l(o(x),{rules:"required",modelValue:c.value,"onUpdate:modelValue":e[2]||(e[2]=a=>c.value=a),id:"password",name:"password",type:g.value?"text":"password",autocomplete:"off",required:"",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte Vaše heslo..."},null,8,["modelValue","type"]),l(o(v),{name:"password",class:"text-rose-400 text-sm block pt-1"}),s("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:j},[g.value?(n(),V(o(N),{key:0,name:"eye-off",class:"h-5 w-5"})):(n(),V(o(A),{key:1,name:"eye",class:"h-5 w-5"}))])])]),s("div",null,[o(_)?(n(),m("span",K,S(r.value),1)):M("",!0),s("div",null,[e[9]||(e[9]=s("p",{class:"text-sm"},"Typ Přihlášení:",-1)),s("fieldset",O,[e[8]||(e[8]=s("legend",{class:"sr-only"},"Notification method",-1)),s("div",Q,[(n(),m(B,null,F(P,a=>s("div",{key:a.id,class:"flex items-center"},[l(o(x),{rules:"requiredRadio",id:a.id,modelValue:r.value,"onUpdate:modelValue":e[3]||(e[3]=C=>r.value=C),name:"selectedLogin",type:"radio",checked:r.value==a.id,value:a.id,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","modelValue","checked","value"]),s("label",{label:"",for:a.id,class:"ml-3 block text-sm leading-6 text-gray-900 cursor-pointer"},S(a.title),9,W)])),64)),l(o(v),{name:"selectedLogin",class:"text-rose-400 text-sm block pt-1"})])])])]),e[10]||(e[10]=s("div",null,[s("button",{type:"submit",class:"flex w-full justify-center rounded-md bg-pink-600 py-2 px-3 text-sm font-semibold text-white shadow-sm hover:bg-pink-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-pink-600"}," Přihlásit se")],-1))]),_:1})])])]))}};export{se as default};

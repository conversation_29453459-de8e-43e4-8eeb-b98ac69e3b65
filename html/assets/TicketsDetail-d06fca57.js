import{m as P,y as Z,a as m,I as B,k as w,r as F,o as r,b as i,e as d,w as c,d as e,t as o,h as x,u as a,v as u,F as h,f as I,q as C,a1 as y,s as N,E as z,x as E,n as q,C as M}from"./index-68b8ac03.js";import{_ as H}from"./AppTopbar-8e1085f0.js";import{P as U,X}from"./index-034b8efa.js";import{z as A}from"./index-67bf9347.js";import{_ as K}from"./basicModal-86318d6e.js";import{c as g}from"./checkPermission.service-2282af58.js";import{S as L}from"./transition-332c98cd.js";import"./dialog-ef3853bc.js";import"./hidden-de3fbc3d.js";const O={key:0},R={key:0},G={class:"flex justify-between items-center py-8"},J={class:"text-2xl text-gray-900"},Q={class:"flex justify-between items-center gap-16"},W={class:"text-sm text-gray-900"},ee={class:"font-semibold"},te={class:"text-sm text-gray-900"},se={class:"font-semibold"},ae={key:0,class:"bg-red-200/75 text-red-700 rounded-lg px-3 py-1 font-semibold text-xs"},oe={key:1,class:"bg-amber-200/75 text-amber-700 rounded-lg px-3 py-1 font-semibold text-xs"},le={key:2,class:"space-x-4 space-y-4"},ne={class:"bg-green-200/75 text-green-700 rounded-lg px-3 py-1 font-semibold text-xd"},re={class:"text-green-700"},ie={class:"relative bg-white border border-zinc-200/70 rounded-md px-5 py-8"},de={class:"text-sm text-gray-900"},ue={role:"list",class:"space-y-6"},ce={key:0,class:"relative flex gap-x-4"},me={key:0,class:"-bottom-6 absolute left-0 top-2 flex w-6 justify-center"},pe={class:"flex w-full flex-wrap"},ve={class:"text-sm pl-2 w-48 pb-4"},xe={class:"font-semibold"},ge={class:"block w-full"},fe={class:"pt-4 sm:pt-3 sm:pl-4"},be={type:"submit",class:"rounded-md bg-green-500 pl-2 pr-4 py-2 text-sm mt-0 text-white shadow-sm hover:bg-green-600 flex items-center gap-2"},_e={class:"flex items-center w-full"},ke={class:"text-sm pl-2 w-48"},we={class:"font-semibold"},ye={class:"block"},he={class:"bg-white rounded-lg p-5 grow"},Ce={class:"text-sm leading-6 text-gray-900"},Me={class:"border-t p-5"},Ye={class:"text-right space-x-3"},Ie={__name:"TicketsDetail",setup(Ve){const Y=P(),V=Z(),j=m(["tickets-parent","ticket-detail"]),f=m(!1),s=m(),p=m("");B(()=>{f.value=!0,b()});function b(){w.get("/api/tickets/"+V.params.id).then(l=>{s.value=l.data.data,s.value.answers&&s.value.answers.reverse(),f.value=!1}).catch(l=>{console.log(l)})}function D(){w.post("/api/tickets/"+s.value.id+"/answers",{text:p.value}).then(l=>{C.success(l.data.message),p.value="",b()}).catch(l=>{console.log(l)})}const _=m(!1);function v(){_.value=!1}function S(){_.value=!0}function T(){w.post("/api/tickets/"+s.value.id+"/close").then(l=>{C.success(l.data.message),b(),v()}).catch(l=>{console.log(l)})}return(l,t)=>{const $=F("router-link");return r(),i(h,null,[d(H,{breadCrumbs:j.value},{topbarButtons:c(()=>[d($,{to:{name:"tickets-list"},class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200 flex items-center"},{default:c(()=>t[7]||(t[7]=[x(" Zavřít ")])),_:1}),s.value?(r(),i("div",O,[s.value.state.id!=4&&(a(g).check("tickets.close")||a(g).check("tickets.master"))?(r(),i("button",{key:0,onClick:t[0]||(t[0]=n=>S()),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600 flex items-center gap-2"},[d(a(A),{class:"h-6 w-6 text-white","aria-hidden":"true"}),t[8]||(t[8]=e("span",null,"Vyřešit požadavek",-1))])):u("",!0)])):u("",!0)]),_:1},8,["breadCrumbs"]),!f.value&&s.value?(r(),i("div",R,[e("div",G,[e("h1",J,o(s.value.subject),1),e("div",Q,[e("span",W,[t[9]||(t[9]=x("Založil: ")),e("span",ee,o(s.value.user.full_name),1)]),e("span",te,[t[10]||(t[10]=x("Založeno: ")),e("span",se,o(a(y)(s.value.created_at).format("DD.MM.YYYY")),1)]),e("span",null,[t[11]||(t[11]=e("span",{class:"pr-3"},"Stav:",-1)),s.value.state.id==1?(r(),i("span",ae,o(s.value.state.name),1)):u("",!0),s.value.state.id==2||s.value.state.id==3?(r(),i("span",oe,o(s.value.state.name),1)):u("",!0),s.value.state.id==4?(r(),i("span",le,[e("span",ne,o(s.value.state.name),1),e("span",re,o(a(y)(s.value.updated_at).format("DD.MM.YYYY"))+" - "+o(s.value.user.full_name),1)])):u("",!0)])])]),e("div",ie,[t[12]||(t[12]=e("span",{class:"bg-main-color-100 text-main-color-700 rounded-lg px-3 py-1 font-semibold text-xs absolute -top-3"},"Popis požadavku",-1)),e("p",de,o(s.value.text),1)]),e("div",null,[t[18]||(t[18]=e("h2",{class:"text-lg pl-2 py-6 text-gray-900"},"Komunikace k požadavku:",-1)),e("ul",ue,[s.value.state.id!=4&&(a(g).check("tickets.edit")||a(g).check("tickets.master"))?(r(),i("li",ce,[s.value.answers&&s.value.answers.length?(r(),i("div",me,t[13]||(t[13]=[e("div",{class:"w-[2px] bg-gray-400"},null,-1)]))):u("",!0),e("div",pe,[t[15]||(t[15]=e("div",{class:"relative flex h-6 w-6 flex-none items-center justify-center bg-transparent"},[e("div",{class:"h-2.5 w-2.5 rounded-full bg-gray-400 ring-1 ring-gray-400"})],-1)),e("div",ve,[e("span",xe,o(a(Y).user.full_name)+":",1)]),d(a(E),{onSubmit:t[2]||(t[2]=n=>D()),class:"rounded-lg grow sm:flex w-full sm:w-auto pl-8 sm:pl-0"},{default:c(({values:n})=>[e("div",ge,[d(a(N),{as:"textarea",rules:"required",modelValue:p.value,"onUpdate:modelValue":t[1]||(t[1]=k=>p.value=k),rows:"2",name:"answer_text",id:"answer_text",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte text odpovědi k požadavku..."},null,8,["modelValue"]),d(a(z),{name:"answer_text",class:"text-rose-400 text-sm block pt-1"})]),e("div",fe,[e("button",be,[d(a(U),{class:"h-6 w-6 text-white","aria-hidden":"true"}),t[14]||(t[14]=e("span",{class:"whitespace-nowrap"},"Přidat odpověď",-1))])])]),_:1})])])):u("",!0),(r(!0),i(h,null,I(s.value.answers,(n,k)=>(r(),i("li",{key:n.id,class:"relative flex gap-x-4"},[e("div",{class:q([k===s.value.answers.length-1?"h-16":"-bottom-6","absolute left-0 top-0 flex w-6 justify-center"])},t[16]||(t[16]=[e("div",{class:"w-[2px] bg-gray-400"},null,-1)]),2),e("div",_e,[t[17]||(t[17]=e("div",{class:"relative flex h-6 w-6 flex-none items-center justify-center bg-transparent"},[e("div",{class:"h-2.5 w-2.5 rounded-full bg-gray-400 ring-1 ring-gray-400"})],-1)),e("div",ke,[e("span",we,o(n.user.full_name)+":",1),e("span",ye,o(a(y)(n.created_at).format("DD.MM.YYYY HH:MM")),1)]),e("div",he,[e("p",Ce,o(n.text),1)])])]))),128))])])])):u("",!0),d(a(L),{appear:"",show:_.value,as:"template",onClose:t[6]||(t[6]=n=>v())},{default:c(()=>[d(K,null,{"modal-title":c(()=>t[19]||(t[19]=[x("Vyřešit požadavek")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:t[3]||(t[3]=n=>v())},[d(a(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[t[20]||(t[20]=e("div",{class:"p-6"},[e("span",null,"Opravdu chcete požadavek vyřešit?")],-1)),e("div",Me,[e("div",Ye,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:t[4]||(t[4]=M(n=>v(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:t[5]||(t[5]=M(n=>T(),["prevent"]))}," Vyřešit ")])])]),_:1})]),_:1},8,["show"])],64)}}};export{Ie as default};

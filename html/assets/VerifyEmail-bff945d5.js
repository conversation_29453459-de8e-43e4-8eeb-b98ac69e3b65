import{k as $,A as M,a as d,m as O,x,r as R,o as y,b,B as i,C as n,d as e,u as s,l as T,e as a,w as p,t as G,F as H,y as U,E as I,z as Y,h as _}from"./index-9d9f1067.js";const J={class:"flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8"},L={class:"sm:mx-auto sm:w-full sm:max-w-md"},Q=["src"],W={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},X={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10"},ee={class:"mt-2"},te={class:"mt-4 text-center text-sm text-gray-600"},se={class:"flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8"},oe={class:"sm:mx-auto sm:w-full sm:max-w-md"},le=["src"],ae={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},ie={class:"mt-2 text-center text-sm text-gray-600"},ne={class:"mt-8 text-center text-sm text-gray-600"},me={class:"text-left mt-8 sm:mx-auto sm:w-full sm:max-w-md"},ue={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10"},re={class:"mt-2"},de={class:"mt-4 text-center text-sm text-gray-600"},ve={__name:"VerifyEmail",setup(pe){const o=$(),h=M(),m=d(""),c=d(!1),u=d(!1),v=d(!1),g=d("");h.query.token?(u.value=!0,c.value=!0,O.get("/api/auth/verify-email?token="+h.query.token).then(l=>{g.value="Emailová adresa byla ověřena, nyní se můžete přihlásit do systému.",v.value=!0,x.success(l.data.message)}).catch(l=>{g.value="Emailovou adresu se nepodařilo ověřit. Zkuste tuto akci opakovat zasláním nového ověřovacího emailu. Chyba: "+l.response.data.message,v.value=!1,x.error("Email se nepodařilo ověřit")}),u.value=!1):c.value=!1;function w(){O.post("/api/auth/verify-email",{email:m.value}).then(l=>{x.success(l.data.message)}).catch(l=>{x.error(l.response.data.message)})}return(l,t)=>{var k,S,V,j,E,C,q,z,B,F,N,Z,P,A,D,K;const f=R("router-link");return y(),b(H,null,[i(e("div",J,[e("div",L,[(S=(k=s(o))==null?void 0:k.appSettings)!=null&&S.logo_small_url||(j=(V=s(o))==null?void 0:V.appSettings)!=null&&j.logo_url?(y(),b("img",{key:0,class:"mx-auto h-12 w-auto",src:((C=(E=s(o))==null?void 0:E.appSettings)==null?void 0:C.logo_small_url)||((z=(q=s(o))==null?void 0:q.appSettings)==null?void 0:z.logo_url),alt:"Your Company"},null,8,Q)):T("",!0),t[4]||(t[4]=e("h2",{class:"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900"},"Ověření emailové adresy",-1)),t[5]||(t[5]=e("p",{class:"mt-2 text-center text-sm text-gray-600"}," Pro přístup do systému je nutné ověřit emailovou adresu. Zkontrolujte Vaši emailovou schránku (také složku spam) a email verifikujte. V případě, že Vám email k ověření nepřišel do pár minut od založení účtu, zažádejte si níže o nový. ",-1))]),e("div",W,[e("div",X,[a(s(Y),{onSubmit:t[1]||(t[1]=r=>w()),class:"space-y-6",action:"#"},{default:p(()=>[e("div",null,[t[6]||(t[6]=e("label",{for:"email",class:"block text-sm font-medium leading-6 text-gray-900"},"Email",-1)),e("div",ee,[a(s(U),{rules:"required|email",modelValue:m.value,"onUpdate:modelValue":t[0]||(t[0]=r=>m.value=r),id:"email",name:"email",type:"email",autocomplete:"email",required:"",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},null,8,["modelValue"]),a(s(I),{name:"email",class:"text-rose-400 text-sm block pt-1"})])]),t[7]||(t[7]=e("div",null,[e("button",{type:"submit",class:"flex w-full justify-center rounded-md bg-main-color-600 py-2 px-3 text-sm font-semibold text-white shadow-sm hover:bg-main-color-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-main-color-600"},"Zaslat nový ověřovací email")],-1))]),_:1})])]),e("p",te,[a(f,{to:{name:"contact-support"},class:"text-sm font-semibold text-gray-900"},{default:p(()=>t[8]||(t[8]=[_(" Kontaktovat podporu "),e("span",{"aria-hidden":"true"},"→",-1)])),_:1})])],512),[[n,!c.value]]),i(e("div",se,[e("div",oe,[(F=(B=s(o))==null?void 0:B.appSettings)!=null&&F.logo_small_url||(Z=(N=s(o))==null?void 0:N.appSettings)!=null&&Z.logo_url?(y(),b("img",{key:0,class:"mx-auto h-12 w-auto",src:((A=(P=s(o))==null?void 0:P.appSettings)==null?void 0:A.logo_small_url)||((K=(D=s(o))==null?void 0:D.appSettings)==null?void 0:K.logo_url),alt:"Your Company"},null,8,le)):T("",!0),t[9]||(t[9]=e("h2",{class:"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900"},"Ověřování emailové adresy",-1))]),e("div",ae,[i(e("p",ie," Prosíme vyčkejte, právě oveřujeme Vaši emailovou adresu. ",512),[[n,u.value]]),i(e("p",{class:"mt-2 text-center text-sm text-gray-600"},G(g.value),513),[[n,!u.value]])]),i(e("div",ne,[i(a(f,{to:{name:"login"},class:"font-medium text-main-color-600 hover:text-main-color-500"},{default:p(()=>t[10]||(t[10]=[_(" Přihlásit se ")])),_:1},512),[[n,v.value]]),i(e("div",me,[e("div",ue,[a(s(Y),{onSubmit:t[3]||(t[3]=r=>w()),class:"space-y-6"},{default:p(()=>[e("div",null,[t[11]||(t[11]=e("label",{for:"email",class:"block text-sm font-medium leading-6 text-gray-900"},"Email",-1)),e("div",re,[a(s(U),{rules:"required|email",modelValue:m.value,"onUpdate:modelValue":t[2]||(t[2]=r=>m.value=r),id:"email",name:"email",type:"email",autocomplete:"email",required:"",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},null,8,["modelValue"])])]),t[12]||(t[12]=e("div",null,[e("button",{type:"submit",class:"flex w-full justify-center rounded-md bg-main-color-600 py-2 px-3 text-sm font-semibold text-white shadow-sm hover:bg-main-color-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-main-color-600"},"Zaslat nový ověřovací email")],-1))]),_:1})])],512),[[n,!v.value]])],512),[[n,!u.value]]),e("p",de,[a(f,{to:{name:"contact-support"},class:"text-sm font-semibold text-gray-900"},{default:p(()=>t[13]||(t[13]=[_(" Kontaktovat podporu "),e("span",{"aria-hidden":"true"},"→",-1)])),_:1})])],512),[[n,c.value]])],64)}}};export{ve as default};

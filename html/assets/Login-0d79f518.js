import{a as u,j as P,o as n,b as d,d as s,e as r,w as U,u as a,k as m,l as E,m as b,p as L,q as M,s as w,v as x,E as f,c as h,t as k,x as B,F,f as N,y as A}from"./index-c2402147.js";import{E as D,a as I}from"./index-a54879fc.js";const Z="/assets/logo-color-285c21c3.svg",G={class:"flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8 bg-center bg-[url('/src/assets/login-bg.jpg')]"},H={class:"mt-8 sm:mx-auto sm:w-full sm:max-w-md"},R={class:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10 relative"},Y={key:0},$={class:"mt-2"},z={key:1},J={class:"mt-2"},K={class:"mt-2 relative"},O={key:0},Q={class:"mt-4"},W={class:"space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0"},X=["for"],te={__name:"Login",setup(T){const i=u("ad"),V=P("debugModeGlobalVar"),y=u(""),v=u(""),c=u(""),p=u(!1),C=()=>{p.value=!p.value},S=[{id:"ad",title:"Účet Active Directory"},{id:"local",title:"Lokální účet"}];async function _(){try{const o=await m.get("/api/items/custom-columns");b().setCustomColumns(o.data.data)}catch(o){console.error("Chyba při načítání vlastních sloupců:",o)}}async function j(){try{const o=await m.get("/api/modules");b().setModules(o.data.data)}catch(o){console.error("Chyba při načítání modulů:",o)}}async function q(){var o,e;try{await m.get("/sanctum/csrf-cookie");let l;if(i.value==="local"?l=await m.post("/api/auth/login",{email:y.value,password:c.value}):i.value==="ad"&&(l=await m.post("/api/auth/login-ad",{username:v.value,password:c.value})),l.status===200){const t=E(),g=b();g.setUser(l.data.data.user),g.setPermissions(l.data.data.permissions),t.setIsLoged(!0),await _(),await j(),await L(),M.push({name:"users"}),w.success("Přihlášení bylo úspěšné")}}catch(l){console.error("Chyba při přihlášení:",l),w.error(((e=(o=l.response)==null?void 0:o.data)==null?void 0:e.message)||"Chyba při přihlášení")}}return(o,e)=>(n(),d("div",G,[e[11]||(e[11]=s("div",{class:"sm:mx-auto sm:w-full sm:max-w-md"},[s("img",{class:"mx-auto h-12 w-auto",src:Z,alt:"Your Company"}),s("h2",{class:"mt-6 text-center text-3xl font-bold tracking-tight text-gray-900"},"Přihlášení do systému"),s("p",{class:"mt-2 text-center text-lg text-gray-600"}," Správa Active Directory ")],-1)),s("div",H,[s("div",R,[r(a(A),{onSubmit:e[4]||(e[4]=l=>q()),class:"space-y-6"},{default:U(({values:l})=>[i.value=="local"?(n(),d("div",Y,[e[5]||(e[5]=s("label",{for:"email",class:"block text-sm font-medium leading-6 text-gray-900"},"Email",-1)),s("div",$,[r(a(x),{rules:"required|email",modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=t=>y.value=t),id:"email",name:"email",type:"email",autocomplete:"email",required:"",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte emailovou adresu..."},null,8,["modelValue"]),r(a(f),{name:"email",class:"text-rose-400 text-sm block pt-1"})])])):(n(),d("div",z,[e[6]||(e[6]=s("label",{for:"username",class:"block text-sm font-medium leading-6 text-gray-900"},"Přihlašovací jméno",-1)),s("div",J,[r(a(x),{rules:"required",modelValue:v.value,"onUpdate:modelValue":e[1]||(e[1]=t=>v.value=t),id:"username",name:"username",type:"text",required:"",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte uživatelké jméno..."},null,8,["modelValue"]),r(a(f),{name:"username",class:"text-rose-400 text-sm block pt-1"})])])),s("div",null,[e[7]||(e[7]=s("label",{for:"password",class:"block text-sm font-medium leading-6 text-gray-900"},"Heslo",-1)),s("div",K,[r(a(x),{rules:"required",modelValue:c.value,"onUpdate:modelValue":e[2]||(e[2]=t=>c.value=t),id:"password",name:"password",type:p.value?"text":"password",autocomplete:"off",required:"",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte Vaše heslo..."},null,8,["modelValue","type"]),r(a(f),{name:"password",class:"text-rose-400 text-sm block pt-1"}),s("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:C},[p.value?(n(),h(a(D),{key:0,name:"eye-off",class:"h-5 w-5"})):(n(),h(a(I),{key:1,name:"eye",class:"h-5 w-5"}))])])]),s("div",null,[a(V)?(n(),d("span",O,k(i.value),1)):B("",!0),s("div",null,[e[9]||(e[9]=s("p",{class:"text-sm"},"Typ Přihlášení:",-1)),s("fieldset",Q,[e[8]||(e[8]=s("legend",{class:"sr-only"},"Notification method",-1)),s("div",W,[(n(),d(F,null,N(S,t=>s("div",{key:t.id,class:"flex items-center"},[r(a(x),{rules:"requiredRadio",id:t.id,modelValue:i.value,"onUpdate:modelValue":e[3]||(e[3]=g=>i.value=g),name:"selectedLogin",type:"radio",checked:i.value==t.id,value:t.id,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","modelValue","checked","value"]),s("label",{label:"",for:t.id,class:"ml-3 block text-sm leading-6 text-gray-900 cursor-pointer"},k(t.title),9,X)])),64)),r(a(f),{name:"selectedLogin",class:"text-rose-400 text-sm block pt-1"})])])])]),e[10]||(e[10]=s("div",null,[s("button",{type:"submit",class:"flex w-full justify-center rounded-md bg-pink-600 py-2 px-3 text-sm font-semibold text-white shadow-sm hover:bg-pink-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-pink-600"}," Přihlásit se")],-1))]),_:1})])])]))}};export{te as default};

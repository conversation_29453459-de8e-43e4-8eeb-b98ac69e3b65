import{a as p,o as i,c as $,w as r,e as a,h as C,d as e,u as s,z as D,x as V,E as P,b,f as q,n as Z,t as z,y as R,F as U,l as M,v as j,H as S,D as E}from"./index-04cb9130.js";import{_ as H}from"./AppTopbar-73f760e5.js";import{_ as L}from"./pagination-813f544d.js";import{L as O,y as K,o as T}from"./index-4485c523.js";import{c as N}from"./checkPermission.service-6ab2ad75.js";import{X as I,e as X,C as A}from"./index-b2d17f43.js";import{_ as B}from"./basicModal-ebaee5b8.js";import{N as G,H as J,$ as Q,K as W,U as Y,_ as ee}from"./combobox-b49acf8a.js";import{S as F}from"./transition-14eed2e8.js";import"./listbox-b603cc4e.js";import"./hidden-0aacd91d.js";import"./use-tracked-pointer-3bd62e78.js";import"./use-resolve-button-type-0913c19f.js";import"./use-controllable-f4b23304.js";import"./dialog-a0a12ec2.js";import"./use-tree-walker-dc102a2d.js";const oe={class:"p-6"},te={class:""},se={class:"mb-5"},le={class:"mt-2"},ae={class:"mb-5"},re={class:"mt-2"},ne={class:"relative mt-2"},ie={__name:"createRoleModal",props:{rolesProp:Object},emits:["reloadRoles"],setup(h,{expose:_,emit:w}){const m=p({}),d=w,c=p(!1);function g(){c.value=!1}function x(){c.value=!0,m.value={}}function y(){M.post("/api/roles",{name:m.value.name,role_priority:m.value.role_priority,copy:m.value.copy_role.id}).then(v=>{j.success(v.data.message),d("reloadRoles",!0),this.closeModal()}).catch(v=>{j.error(v.response.data.message)})}return _({openModal:x}),(v,o)=>(i(),$(s(F),{appear:"",show:c.value,as:"template",onClose:g},{default:r(()=>[a(B,null,{"modal-title":r(()=>o[4]||(o[4]=[C("Vytvoření nové role")])),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:g},[a(s(I),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[a(s(D),{onSubmit:o[3]||(o[3]=f=>y())},{default:r(({values:f})=>[e("div",oe,[e("div",te,[e("div",se,[o[5]||(o[5]=e("label",{for:"name",class:"block leading-6 text-gray-900"},"Název role:",-1)),e("div",le,[a(s(V),{rules:"required",modelValue:m.value.name,"onUpdate:modelValue":o[0]||(o[0]=t=>m.value.name=t),type:"text",name:"name",id:"name",autocomplete:"given-name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název role..."},null,8,["modelValue"]),a(s(P),{name:"name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",ae,[o[6]||(o[6]=e("label",{for:"name",class:"block leading-6 text-gray-900"},"Priorita role:",-1)),e("div",re,[a(s(V),{rules:"required|minMax:1",modelValue:m.value.role_priority,"onUpdate:modelValue":o[1]||(o[1]=t=>m.value.role_priority=t),type:"number",name:"priority",id:"priority",autocomplete:"given-priority",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zvolte číselnou prioritu role..."},null,8,["modelValue"]),a(s(P),{name:"priority",class:"text-rose-400 text-sm block pt-1"})])]),a(s(V),{rules:"required",name:"copyrole"},{default:r(({handleChange:t,value:u})=>[a(s(G),{as:"div",modelValue:m.value.copy_role,"onUpdate:modelValue":[o[2]||(o[2]=l=>m.value.copy_role=l),t],class:"relative"},{default:r(()=>[a(s(J),{class:"block leading-6 text-gray-900"},{default:r(()=>o[7]||(o[7]=[C("Převzít nastavení z existující role:")])),_:1}),e("div",ne,[a(s(Q),{class:"placeholder-gray-400 w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",onChange:l=>v.query=l.target.value,"display-value":l=>l==null?void 0:l.name,placeholder:"Zvolte existující roli..."},null,8,["onChange","display-value"]),a(s(W),{class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"},{default:r(()=>[a(s(X),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1}),h.rolesProp&&h.rolesProp.length&&h.rolesProp.length>0?(i(),$(s(Y),{key:0,class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:r(()=>[(i(!0),b(U,null,q(h.rolesProp,l=>(i(),$(s(ee),{key:l.id,value:l,as:"template"},{default:r(({active:n,selected:k})=>[e("li",{class:Z(["relative cursor-default select-none py-2 pl-3 pr-9",n?"bg-main-color-600 text-white":"text-gray-900"])},[e("span",{class:Z(["block truncate",k&&"font-semibold"])},z(l.name),3),k?(i(),b("span",{key:0,class:Z(["absolute inset-y-0 right-0 flex items-center pr-4",n?"text-white":"text-main-color-600"])},[a(s(A),{class:"h-5 w-5","aria-hidden":"true"})],2)):R("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):R("",!0)])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),a(s(P),{name:"copyrole",class:"text-rose-400 text-sm block pt-1"})])]),e("div",{class:"border-t p-5"},[e("div",{class:"text-right"},[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:g},o[8]||(o[8]=[e("span",{class:"text-main-color-600"},"Zavřít",-1)])),o[9]||(o[9]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},de={class:"sm:col-span-3 flex items-center border-b p-6"},ue={class:"grow"},me={class:"mb-5 p-6 flex items-center border-b"},ce={class:"grow"},pe={class:"p-6"},ge={class:"flex items-center"},ve={class:"mt-6 mb-10 space-y-10"},fe={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10"},be={class:"text-sm font-semibold leading-6 text-main-color-600"},xe={class:"relative flex gap-x-3"},ye={class:"flex h-6 items-center"},he={class:"text-sm leading-6"},_e=["for"],we={__name:"editRoleModal",props:{permissionsCategoriesProp:Object},emits:["reloadRoles"],setup(h,{expose:_,emit:w}){const m=w,d=p({}),c=p([]),g=p(!1);function x(){g.value=!1}function y(f){g.value=!0,v(f)}function v(f){M.get("/api/roles/"+f.id).then(t=>{d.value=f,c.value=t.data.data.permission_names}).catch(t=>{console.log(t)})}function o(){M.post("/api/roles/"+d.value.id+"/update",{name:d.value.name,permissions:c.value,role_priority:d.value.role_priority}).then(f=>{j.success(f.data.message),m("reloadRoles",!0),this.closeModal()}).catch(f=>{j.error(f.response.data.message)})}return _({openModal:y}),(f,t)=>(i(),$(s(F),{appear:"",show:g.value,as:"template",onClose:x},{default:r(()=>[a(B,{size:"lg"},{"modal-title":r(()=>t[4]||(t[4]=[C("Úprava role")])),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:x},[a(s(I),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[a(s(D),{onSubmit:t[3]||(t[3]=u=>o())},{default:r(({values:u})=>[e("div",de,[t[5]||(t[5]=e("label",{for:"role-name",class:"mr-8 leading-6 text-gray-900"},"Název role:",-1)),e("div",ue,[a(s(V),{rules:"required",modelValue:d.value.name,"onUpdate:modelValue":t[0]||(t[0]=l=>d.value.name=l),type:"text",name:"role-name",id:"role-name",autocomplete:"given-name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název role..."},null,8,["modelValue"]),a(s(P),{name:"role-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",me,[t[6]||(t[6]=e("label",{for:"name",class:"mr-8 block leading-6 text-gray-900 w-24"},"Priorita role:",-1)),e("div",ce,[a(s(V),{rules:"required|minMax:1",modelValue:d.value.role_priority,"onUpdate:modelValue":t[1]||(t[1]=l=>d.value.role_priority=l),type:"number",name:"role_priority",id:"role_priority",autocomplete:"given-priority",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zvolte číselnou prioritu role..."},null,8,["modelValue"]),a(s(P),{name:"role_priority",class:"text-rose-400 text-sm block pt-1"})])]),e("div",pe,[e("div",ge,[a(s(O),{class:"h-7 w-7 text-gray-900","aria-hidden":"true"}),t[7]||(t[7]=e("span",{class:"text-lg pl-4 font-light"},"Nastavení oprávnění role",-1))]),e("div",ve,[e("fieldset",fe,[(i(!0),b(U,null,q(h.permissionsCategoriesProp,l=>(i(),b("div",{key:l.id},[e("legend",be,z(l.name),1),(i(!0),b(U,null,q(l.permissions,n=>(i(),b("div",{class:"mt-6 space-y-6",key:n.id},[e("div",xe,[e("div",ye,[a(s(V),{id:n.id,name:"rolesPermissions",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",modelValue:c.value,"onUpdate:modelValue":t[2]||(t[2]=k=>c.value=k),value:n.name},null,8,["id","modelValue","value"])]),e("div",he,[e("label",{for:n.id,class:"font-medium text-gray-900 cursor-pointer"},z(n.human_name),9,_e)])])]))),128))]))),128))])])]),e("div",{class:"border-t p-5"},[e("div",{class:"text-right"},[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:x},t[8]||(t[8]=[e("span",{class:"text-main-color-600"},"Zavřít",-1)])),t[9]||(t[9]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},ke={class:"p-6"},Re={class:"py-6"},$e={class:"border-t p-5"},Ce={class:"text-right"},Ve={__name:"deleteRoleModal",emits:["reloadRoles"],setup(h,{expose:_,emit:w}){const m=w,d=p({}),c=p(!1);function g(){c.value=!1}function x(v){c.value=!0,d.value=v}async function y(v){await M.post("/api/roles/"+v+"/delete").then(o=>{j.success(o.data.message),m("reloadRoles",!0),g()}).catch(o=>{console.log(o)})}return _({openModal:x}),(v,o)=>(i(),$(s(F),{appear:"",show:c.value,as:"template",onClose:g},{default:r(()=>[a(B,null,{"modal-title":r(()=>o[1]||(o[1]=[C("Smazat roli ")])),"modal-close-button":r(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:g},[a(s(I),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":r(()=>[e("div",ke,[e("div",Re,[e("p",null,[o[2]||(o[2]=C("Opravdu si přejete roli ")),e("strong",null,z(d.value.name),1),o[3]||(o[3]=C(" smazat?"))])])]),e("div",$e,[e("div",Ce,[e("button",{class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",onClick:o[0]||(o[0]=S(f=>y(d.value.id),["prevent"]))}," Smazat roli ")])])]),_:1})]),_:1},8,["show"]))}},Me={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"},Pe=["onClick"],ze=["onClick"],Ue={key:1,class:"text-gray-900 text-sm text-center col-span-3"},Ae={__name:"Roles",setup(h){const _=p(null),w=p(null),m=p(null),d=p({}),c=p({}),g=p(null),x=p(!1),y=p(1),v=p(["roles"]);E(()=>{x.value=!0,o(),t(),x.value=!1});async function o(){await M.get("/api/roles?page="+y.value).then(u=>{c.value=u.data.data,g.value=u.data.meta}).catch(u=>{console.log(u)})}function f(u){y.value=u,o()}function t(){M.get("/api/permission-categories/permissions").then(u=>{d.value=u.data}).catch(u=>{console.log(u)})}return(u,l)=>(i(),b(U,null,[a(H,{breadCrumbs:v.value},{topbarButtons:r(()=>[s(N).check("roles.create")?(i(),b("button",{key:0,class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:l[0]||(l[0]=S(n=>u.$refs.createRoleRef.openModal(),["prevent"]))},"Přidat Roli")):R("",!0)]),_:1},8,["breadCrumbs"]),e("div",null,[e("div",Me,[s(N).check("roles.read")?(i(!0),b(U,{key:0},q(c.value,n=>(i(),b("div",{key:n.id,class:"bg-white border border-zinc-200/70 rounded-md p-5 flex justify-between items-center"},[e("div",null,z(n.name),1),e("div",null,[s(N).check("roles.delete")?(i(),b("button",{key:0,class:"mr-2",onClick:S(k=>u.$refs.deleteRoleRef.openModal(n),["prevent"])},[a(s(K),{class:"mx-auto h-9 w-9 p-2 text-red-600 bg-red-200/70 hover:bg-red-200 rounded-lg","aria-hidden":"true"})],8,Pe)):R("",!0),s(N).check("roles.edit")?(i(),b("button",{key:1,onClick:S(k=>u.$refs.editRoleRef.openModal(n),["prevent"])},[a(s(T),{class:"mx-auto h-9 w-9 p-2 bg-main-color-50 text-main-color-600 hover:bg-main-color-100 rounded-lg","aria-hidden":"true"})],8,ze)):R("",!0)])]))),128)):(i(),b("div",Ue,l[5]||(l[5]=[e("span",null,"Na zobrazení rolí nemáte dostatečná práva.",-1)])))]),g.value!==null?(i(),$(L,{key:0,meta:g.value,onSetPage:f,modelValue:y.value,"onUpdate:modelValue":l[1]||(l[1]=n=>y.value=n)},null,8,["meta","modelValue"])):R("",!0)]),a(ie,{ref_key:"createRoleRef",ref:_,onReloadRoles:l[2]||(l[2]=n=>(o(),t())),rolesProp:c.value},null,8,["rolesProp"]),a(we,{ref_key:"editRoleRef",ref:w,onReloadRoles:l[3]||(l[3]=n=>(o(),t())),permissionsCategoriesProp:d.value},null,8,["permissionsCategoriesProp"]),a(Ve,{ref_key:"deleteRoleRef",ref:m,onReloadRoles:l[4]||(l[4]=n=>(o(),t()))},null,512)],64))}};export{Ae as default};

import{a as _,o as m,c as M,w as i,e as s,h as U,d as e,u as t,x as B,s as f,E as x,k as V,q as S,b as p,f as q,n as N,t as Z,v as k,F as j,j as Y,B as ee,C as E}from"./index-3de9bee7.js";import{_ as te}from"./AppTopbar-27ebc835.js";import{l as I,v as O,C as T,w as D,s as H}from"./index-ddf5f523.js";import{c as z}from"./checkPermission.service-9981644d.js";import{_ as F}from"./basicModal-d721522e.js";import{S as K}from"./transition-97ad7178.js";import{N as G,H as L,$ as X,K as A,U as J,_ as Q}from"./combobox-682118fa.js";import"./dialog-ddee7d0d.js";import"./hidden-7b234e84.js";import"./use-tracked-pointer-ed292bdb.js";import"./use-resolve-button-type-8b834d9a.js";import"./use-tree-walker-b9bc1be5.js";import"./use-controllable-4f60503e.js";const se={class:"p-6"},oe={class:"sm:col-span-3 mb-5"},ae=e("label",{for:"name",class:"block leading-6 text-gray-900"},"Název kategorie:",-1),ne={class:"mt-2"},le={class:"sm:col-span-3"},re=e("label",{for:"position",class:"block leading-6 text-gray-900"},"Pozice kategorie:",-1),ie={class:"mt-2"},de={class:"border-t p-5"},me={class:"text-right"},ce=e("span",{class:"text-main-color-600"},"Zavřít",-1),ue=[ce],pe=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ",-1),ge={__name:"createCategoryModal",emits:["reloadPermissions"],setup(R,{expose:$,emit:y}){const c=_({}),o=_(!1);function g(){o.value=!1}function b(){o.value=!0,c.value={}}function C(){V.post("/api/permission-categories",{name:c.value.name,position:c.value.position}).then(h=>{S.success(h.data.message),y("reloadPermissions",!0),g()}).catch(h=>{console.log(h)})}return $({openModal:b}),(h,n)=>(m(),M(t(K),{appear:"",show:o.value,as:"template",onClose:n[5]||(n[5]=l=>g())},{default:i(()=>[s(F,null,{"modal-title":i(()=>[U("Vytvoření nové kategorie")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:n[0]||(n[0]=l=>g())},[s(t(I),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[s(t(B),{onSubmit:n[4]||(n[4]=l=>C())},{default:i(({values:l})=>[e("div",se,[e("div",oe,[ae,e("div",ne,[s(t(f),{rules:"required",modelValue:c.value.name,"onUpdate:modelValue":n[1]||(n[1]=a=>c.value.name=a),type:"text",name:"name",id:"name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název kategorie oprávnění"},null,8,["modelValue"]),s(t(x),{name:"name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",le,[re,e("div",ie,[s(t(f),{rules:"required",modelValue:c.value.position,"onUpdate:modelValue":n[2]||(n[2]=a=>c.value.position=a),type:"number",name:"position",id:"position",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte číselnou pozici kategorie"},null,8,["modelValue"]),s(t(x),{name:"position",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",de,[e("div",me,[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:n[3]||(n[3]=a=>g())},ue),pe])])]),_:1})]),_:1})]),_:1},8,["show"]))}},ve={class:"p-6"},he={class:"sm:col-span-3 mb-5"},be=e("label",{for:"name",class:"block leading-6 text-gray-900"},"Systémový název",-1),_e={class:"mt-2"},fe={class:"sm:col-span-3 mb-5"},xe=e("label",{for:"human_name",class:"block leading-6 text-gray-900"},"Název oprávnění",-1),ye={class:"mt-2"},ke={class:"sm:col-span-3 mb-5"},we=e("label",{for:"position",class:"block leading-6 text-gray-900"},"Pozice oprávnění",-1),$e={class:"mt-2"},Ce={class:"relative mt-2"},Pe={class:"border-t p-5"},Ve={class:"text-right"},ze=e("span",{class:"text-main-color-600"},"Zavřít",-1),Me=[ze],je=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ",-1),Ue={__name:"createPermissionModal",props:{categoriesProp:Object},emits:["reloadPermissions"],setup(R,{expose:$,emit:y}){const c=R,o=_({}),g=_(!1);function b(){g.value=!1}function C(){g.value=!0,o.value={}}function h(){V.post("/api/permissions",{name:o.value.name,human_name:o.value.human_name,position:o.value.position,category_id:o.value.selected_category.id}).then(n=>{S.success(n.data.message),y("reloadPermissions",!0),b()}).catch(n=>{console.log(n)})}return $({openModal:C}),(n,l)=>(m(),M(t(K),{appear:"",show:g.value,as:"template",onClose:l[7]||(l[7]=a=>b())},{default:i(()=>[s(F,null,{"modal-title":i(()=>[U("Vytvoření nového oprávnění")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:l[0]||(l[0]=a=>b())},[s(t(I),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[s(t(B),{onSubmit:l[6]||(l[6]=a=>h())},{default:i(({values:a})=>[e("div",ve,[e("div",he,[be,e("div",_e,[s(t(f),{rules:"required",modelValue:o.value.name,"onUpdate:modelValue":l[1]||(l[1]=v=>o.value.name=v),type:"text",name:"name",id:"name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte kód oprávnění, např. user.create"},null,8,["modelValue"]),s(t(x),{name:"name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",fe,[xe,e("div",ye,[s(t(f),{rules:"required",modelValue:o.value.human_name,"onUpdate:modelValue":l[2]||(l[2]=v=>o.value.human_name=v),type:"text",name:"human_name",id:"human_name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"zadejte název oprávnění, např. Úprava uživatele"},null,8,["modelValue"]),s(t(x),{name:"human_name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",ke,[we,e("div",$e,[s(t(f),{rules:"required",modelValue:o.value.position,"onUpdate:modelValue":l[3]||(l[3]=v=>o.value.position=v),type:"number",name:"position",id:"position",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte číselnou pozici oprávnění"},null,8,["modelValue"]),s(t(x),{name:"position",class:"text-rose-400 text-sm block pt-1"})])]),s(t(f),{name:"category",rules:"requiredRadio"},{default:i(({handleChange:v,value:u})=>[s(t(G),{as:"div",modelValue:o.value.selected_category,"onUpdate:modelValue":[l[4]||(l[4]=r=>o.value.selected_category=r),v]},{default:i(()=>[s(t(L),{class:"block leading-6 text-gray-900"},{default:i(()=>[U("Kategorie oprávnění")]),_:1}),e("div",Ce,[s(t(X),{class:"placeholder-gray-400 w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",onChange:r=>n.query=r.target.value,"display-value":r=>r==null?void 0:r.name,placeholder:"Zvolte kategorii oprávnění"},null,8,["onChange","display-value"]),s(t(A),{class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"},{default:i(()=>[s(t(O),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1}),c.categoriesProp.length>0?(m(),M(t(J),{key:0,class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:i(()=>[(m(!0),p(j,null,q(c.categoriesProp,r=>(m(),M(t(Q),{key:r.id,value:r,as:"template"},{default:i(({active:d,selected:w})=>[e("li",{class:N(["relative cursor-default select-none py-2 pl-3 pr-9",d?"bg-main-color-600 text-white":"text-gray-900"])},[e("span",{class:N(["block truncate",w&&"font-semibold"])},Z(r.name),3),w?(m(),p("span",{key:0,class:N(["absolute inset-y-0 right-0 flex items-center pr-4",d?"text-white":"text-main-color-600"])},[s(t(T),{class:"h-5 w-5","aria-hidden":"true"})],2)):k("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):k("",!0)])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),s(t(x),{name:"category",class:"text-rose-400 text-sm block pt-1"})]),e("div",Pe,[e("div",Ve,[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:l[5]||(l[5]=v=>b())},Me),je])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Re={class:"p-6"},qe={class:"sm:col-span-3 mb-5"},Ze=e("label",{for:"name",class:"block leading-6 text-gray-900"},"Název kategorie:",-1),Ne={class:"mt-2"},Se={class:"sm:col-span-3"},Be=e("label",{for:"position",class:"block leading-6 text-gray-900"},"Pozice kategorie:",-1),Ie={class:"mt-2"},Fe=e("span",{class:"text-main-color-600"},"Zavřít",-1),Ke=[Fe],Ee=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ",-1),De={__name:"editCategoryModal",emits:["reloadPermissions"],setup(R,{expose:$,emit:y}){const c=_({}),o=_(!1);function g(){o.value=!1}function b(h){o.value=!0,c.value=h}function C(){V.post("/api/permission-categories/"+c.value.id+"/update",{name:c.value.name,position:c.value.position}).then(h=>{S.success(h.data.message),y("reloadPermissions",!0),g()}).catch(h=>{console.log(h)})}return $({openModal:b}),(h,n)=>(m(),M(t(K),{appear:"",show:o.value,as:"template",onClose:g},{default:i(()=>[s(F,null,{"modal-title":i(()=>[U("Úprava kategorie oprávnění")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:g},[s(t(I),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[s(t(B),{onSubmit:C},{default:i(({values:l})=>[e("div",Re,[e("div",qe,[Ze,e("div",Ne,[s(t(f),{rules:"required",value:c.value.name,modelValue:c.value.name,"onUpdate:modelValue":n[0]||(n[0]=a=>c.value.name=a),type:"text",name:"name",id:"name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název kategorie oprávnění"},null,8,["value","modelValue"]),s(t(x),{name:"name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Se,[Be,e("div",Ie,[s(t(f),{rules:"required",value:c.value.position,modelValue:c.value.position,"onUpdate:modelValue":n[1]||(n[1]=a=>c.value.position=a),type:"number",name:"position",id:"position",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte číselnou pozici kategorie"},null,8,["value","modelValue"]),s(t(x),{name:"position",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",{class:"border-t p-5"},[e("div",{class:"text-right"},[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:g},Ke),Ee])])]),_:1})]),_:1})]),_:1},8,["show"]))}},He={class:"p-6"},Oe={class:"sm:col-span-3"},Te=e("label",{for:"name",class:"block text-sm font-medium leading-6 text-gray-900"},"Systémový název",-1),Ge={class:"mt-2"},Le={class:"sm:col-span-3"},Xe=e("label",{for:"human_name",class:"block text-sm font-medium leading-6 text-gray-900"},"Název oprávnění",-1),Ae={class:"mt-2"},Je={class:"sm:col-span-3"},Qe=e("label",{for:"position",class:"block text-sm font-medium leading-6 text-gray-900"},"Pozice oprávnění",-1),We={class:"mt-2"},Ye={class:"relative mt-2"},et={class:"border-t p-5"},tt={class:"text-right"},st=e("span",{class:"text-main-color-600"},"Zavřít",-1),ot=[st],at=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ",-1),nt={__name:"editPermissionModal",props:{categoriesProp:Object},emits:["reloadPermissions"],setup(R,{expose:$,emit:y}){const c=R,o=_({}),g=_(!1);function b(){g.value=!1}function C(l){g.value=!0,n(l)}function h(){V.post("/api/permissions/"+o.value.id+"/update",{name:o.value.name,human_name:o.value.human_name,position:o.value.position,category_id:o.value.selected_category.id}).then(l=>{S.success(l.data.message),y("reloadPermissions",!0),b()}).catch(l=>{console.log(l)})}function n(l){V.get("/api/permissions/"+l.id).then(a=>{o.value=a.data.data,o.value.selected_category=a.data.data.category}).catch(a=>{console.log(a)})}return $({openModal:C}),(l,a)=>(m(),M(t(K),{appear:"",show:g.value,as:"template",onClose:a[7]||(a[7]=v=>b())},{default:i(()=>[s(F,null,{"modal-title":i(()=>[U("Úprava oprávnění")]),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:a[0]||(a[0]=v=>b())},[s(t(I),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[s(t(B),{onSubmit:a[6]||(a[6]=v=>h())},{default:i(({values:v})=>[e("div",He,[e("div",Oe,[Te,e("div",Ge,[s(t(f),{rules:"required",modelValue:o.value.name,"onUpdate:modelValue":a[1]||(a[1]=u=>o.value.name=u),type:"text",name:"name",id:"name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte kód oprávnění, např. user.create"},null,8,["modelValue"]),s(t(x),{name:"name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Le,[Xe,e("div",Ae,[s(t(f),{rules:"required",modelValue:o.value.human_name,"onUpdate:modelValue":a[2]||(a[2]=u=>o.value.human_name=u),type:"text",name:"human_name",id:"human_name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"zadejte název oprávnění, např. Úprava uživatele"},null,8,["modelValue"]),s(t(x),{name:"human_name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Je,[Qe,e("div",We,[s(t(f),{rules:"required",modelValue:o.value.position,"onUpdate:modelValue":a[3]||(a[3]=u=>o.value.position=u),type:"number",name:"position",id:"position",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte číselnou pozici oprávnění"},null,8,["modelValue"]),s(t(x),{name:"position",class:"text-rose-400 text-sm block pt-1"})])]),s(t(f),{name:"category",rules:"requiredRadio"},{default:i(({handleChange:u,value:r})=>[s(t(G),{as:"div",modelValue:o.value.selected_category,"onUpdate:modelValue":[a[4]||(a[4]=d=>o.value.selected_category=d),u]},{default:i(()=>[s(t(L),{class:"block text-sm font-medium leading-6 text-gray-900"},{default:i(()=>[U("Kategorie oprávnění")]),_:1}),e("div",Ye,[s(t(X),{class:"w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",onChange:d=>l.query=d.target.value,"display-value":d=>d==null?void 0:d.name,placeholder:"Zvolte kategorii oprávnění"},null,8,["onChange","display-value"]),s(t(A),{class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"},{default:i(()=>[s(t(O),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1}),c.categoriesProp.length>0?(m(),M(t(J),{key:0,class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:i(()=>[(m(!0),p(j,null,q(c.categoriesProp,d=>(m(),M(t(Q),{key:d.id,value:d,as:"template"},{default:i(({active:w,selected:P})=>[e("li",{class:N(["relative cursor-default select-none py-2 pl-3 pr-9",w?"bg-main-color-600 text-white":"text-gray-900"])},[e("span",{class:N(["block truncate",P&&"font-semibold"])},Z(d.name),3),P?(m(),p("span",{key:0,class:N(["absolute inset-y-0 right-0 flex items-center pr-4",w?"text-white":"text-main-color-600"])},[s(t(T),{class:"h-5 w-5","aria-hidden":"true"})],2)):k("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):k("",!0)])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),s(t(x),{name:"category",class:"text-rose-400 text-sm block pt-1"})]),e("div",et,[e("div",tt,[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:a[5]||(a[5]=u=>b())},ot),at])])]),_:1})]),_:1})]),_:1},8,["show"]))}},lt={class:"px-4 sm:px-6 lg:px-8"},rt={class:"mt-8 flow-root"},it={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},dt={class:"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-8"},mt={class:"flex justify-between items-center gap-3 mb-4"},ct={class:"text-base font-semibold leading-5 text-main-color-600"},ut={class:"pr-3"},pt=["onClick"],gt=["onClick"],vt={class:"relative flex justify-between items-center"},ht={class:"font-light"},bt={class:"flex gap-2"},_t=["onClick"],ft=["onClick"],xt={key:1,class:"text-gray-900 text-sm text-center col-span-3"},yt=e("span",null,"Na zobrazení oprávnění nemáte dostatečná práva.",-1),kt=[yt],wt={key:1,class:"text-gray-900 text-sm text-center col-span-3"},$t=e("span",null,"Na zobrazení kategorií oprávnění nemáte dostatečná práva.",-1),Ct=[$t],Ft={__name:"Permissions",setup(R){const $=Y("debugModeGlobalVar"),y=_({}),c=_({}),o=_(["permissions"]),g=_(null),b=_(null),C=_(null),h=_(null);ee(()=>{n(),v()});function n(){V.get("/api/permission-categories/permissions").then(u=>{y.value=u.data}).catch(u=>{console.log(u)})}async function l(u){await V.post("/api/permissions/"+u+"/delete").then(r=>{S.success(r.data.message),n()}).catch(r=>{console.log(r)})}async function a(u){await V.post("/api/permission-categories/"+u+"/delete").then(r=>{S.success(r.data.message),n()}).catch(r=>{console.log(r)})}function v(){V.get("/api/permission-categories").then(u=>{c.value=u.data.data}).catch(u=>{console.log(u)})}return(u,r)=>(m(),p(j,null,[s(te,{breadCrumbs:o.value},{topbarButtons:i(()=>[t(z).check("permissions_categories.create")?(m(),p("button",{key:0,class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:r[0]||(r[0]=d=>u.$refs.createCategoryRef.openModal())},"Přidat kategorii")):k("",!0),t(z).check("permissions.create")?(m(),p("button",{key:1,class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:r[1]||(r[1]=d=>u.$refs.createPermissionRef.openModal())},"Přidat oprávnění")):k("",!0)]),_:1},8,["breadCrumbs"]),t($)==!0?(m(!0),p(j,{key:0},q(y.value,d=>(m(),p("div",{key:d.id},[(m(!0),p(j,null,q(d.permissions,w=>(m(),p("div",{key:w},Z(w.name),1))),128))]))),128)):k("",!0),e("div",lt,[e("div",rt,[e("div",it,[e("div",dt,[t(z).check("permissions_categories.read")?(m(!0),p(j,{key:0},q(y.value,(d,w)=>(m(),p("div",{class:"bg-white border border-zinc-200/70 rounded-md p-5",key:d.id},[e("div",mt,[e("h2",ct,[e("span",ut,Z(w+1),1),U(Z(d.name),1)]),e("div",null,[t(z).check("permissions_categories.delete")?(m(),p("button",{key:0,class:"mr-2",onClick:P=>a(d.id)},[s(t(D),{class:"mx-auto h-9 w-9 p-2 text-red-600 bg-red-200/70 hover:bg-red-200 rounded-lg","aria-hidden":"true"})],8,pt)):k("",!0),t(z).check("permissions_categories.edit")?(m(),p("button",{key:1,onClick:P=>u.$refs.editCategoryRef.openModal(d)},[s(t(H),{class:"mx-auto h-9 w-9 p-2 bg-main-color-50 text-main-color-600 hover:bg-main-color-100 rounded-lg","aria-hidden":"true"})],8,gt)):k("",!0)])]),t(z).check("permissions.read")?(m(!0),p(j,{key:0},q(d.permissions,P=>(m(),p("div",{class:"mt-2 space-y-2",key:P.id},[e("div",vt,[e("span",ht,Z(P.human_name),1),e("div",bt,[t(z).check("permissions.delete")?(m(),p("button",{key:0,onClick:E(W=>l(P.id),["prevent"])},[s(t(D),{class:"h-9 w-9 text-red-600 hover:bg-red-100/50 p-2 rounded-md","aria-hidden":"true"})],8,_t)):k("",!0),t(z).check("permissions.edit")?(m(),p("button",{key:1,onClick:E(W=>u.$refs.editPermissionRef.openModal(P),["prevent"])},[s(t(H),{class:"h-9 w-9 text-main-color-600 hover:bg-main-color-100/50 p-2 rounded-md","aria-hidden":"true"})],8,ft)):k("",!0)])])]))),128)):(m(),p("div",xt,kt))]))),128)):(m(),p("div",wt,Ct))])])])]),s(ge,{ref_key:"createCategoryRef",ref:g,onReloadPermissions:r[2]||(r[2]=d=>(n(),v()))},null,512),s(Ue,{ref_key:"createPermissionRef",ref:b,onReloadPermissions:r[3]||(r[3]=d=>(n(),v())),categoriesProp:c.value},null,8,["categoriesProp"]),s(De,{ref_key:"editCategoryRef",ref:C,onReloadPermissions:r[4]||(r[4]=d=>(n(),v()))},null,512),s(nt,{ref_key:"editPermissionRef",ref:h,onReloadPermissions:r[5]||(r[5]=d=>(n(),v())),categoriesProp:c.value},null,8,["categoriesProp"])],64))}};export{Ft as default};

import{a as m,o as d,c as R,w as a,e as s,h as $,d as e,u as t,x as F,s as C,E as M,b as f,f as S,n as q,t as P,v as k,F as z,k as V,q as U,C as N,B as L}from"./index-f770c8ab.js";import{_ as O}from"./AppTopbar-bf151018.js";import{_ as D}from"./pagination-af3b0cce.js";import{L as H,p as K,m as T}from"./index-3a3ac444.js";import{c as j}from"./checkPermission.service-5e6704d7.js";import{X as Z,e as X,C as A}from"./index-0d59a700.js";import{_ as B}from"./basicModal-bc7db752.js";import{N as G,H as J,$ as Q,K as W,U as Y,_ as ee}from"./combobox-441bbe15.js";import{S as I}from"./transition-a3ece599.js";import"./listbox-331f328c.js";import"./hidden-ab0c4efa.js";import"./use-tracked-pointer-f932b80e.js";import"./use-resolve-button-type-23b48367.js";import"./use-controllable-eb3312b6.js";import"./dialog-e364ba6d.js";import"./use-tree-walker-c3f4727c.js";const oe={class:"p-6"},te={class:""},se={class:"mb-5"},le=e("label",{for:"name",class:"block leading-6 text-gray-900"},"Název role:",-1),ae={class:"mt-2"},re={class:"mb-5"},ne=e("label",{for:"name",class:"block leading-6 text-gray-900"},"Priorita role:",-1),ie={class:"mt-2"},de={class:"relative mt-2"},ce=e("span",{class:"text-main-color-600"},"Zavřít",-1),ue=[ce],me=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ",-1),pe={__name:"createRoleModal",props:{rolesProp:Object},emits:["reloadRoles"],setup(_,{expose:x,emit:y}){const l=m({}),p=m(!1);function g(){p.value=!1}function h(){p.value=!0,l.value={}}function b(){V.post("/api/roles",{name:l.value.name,role_priority:l.value.role_priority,copy:l.value.copy_role.id}).then(u=>{U.success(u.data.message),y("reloadRoles",!0),this.closeModal()}).catch(u=>{U.error(u.response.data.message)})}return x({openModal:h}),(u,n)=>(d(),R(t(I),{appear:"",show:p.value,as:"template",onClose:g},{default:a(()=>[s(B,null,{"modal-title":a(()=>[$("Vytvoření nové role")]),"modal-close-button":a(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:g},[s(t(Z),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":a(()=>[s(t(F),{onSubmit:n[3]||(n[3]=c=>b())},{default:a(({values:c})=>[e("div",oe,[e("div",te,[e("div",se,[le,e("div",ae,[s(t(C),{rules:"required",modelValue:l.value.name,"onUpdate:modelValue":n[0]||(n[0]=r=>l.value.name=r),type:"text",name:"name",id:"name",autocomplete:"given-name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název role..."},null,8,["modelValue"]),s(t(M),{name:"name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",re,[ne,e("div",ie,[s(t(C),{rules:"required|minMax:1",modelValue:l.value.role_priority,"onUpdate:modelValue":n[1]||(n[1]=r=>l.value.role_priority=r),type:"number",name:"priority",id:"priority",autocomplete:"given-priority",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zvolte číselnou prioritu role..."},null,8,["modelValue"]),s(t(M),{name:"priority",class:"text-rose-400 text-sm block pt-1"})])]),s(t(C),{rules:"required",name:"copyrole"},{default:a(({handleChange:r,value:w})=>[s(t(G),{as:"div",modelValue:l.value.copy_role,"onUpdate:modelValue":[n[2]||(n[2]=o=>l.value.copy_role=o),r],class:"relative"},{default:a(()=>[s(t(J),{class:"block leading-6 text-gray-900"},{default:a(()=>[$("Převzít nastavení z existující role:")]),_:1}),e("div",de,[s(t(Q),{class:"placeholder-gray-400 w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",onChange:o=>u.query=o.target.value,"display-value":o=>o==null?void 0:o.name,placeholder:"Zvolte existující roli..."},null,8,["onChange","display-value"]),s(t(W),{class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"},{default:a(()=>[s(t(X),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1}),_.rolesProp&&_.rolesProp.length&&_.rolesProp.length>0?(d(),R(t(Y),{key:0,class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:a(()=>[(d(!0),f(z,null,S(_.rolesProp,o=>(d(),R(t(ee),{key:o.id,value:o,as:"template"},{default:a(({active:i,selected:v})=>[e("li",{class:q(["relative cursor-default select-none py-2 pl-3 pr-9",i?"bg-main-color-600 text-white":"text-gray-900"])},[e("span",{class:q(["block truncate",v&&"font-semibold"])},P(o.name),3),v?(d(),f("span",{key:0,class:q(["absolute inset-y-0 right-0 flex items-center pr-4",i?"text-white":"text-main-color-600"])},[s(t(A),{class:"h-5 w-5","aria-hidden":"true"})],2)):k("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):k("",!0)])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),s(t(M),{name:"copyrole",class:"text-rose-400 text-sm block pt-1"})])]),e("div",{class:"border-t p-5"},[e("div",{class:"text-right"},[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:g},ue),me])])]),_:1})]),_:1})]),_:1},8,["show"]))}},ge={class:"sm:col-span-3 flex items-center border-b p-6"},ve=e("label",{for:"role-name",class:"mr-8 leading-6 text-gray-900"},"Název role:",-1),fe={class:"grow"},he={class:"mb-5 p-6 flex items-center border-b"},_e=e("label",{for:"name",class:"mr-8 block leading-6 text-gray-900 w-24"},"Priorita role:",-1),be={class:"grow"},xe={class:"p-6"},ye={class:"flex items-center"},we=e("span",{class:"text-lg pl-4 font-light"},"Nastavení oprávnění role",-1),ke={class:"mt-6 mb-10 space-y-10"},Re={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10"},$e={class:"text-sm font-semibold leading-6 text-main-color-600"},Ce={class:"relative flex gap-x-3"},Ve={class:"flex h-6 items-center"},Me={class:"text-sm leading-6"},Pe=["for"],ze=e("span",{class:"text-main-color-600"},"Zavřít",-1),Ue=[ze],je=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ",-1),Ne={__name:"editRoleModal",props:{permissionsCategoriesProp:Object},emits:["reloadRoles"],setup(_,{expose:x,emit:y}){const l=m({}),p=m([]),g=m(!1);function h(){g.value=!1}function b(c){g.value=!0,u(c)}function u(c){V.get("/api/roles/"+c.id).then(r=>{l.value=c,p.value=r.data.data.permission_names}).catch(r=>{console.log(r)})}function n(){V.post("/api/roles/"+l.value.id+"/update",{name:l.value.name,permissions:p.value,role_priority:l.value.role_priority}).then(c=>{U.success(c.data.message),y("reloadRoles",!0),this.closeModal()}).catch(c=>{U.error(c.response.data.message)})}return x({openModal:b}),(c,r)=>(d(),R(t(I),{appear:"",show:g.value,as:"template",onClose:h},{default:a(()=>[s(B,{size:"lg"},{"modal-title":a(()=>[$("Úprava role")]),"modal-close-button":a(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:h},[s(t(Z),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":a(()=>[s(t(F),{onSubmit:r[3]||(r[3]=w=>n())},{default:a(({values:w})=>[e("div",ge,[ve,e("div",fe,[s(t(C),{rules:"required",modelValue:l.value.name,"onUpdate:modelValue":r[0]||(r[0]=o=>l.value.name=o),type:"text",name:"role-name",id:"role-name",autocomplete:"given-name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název role..."},null,8,["modelValue"]),s(t(M),{name:"role-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",he,[_e,e("div",be,[s(t(C),{rules:"required|minMax:1",modelValue:l.value.role_priority,"onUpdate:modelValue":r[1]||(r[1]=o=>l.value.role_priority=o),type:"number",name:"role_priority",id:"role_priority",autocomplete:"given-priority",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zvolte číselnou prioritu role..."},null,8,["modelValue"]),s(t(M),{name:"role_priority",class:"text-rose-400 text-sm block pt-1"})])]),e("div",xe,[e("div",ye,[s(t(H),{class:"h-7 w-7 text-gray-900","aria-hidden":"true"}),we]),e("div",ke,[e("fieldset",Re,[(d(!0),f(z,null,S(_.permissionsCategoriesProp,o=>(d(),f("div",{key:o.id},[e("legend",$e,P(o.name),1),(d(!0),f(z,null,S(o.permissions,i=>(d(),f("div",{class:"mt-6 space-y-6",key:i.id},[e("div",Ce,[e("div",Ve,[s(t(C),{id:i.id,name:"rolesPermissions",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",modelValue:p.value,"onUpdate:modelValue":r[2]||(r[2]=v=>p.value=v),value:i.name},null,8,["id","modelValue","value"])]),e("div",Me,[e("label",{for:i.id,class:"font-medium text-gray-900 cursor-pointer"},P(i.human_name),9,Pe)])])]))),128))]))),128))])])]),e("div",{class:"border-t p-5"},[e("div",{class:"text-right"},[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:h},Ue),je])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Se={class:"p-6"},qe={class:"py-6"},Ze={class:"border-t p-5"},Be={class:"text-right"},Ie={__name:"deleteRoleModal",emits:["reloadRoles"],setup(_,{expose:x,emit:y}){const l=m({}),p=m(!1);function g(){p.value=!1}function h(u){p.value=!0,l.value=u}async function b(u){await V.post("/api/roles/"+u+"/delete").then(n=>{U.success(n.data.message),y("reloadRoles",!0),g()}).catch(n=>{console.log(n)})}return x({openModal:h}),(u,n)=>(d(),R(t(I),{appear:"",show:p.value,as:"template",onClose:g},{default:a(()=>[s(B,null,{"modal-title":a(()=>[$("Smazat roli ")]),"modal-close-button":a(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:g},[s(t(Z),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":a(()=>[e("div",Se,[e("div",qe,[e("p",null,[$("Opravdu si přejete roli "),e("strong",null,P(l.value.name),1),$(" smazat?")])])]),e("div",Ze,[e("div",Be,[e("button",{class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",onClick:n[0]||(n[0]=N(c=>b(l.value.id),["prevent"]))}," Smazat roli ")])])]),_:1})]),_:1},8,["show"]))}},Fe={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"},Ee=["onClick"],Le=["onClick"],Oe={key:1,class:"text-gray-900 text-sm text-center col-span-3"},De=e("span",null,"Na zobrazení rolí nemáte dostatečná práva.",-1),He=[De],no={__name:"Roles",setup(_){const x=m(null),y=m(null),l=m(null),p=m({}),g=m({}),h=m(null),b=m(!1),u=m(1),n=m(["roles"]);L(()=>{b.value=!0,c(),w(),b.value=!1});async function c(){await V.get("/api/roles?page="+u.value).then(o=>{g.value=o.data.data,h.value=o.data.meta}).catch(o=>{console.log(o)})}function r(o){u.value=o,c()}function w(){V.get("/api/permission-categories/permissions").then(o=>{p.value=o.data}).catch(o=>{console.log(o)})}return(o,i)=>(d(),f(z,null,[s(O,{breadCrumbs:n.value},{topbarButtons:a(()=>[t(j).check("roles.create")?(d(),f("button",{key:0,class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:i[0]||(i[0]=N(v=>o.$refs.createRoleRef.openModal(),["prevent"]))},"Přidat Roli")):k("",!0)]),_:1},8,["breadCrumbs"]),e("div",null,[e("div",Fe,[t(j).check("roles.read")?(d(!0),f(z,{key:0},S(g.value,v=>(d(),f("div",{key:v.id,class:"bg-white border border-zinc-200/70 rounded-md p-5 flex justify-between items-center"},[e("div",null,P(v.name),1),e("div",null,[t(j).check("roles.delete")?(d(),f("button",{key:0,class:"mr-2",onClick:N(E=>o.$refs.deleteRoleRef.openModal(v),["prevent"])},[s(t(K),{class:"mx-auto h-9 w-9 p-2 text-red-600 bg-red-200/70 hover:bg-red-200 rounded-lg","aria-hidden":"true"})],8,Ee)):k("",!0),t(j).check("roles.edit")?(d(),f("button",{key:1,onClick:N(E=>o.$refs.editRoleRef.openModal(v),["prevent"])},[s(t(T),{class:"mx-auto h-9 w-9 p-2 bg-main-color-50 text-main-color-600 hover:bg-main-color-100 rounded-lg","aria-hidden":"true"})],8,Le)):k("",!0)])]))),128)):(d(),f("div",Oe,He))]),h.value!==null?(d(),R(D,{key:0,meta:h.value,onSetPage:r,modelValue:u.value,"onUpdate:modelValue":i[1]||(i[1]=v=>u.value=v)},null,8,["meta","modelValue"])):k("",!0)]),s(pe,{ref_key:"createRoleRef",ref:x,onReloadRoles:i[2]||(i[2]=v=>(c(),w())),rolesProp:g.value},null,8,["rolesProp"]),s(Ne,{ref_key:"editRoleRef",ref:y,onReloadRoles:i[3]||(i[3]=v=>(c(),w())),permissionsCategoriesProp:p.value},null,8,["permissionsCategoriesProp"]),s(Ie,{ref_key:"deleteRoleRef",ref:l,onReloadRoles:i[4]||(i[4]=v=>(c(),w()))},null,512)],64))}};export{no as default};

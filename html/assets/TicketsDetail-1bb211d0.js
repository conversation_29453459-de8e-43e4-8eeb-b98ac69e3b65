import{m as P,y as B,a as m,B as Z,k,r as F,o as r,b as i,e as d,w as u,d as e,t as o,h as v,u as s,v as c,F as y,f as N,q as C,P as w,s as E,E as I,x as q,n as z,C as M}from"./index-f770c8ab.js";import{_ as H}from"./AppTopbar-bf151018.js";import{P as U,X}from"./index-0d59a700.js";import{x as A}from"./index-3a3ac444.js";import{_ as K}from"./basicModal-bc7db752.js";import{c as x}from"./checkPermission.service-5e6704d7.js";import{S as L}from"./transition-a3ece599.js";import"./dialog-e364ba6d.js";import"./hidden-ab0c4efa.js";const O={key:0},R=e("span",null,"<PERSON><PERSON>ř<PERSON><PERSON><PERSON> požadavek",-1),G={key:0},J={class:"flex justify-between items-center py-8"},Q={class:"text-2xl text-gray-900"},W={class:"flex justify-between items-center gap-16"},ee={class:"text-sm text-gray-900"},te={class:"font-semibold"},se={class:"text-sm text-gray-900"},ae={class:"font-semibold"},oe=e("span",{class:"pr-3"},"Stav:",-1),le={key:0,class:"bg-red-200/75 text-red-700 rounded-lg px-3 py-1 font-semibold text-xs"},ne={key:1,class:"bg-amber-200/75 text-amber-700 rounded-lg px-3 py-1 font-semibold text-xs"},re={key:2,class:"space-x-4 space-y-4"},ie={class:"bg-green-200/75 text-green-700 rounded-lg px-3 py-1 font-semibold text-xd"},de={class:"text-green-700"},ce={class:"relative bg-white border border-zinc-200/70 rounded-md px-5 py-8"},ue=e("span",{class:"bg-main-color-100 text-main-color-700 rounded-lg px-3 py-1 font-semibold text-xs absolute -top-3"},"Popis požadavku",-1),me={class:"text-sm text-gray-900"},pe=e("h2",{class:"text-lg pl-2 py-6 text-gray-900"},"Komunikace k požadavku:",-1),_e={role:"list",class:"space-y-6"},ve={key:0,class:"relative flex gap-x-4"},xe={key:0,class:"-bottom-6 absolute left-0 top-2 flex w-6 justify-center"},he=e("div",{class:"w-[2px] bg-gray-400"},null,-1),ge=[he],fe={class:"flex w-full flex-wrap"},be=e("div",{class:"relative flex h-6 w-6 flex-none items-center justify-center bg-transparent"},[e("div",{class:"h-2.5 w-2.5 rounded-full bg-gray-400 ring-1 ring-gray-400"})],-1),ke={class:"text-sm pl-2 w-48 pb-4"},we={class:"font-semibold"},ye={class:"block w-full"},Ce={class:"pt-4 sm:pt-3 sm:pl-4"},Me={type:"submit",class:"rounded-md bg-green-500 pl-2 pr-4 py-2 text-sm mt-0 text-white shadow-sm hover:bg-green-600 flex items-center gap-2"},Ye=e("span",{class:"whitespace-nowrap"},"Přidat odpověď",-1),Ve=e("div",{class:"w-[2px] bg-gray-400"},null,-1),je=[Ve],De={class:"flex items-center w-full"},Se=e("div",{class:"relative flex h-6 w-6 flex-none items-center justify-center bg-transparent"},[e("div",{class:"h-2.5 w-2.5 rounded-full bg-gray-400 ring-1 ring-gray-400"})],-1),Te={class:"text-sm pl-2 w-48"},$e={class:"font-semibold"},Pe={class:"block"},Be={class:"bg-white rounded-lg p-5 grow"},Ze={class:"text-sm leading-6 text-gray-900"},Fe=e("div",{class:"p-6"},[e("span",null,"Opravdu chcete požadavek vyřešit?")],-1),Ne={class:"border-t p-5"},Ee={class:"text-right space-x-3"},Re={__name:"TicketsDetail",setup(Ie){const Y=P(),V=B(),j=m(["tickets-parent","ticket-detail"]),h=m(!1),t=m(),p=m("");Z(()=>{h.value=!0,g()});function g(){k.get("/api/tickets/"+V.params.id).then(l=>{t.value=l.data.data,t.value.answers&&t.value.answers.reverse(),h.value=!1}).catch(l=>{console.log(l)})}function D(){k.post("/api/tickets/"+t.value.id+"/answers",{text:p.value}).then(l=>{C.success(l.data.message),p.value="",g()}).catch(l=>{console.log(l)})}const f=m(!1);function _(){f.value=!1}function S(){f.value=!0}function T(){k.post("/api/tickets/"+t.value.id+"/close").then(l=>{C.success(l.data.message),g(),_()}).catch(l=>{console.log(l)})}return(l,a)=>{const $=F("router-link");return r(),i(y,null,[d(H,{breadCrumbs:j.value},{topbarButtons:u(()=>[d($,{to:{name:"tickets-list"},class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200 flex items-center"},{default:u(()=>[v(" Zavřít ")]),_:1}),t.value?(r(),i("div",O,[t.value.state.id!=4&&(s(x).check("tickets.close")||s(x).check("tickets.master"))?(r(),i("button",{key:0,onClick:a[0]||(a[0]=n=>S()),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600 flex items-center gap-2"},[d(s(A),{class:"h-6 w-6 text-white","aria-hidden":"true"}),R])):c("",!0)])):c("",!0)]),_:1},8,["breadCrumbs"]),!h.value&&t.value?(r(),i("div",G,[e("div",J,[e("h1",Q,o(t.value.subject),1),e("div",W,[e("span",ee,[v("Založil: "),e("span",te,o(t.value.user.full_name),1)]),e("span",se,[v("Založeno: "),e("span",ae,o(s(w)(t.value.created_at).format("DD.MM.YYYY")),1)]),e("span",null,[oe,t.value.state.id==1?(r(),i("span",le,o(t.value.state.name),1)):c("",!0),t.value.state.id==2||t.value.state.id==3?(r(),i("span",ne,o(t.value.state.name),1)):c("",!0),t.value.state.id==4?(r(),i("span",re,[e("span",ie,o(t.value.state.name),1),e("span",de,o(s(w)(t.value.updated_at).format("DD.MM.YYYY"))+" - "+o(t.value.user.full_name),1)])):c("",!0)])])]),e("div",ce,[ue,e("p",me,o(t.value.text),1)]),e("div",null,[pe,e("ul",_e,[t.value.state.id!=4&&(s(x).check("tickets.edit")||s(x).check("tickets.master"))?(r(),i("li",ve,[t.value.answers&&t.value.answers.length?(r(),i("div",xe,ge)):c("",!0),e("div",fe,[be,e("div",ke,[e("span",we,o(s(Y).user.full_name)+":",1)]),d(s(q),{onSubmit:a[2]||(a[2]=n=>D()),class:"rounded-lg grow sm:flex w-full sm:w-auto pl-8 sm:pl-0"},{default:u(({values:n})=>[e("div",ye,[d(s(E),{as:"textarea",rules:"required",modelValue:p.value,"onUpdate:modelValue":a[1]||(a[1]=b=>p.value=b),rows:"2",name:"answer_text",id:"answer_text",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte text odpovědi k požadavku..."},null,8,["modelValue"]),d(s(I),{name:"answer_text",class:"text-rose-400 text-sm block pt-1"})]),e("div",Ce,[e("button",Me,[d(s(U),{class:"h-6 w-6 text-white","aria-hidden":"true"}),Ye])])]),_:1})])])):c("",!0),(r(!0),i(y,null,N(t.value.answers,(n,b)=>(r(),i("li",{key:n.id,class:"relative flex gap-x-4"},[e("div",{class:z([b===t.value.answers.length-1?"h-16":"-bottom-6","absolute left-0 top-0 flex w-6 justify-center"])},je,2),e("div",De,[Se,e("div",Te,[e("span",$e,o(n.user.full_name)+":",1),e("span",Pe,o(s(w)(n.created_at).format("DD.MM.YYYY HH:MM")),1)]),e("div",Be,[e("p",Ze,o(n.text),1)])])]))),128))])])])):c("",!0),d(s(L),{appear:"",show:f.value,as:"template",onClose:a[6]||(a[6]=n=>_())},{default:u(()=>[d(K,null,{"modal-title":u(()=>[v("Vyřešit požadavek")]),"modal-close-button":u(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:a[3]||(a[3]=n=>_())},[d(s(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":u(()=>[Fe,e("div",Ne,[e("div",Ee,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:a[4]||(a[4]=M(n=>_(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:a[5]||(a[5]=M(n=>T(),["prevent"]))}," Vyřešit ")])])]),_:1})]),_:1},8,["show"])],64)}}};export{Re as default};

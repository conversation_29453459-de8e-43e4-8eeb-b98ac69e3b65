import{a as p,r as T,o as d,c as L,w as c,e as o,h as $,d as e,u as l,y as Z,t as C,b as r,F as O,f as B,E as F,v as u,G as P,k as f,s as b,x,j as ie,C as de,T as re,n as J}from"./index-e505f2bc.js";import{_ as ue}from"./AppTopbar-3ffa7ee1.js";import{i as me,a as ce,N as pe,j as ge,k as ve,l as xe,m as be,n as fe,H as _e,o as ye,p as he,h as ke,q as we,r as Ve,g as je,s as Se}from"./index-079bbc30.js";import{c as V}from"./checkPermission.service-1648a8ba.js";import{X as q,d as Ue,C as $e}from"./index-84528390.js";import{s as H}from"./default.css_vue_type_style_index_1_src_true_lang-c0f08eb9.js";import{_ as W}from"./basicModal-3b1f6411.js";import{S as Q}from"./transition-feb753d8.js";import{E as Me,A as Ie,B as Ze,F as Ce}from"./listbox-6a8c52e6.js";import"./dialog-a220f9aa.js";import"./hidden-a70e7379.js";import"./use-tracked-pointer-22b1e651.js";import"./use-resolve-button-type-b3f8121e.js";import"./use-controllable-a2a436f1.js";const ze={class:"p-6 web-filter-modal-data"},Ne={class:"text-xl"},Pe={key:0,class:"space-y-5 border-t pt-5 mt-5 pr-5 max-h-96 overflow-y-scroll"},Ee={class:"space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0"},Re={class:"flex items-center"},Ge=["for"],Fe={class:"flex items-center"},Oe=["for"],Le={class:"flex items-center"},Be=["for"],He={class:"flex items-center"},Te=["for"],Ae={key:1,class:"h-40 flex items-center"},De={class:"border-t p-5"},Je={class:"text-right space-x-3"},qe={__name:"webFiltersModal",emits:["reloadWebFilter"],setup(A,{expose:E,emit:z}){const N=z,j=p(!0),t=p(""),v=p({}),S=p(!1);function _(){S.value=!1}function y(g){j.value=!0,t.value=g,f.get("/api/fortinet/webfilters/"+g).then(n=>{v.value=n.data.data.filters,j.value=!1}).catch(n=>{console.log(n)}),S.value=!0}function M(){f.post("/api/fortinet/webfilters/"+t.value+"/update",{filters:v.value}).then(g=>{b.success(g.data.message),N("reloadWebFilter",!0),_()}).catch(g=>{console.log(g),b.error("Změny se nepovedlo uložit")})}return E({openModal:y}),(g,n)=>{const h=T("VueSpinner");return d(),L(l(Q),{appear:"",show:S.value,as:"template",onClose:n[3]||(n[3]=k=>_())},{default:c(()=>[o(W,{size:"lg"},{"modal-title":c(()=>n[4]||(n[4]=[$("Editace kategorie nevhodných stránek")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:n[0]||(n[0]=k=>_())},[o(l(q),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[o(l(Z),{onSubmit:n[2]||(n[2]=k=>M())},{default:c(({values:k})=>[e("div",ze,[e("div",null,[n[5]||(n[5]=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolená kategorie:",-1)),e("p",Ne,C(t.value),1)]),j.value?(d(),r("div",Ae,[o(h,{class:"mx-auto text-spinner-color",size:"40"})])):(d(),r("div",Pe,[(d(!0),r(O,null,B(v.value,m=>(d(),r("div",{key:m.id,class:"flex justify-between items-center"},[e("span",null,C(m.name),1),o(l(F),{name:"filter-id"+m.id,class:"text-rose-400 text-sm block pt-1"},null,8,["name"]),e("fieldset",null,[e("div",Ee,[e("div",Re,[o(l(u),{rules:"requiredRadio",id:"allow-id-"+m.id,name:"filter-id"+m.id,type:"radio",value:"allow",modelValue:m.action,"onUpdate:modelValue":U=>m.action=U,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"allow-id-"+m.id,class:"ml-2 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[o(l(me),{class:"mx-auto h-5 w-5 text-green-500","aria-hidden":"true"}),n[6]||(n[6]=e("span",null,"Allow",-1))],8,Ge)]),e("div",Fe,[o(l(u),{rules:"requiredRadio",id:"monitor-id-"+m.id,name:"filter-id"+m.id,type:"radio",value:"monitor",modelValue:m.action,"onUpdate:modelValue":U=>m.action=U,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"monitor-id-"+m.id,class:"ml-3 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[o(l(ce),{class:"mx-auto h-5 w-5 text-sky-500","aria-hidden":"true"}),n[7]||(n[7]=e("span",null,"Monitor",-1))],8,Oe)]),e("div",Le,[o(l(u),{rules:"requiredRadio",id:"block-id-"+m.id,name:"filter-id"+m.id,type:"radio",value:"block",modelValue:m.action,"onUpdate:modelValue":U=>m.action=U,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"block-id-"+m.id,class:"ml-3 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[o(l(pe),{class:"mx-auto h-5 w-5 text-red-500","aria-hidden":"true"}),n[8]||(n[8]=e("span",null,"Block",-1))],8,Be)]),e("div",He,[o(l(u),{rules:"requiredRadio",id:"warning-id-"+m.id,name:"filter-id"+m.id,type:"radio",value:"warning",modelValue:m.action,"onUpdate:modelValue":U=>m.action=U,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"warning-id-"+m.id,class:"ml-3 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[o(l(ge),{class:"mx-auto h-5 w-5 text-amber-500","aria-hidden":"true"}),n[9]||(n[9]=e("span",null,"Warning",-1))],8,Te)])])])]))),128))]))]),e("div",De,[e("div",Je,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:n[1]||(n[1]=P(m=>_(),["prevent"]))}," Zavřít "),n[10]||(n[10]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"])}}},We={class:"p-6"},Qe={key:0,class:"min-w-full divide-y divide-gray-200"},Xe={key:0,class:"divide-y divide-gray-200"},Ke={class:"whitespace-nowrap py-3 pl-5 pr-3 text-left text-sm"},Ye={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-600"},es=["onClick"],ss={key:1},ts={key:1,class:"h-40 flex items-center"},ls={class:"border-t p-5"},os={class:"text-right space-x-3"},as={__name:"backupsModal",setup(A,{expose:E}){const z=p(!1),N=p(!1);function j(){N.value=!1}function t(){N.value=!0,S()}const v=p({});async function S(){z.value=!0,await f.get("/api/backups/").then(g=>{v.value=g.data.data}).catch(g=>{console.log(g)}),z.value=!1}const _=p(null),y=p(null);function M(g){f.post("/api/backups/download",{name:g}).then(n=>{_.value=n.headers["file-name"],y.value=window.URL.createObjectURL(new Blob([n.data],{type:n.headers["content-type"]}));var h=_.value;h=decodeURIComponent(h),h=h.replaceAll("+"," ");var k=y.value,m=document.createElement("a");m.href=k,m.setAttribute("download",h),document.body.appendChild(m),m.click(),_.value=null,y.value=null,b.success("Soubor byl úspěšně stáhnut.")}).catch(n=>{b.error("Soubor se nepodařil stáhnout.")})}return E({openModal:t}),(g,n)=>{const h=T("VueSpinner");return d(),L(l(Q),{appear:"",show:N.value,as:"template",onClose:n[2]||(n[2]=k=>j())},{default:c(()=>[o(W,{size:"lg"},{"modal-title":c(()=>n[3]||(n[3]=[$("Zálohy Databáze")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:n[0]||(n[0]=k=>j())},[o(l(q),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[e("div",We,[z.value==!1?(d(),r("table",Qe,[n[5]||(n[5]=e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Název souboru "),e("th",{scope:"col",class:"py-4 pl-10 pr-5 text-right text-sm font-semibold text-gray-900 rounded-tr-md bg-gray-100/70"}," Akce ")])],-1)),v.value&&v.value.length?(d(),r("tbody",Xe,[(d(!0),r(O,null,B(v.value,k=>(d(),r("tr",{key:k},[e("td",Ke,C(k.name),1),e("td",Ye,[l(V).check("backup.download")?(d(),r("button",{key:0,type:"button",onClick:P(m=>M(k.name),["prevent"]),class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100 text-right ml-auto"},[o(l(ve),{class:"mx-auto h-9 w-9 p-2 text-main-color-600","aria-hidden":"true"})],8,es)):x("",!0)])]))),128))])):(d(),r("tbody",ss,n[4]||(n[4]=[e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné zálohy...")],-1)])))])):(d(),r("div",ts,[o(h,{class:"mx-auto text-spinner-color",size:"40"})]))]),e("div",ls,[e("div",os,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:n[1]||(n[1]=P(k=>j(),["prevent"]))}," Zavřít ")])])]),_:1})]),_:1},8,["show"])}}};const ns={class:"space-y-12"},is={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ds={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},rs={key:0,class:"p-5 pb-8"},us={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3"},ms={class:"flex items-center"},cs={key:1,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},ps={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},gs={class:"sm:col-span-3"},vs={class:"mt-2"},xs={class:"sm:col-span-3"},bs={class:"mt-2"},fs={class:"sm:col-span-3"},_s={class:"mt-2"},ys={class:"sm:col-span-3"},hs={class:"mt-2"},ks={class:"sm:col-span-3"},ws={class:"mt-2"},Vs={key:1,class:"h-40 flex items-center"},js={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},Ss={key:0,class:"p-5 pb-8"},Us={class:"flex justify-between items-center"},$s={class:"flex items-center"},Ms={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},Is={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},Zs={class:"sm:col-span-3"},Cs={class:"mt-2"},zs={class:"sm:col-span-3"},Ns={class:"mt-2"},Ps={class:"sm:col-span-3"},Es={class:"mt-2"},Rs={class:"sm:col-span-3"},Gs={class:"mt-2"},Fs={class:"sm:col-span-3"},Os={class:"mt-2"},Ls={key:1,class:"h-40 flex items-center"},Bs={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},Hs={key:0,class:"p-5 pb-8"},Ts={class:"flex justify-between items-center"},As={class:"flex items-center"},Ds={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},Js={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},qs={class:"sm:col-span-3"},Ws={class:"mt-2"},Qs={class:"sm:col-span-3"},Xs={class:"mt-2"},Ks={class:"sm:col-span-3"},Ys={class:"mt-2"},et={class:"sm:col-span-3"},st={class:"mt-2"},tt={class:"sm:col-span-3"},lt={class:"flex gap-x-6 mt-4"},ot={class:"flex gap-x-3"},at={class:"flex h-6 items-center"},nt={key:1,class:"h-40 flex items-center"},it={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},dt={key:0},rt={class:"p-5"},ut={class:"flex justify-between items-center"},mt={class:"flex items-center"},ct={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},pt={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},gt={class:"sm:col-span-3"},vt={class:"mt-2"},xt={class:"sm:col-span-3"},bt={class:"mt-2"},ft={class:"border-t mt-1 pt-6 px-5 pb-8"},_t={class:"space-y-3"},yt=["onClick"],ht={key:1,class:"h-40 flex items-center"},kt={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},wt={key:0,class:"p-5 pb-8"},Vt={class:"flex justify-between items-center"},jt={class:"flex items-center"},St={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},Ut={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6 border-b pb-8 mb-6"},$t={class:"sm:col-span-3"},Mt={class:"mt-2"},It={class:"sm:col-span-3"},Zt={class:"mt-2"},Ct={class:"sm:col-span-3"},zt={class:"mt-2"},Nt={class:"sm:col-span-3"},Pt={class:"mt-2"},Et={class:"sm:col-span-3"},Rt={class:"mt-2"},Gt={class:"sm:col-span-3"},Ft={class:"mt-2"},Ot={class:"sm:col-span-3"},Lt={class:"mt-2"},Bt={class:"sm:col-span-3"},Ht={class:"flex gap-x-6 mt-4"},Tt={class:"flex gap-x-3"},At={class:"flex h-6 items-center"},Dt={class:"flex gap-x-3"},Jt={class:"flex h-6 items-center"},qt={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},Wt={class:"sm:col-span-6"},Qt={class:"mt-2 mb-6"},Xt={key:1,class:"h-40 flex items-center"},Kt={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},Yt={key:0,class:"p-5"},el={class:"flex justify-between items-center"},sl={class:"flex items-center"},tl={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},ll={class:"mt-6 pb-4 gap-4"},ol={class:"grow mb-2"},al={class:"text-base font-normal leading-6 text-gray-900 flex items-center gap-2"},nl={class:"border-b pt-2 pb-6 grid grid-cols-1 gap-6"},il={class:"mt-2 pb-6 gap-4"},dl={class:"grow mb-2"},rl={class:"text-base font-normal leading-6 text-gray-900 flex items-center gap-2"},ul={class:"border-b pt-2 pb-4 grid grid-cols-1 gap-6"},ml={class:"mt-2 pb-6 gap-4"},cl={class:"grow mb-2"},pl={class:"text-base font-normal leading-6 text-gray-900 flex items-center gap-2"},gl={class:"pt-2 pb-4 grid grid-cols-1 sm:grid-cols-2 gap-6"},vl={class:"relative"},xl={key:0,class:"block truncate"},bl={key:0,class:"bg-main-color-200 rounded-lg inline-block py-1 px-2 ml-2"},fl={key:1,class:"block truncate text-gray-400"},_l={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},yl={key:0,class:"bg-main-color-200 rounded-lg inline-block py-1 px-2 ml-2"},hl={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},kl={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},wl={key:0,class:"p-5"},Vl={class:"flex justify-between items-center"},jl={class:"flex items-center"},Sl={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},Ul={class:"mt-6 pb-4 flex justify-center items-end gap-4"},$l={class:"grow"},Ml={class:"mt-2"},Il={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block mb-6"},Al={__name:"Settings",setup(A){const E=p(),z=p(),N=ie("debugModeGlobalVar"),j=p(["skolasys-root","settings"]),t=p({}),v=p(!0),S=p({}),_=p({}),y=p({});p({}),p("");const M=p([]);p("");const g=p([]),n=p(""),h=p([]);de(async()=>{v.value=!0,await ae(),await ne(),await k(),await D(),m(),v.value=!1});async function k(){await f.get("/api/settings").then(i=>{t.value=i.data.data}).catch(i=>{console.log(i)})}function m(){!t.value||!y.value.length||!_.value.length||(n.value=_.value.find(i=>i.id==t.value.guest_default_organization_unit)||"",M.value=t.value.student_default_groups?t.value.student_default_groups.split(",").map(i=>y.value.find(s=>s.id==i)).filter(Boolean):[],g.value=t.value.employee_default_groups?t.value.employee_default_groups.split(",").map(i=>y.value.find(s=>s.id==i)).filter(Boolean):[],h.value=t.value.guest_default_groups?t.value.guest_default_groups.split(",").map(i=>y.value.find(s=>s.id==i)).filter(Boolean):[])}function U(){f.post("/api/settings/active-directory/check",{ldap_hosts:t.value.ldap_hosts,ldap_port:t.value.ldap_port,ldap_base_dn:t.value.ldap_base_dn,ldap_username:t.value.ldap_username,ldap_password:t.value.ldap_password,ldap_account_prefix:t.value.ldap_account_prefix,ldap_account_suffix:t.value.ldap_account_suffix,ldap_use_ssl:t.value.ldap_use_ssl,ldap_use_tls:t.value.ldap_use_tls}).then(i=>{b.success(i.data.message)}).catch(i=>{})}function X(){f.post("/api/settings/active-directory",{ldap_hosts:t.value.ldap_hosts,ldap_port:t.value.ldap_port,ldap_base_dn:t.value.ldap_base_dn,ldap_username:t.value.ldap_username,ldap_password:t.value.ldap_password,ldap_account_prefix:t.value.ldap_account_prefix,ldap_account_suffix:t.value.ldap_account_suffix,ldap_use_ssl:t.value.ldap_use_ssl,ldap_use_tls:t.value.ldap_use_tls,ldap_username_scheme:t.value.ldap_username_scheme}).then(i=>{b.success(i.data.message)}).catch(i=>{})}function K(){f.post("/api/settings/fortinet",{fortinet_ip:t.value.fortinet_ip,fortinet_token:t.value.fortinet_token}).then(i=>{b.success(i.data.message)}).catch(i=>{b.error("Změny se nepovedlo uložit")})}function Y(){f.post("/api/settings/school-smtp",{school_mail_host:t.value.school_mail_host,school_mail_port:t.value.school_mail_port,school_mail_username:t.value.school_mail_username,school_mail_password:t.value.school_mail_password,school_mail_encryption:t.value.school_mail_encryption}).then(i=>{b.success(i.data.message)}).catch(i=>{b.error("Změny se nepovedlo uložit")})}function ee(){f.post("/api/settings/gsm-gate",{gsm_gate_ip:t.value.gsm_gate_ip,gsm_gate_account:t.value.gsm_gate_account,gsm_gate_password:t.value.gsm_gate_password,gsm_gate_sim_port:t.value.gsm_gate_sim_port,gsm_send_sms:t.value.gsm_send_sms}).then(i=>{b.success(i.data.message)}).catch(i=>{b.error("Změny se nepovedlo uložit")})}function se(){f.post("/api/settings/mysql",{db_host:t.value.db_host,db_port:t.value.db_port,db_database:t.value.db_database,db_username:t.value.db_username,db_password:t.value.db_password}).then(i=>{b.success(i.data.message)}).catch(i=>{console.log(i),b.error("Změny se nepovedlo uložit")})}async function D(){await f.get("/api/fortinet/webfilters").then(i=>{S.value=i.data.data.webfilters}).catch(i=>{console.log(i)})}function te(){f.post("/api/settings/license/check",{license_key:t.value.license_key}).then(i=>{b.success(i.data.message)}).catch(i=>{})}function le(){f.post("/api/settings/license",{license_key:t.value.license_key}).then(i=>{b.success(i.data.message)}).catch(i=>{})}async function oe(){var i;await f.post("/api/settings/default-groups-ous",{student_default_groups:M.value.map(s=>s.id).join(","),employee_default_groups:g.value.map(s=>s.id).join(","),guest_default_organization_unit:((i=n.value)==null?void 0:i.id)||"",guest_default_groups:h.value.map(s=>s.id).join(",")}).then(s=>{b.success(s.data.message)}).catch(s=>{console.log(s),b.error("Chyba při ukládání dat")})}async function ae(){await f.get("/api/organization-units?perpage=9999").then(i=>{_.value=i.data.data}).catch(i=>{console.log(i)})}async function ne(){await f.get("/api/groups?perpage=9999").then(i=>{y.value=i.data.data}).catch(i=>{console.log(i)})}return(i,s)=>{const R=T("VueSpinner");return d(),r(O,null,[o(ue,{breadCrumbs:j.value},{topbarButtons:c(()=>s[43]||(s[43]=[])),_:1},8,["breadCrumbs"]),e("div",ns,[e("div",is,[e("div",null,[e("div",ds,[v.value?(d(),r("div",Vs,[o(R,{class:"mx-auto text-spinner-color",size:"40"})])):(d(),r("div",rs,[o(l(Z),{onSubmit:s[6]||(s[6]=w=>se())},{default:c(({values:w})=>[e("div",us,[e("div",ms,[o(l(xe),{class:"w-7"}),s[44]||(s[44]=e("p",{class:"ml-4 text-lg text-gray-900"},"MySQL Databáze",-1))]),e("div",null,[l(V).check("backup.read")?(d(),r("button",{key:0,onClick:s[0]||(s[0]=P(a=>i.$refs.backupsRef.openModal(),["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"}," Zálohy ")):x("",!0),l(V).check("settings.edit")?(d(),r("button",cs,"Uložit")):x("",!0)])]),e("div",ps,[e("div",gs,[s[45]||(s[45]=e("label",{for:"db-username",class:"block text-sm font-normal leading-6 text-gray-900"},"Přihlašovací jméno:",-1)),e("div",vs,[o(l(u),{modelValue:t.value.db_username,"onUpdate:modelValue":s[1]||(s[1]=a=>t.value.db_username=a),id:"db-username",name:"db-username",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno k MySQL databázi..."},null,8,["modelValue"])])]),e("div",xs,[s[46]||(s[46]=e("label",{for:"db-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1)),e("div",bs,[o(l(u),{modelValue:t.value.db_password,"onUpdate:modelValue":s[2]||(s[2]=a=>t.value.db_password=a),id:"db-password",name:"db-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte heslo k MySQL databázi..."},null,8,["modelValue"])])]),e("div",fs,[s[47]||(s[47]=e("label",{for:"db-database",class:"block text-sm font-normal leading-6 text-gray-900"},"Název databáze:",-1)),e("div",_s,[o(l(u),{modelValue:t.value.db_database,"onUpdate:modelValue":s[3]||(s[3]=a=>t.value.db_database=a),id:"db-database",name:"db-database",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název databáze..."},null,8,["modelValue"])])]),e("div",ys,[s[48]||(s[48]=e("label",{for:"db-host",class:"block text-sm font-normal leading-6 text-gray-900"},"Host:",-1)),e("div",hs,[o(l(u),{modelValue:t.value.db_host,"onUpdate:modelValue":s[4]||(s[4]=a=>t.value.db_host=a),id:"db-host",name:"db-host",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte host MySQL databáze..."},null,8,["modelValue"])])]),e("div",ks,[s[49]||(s[49]=e("label",{for:"db-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Port:",-1)),e("div",ws,[o(l(u),{modelValue:t.value.db_port,"onUpdate:modelValue":s[5]||(s[5]=a=>t.value.db_port=a),id:"db-port",name:"db-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port MySQL databáze..."},null,8,["modelValue"])])])])]),_:1})]))]),e("div",js,[o(l(Z),{onSubmit:s[12]||(s[12]=w=>Y())},{default:c(({values:w})=>[v.value?(d(),r("div",Ls,[o(R,{class:"mx-auto text-spinner-color",size:"40"})])):(d(),r("div",Ss,[e("div",Us,[e("div",$s,[o(l(be),{class:"w-7"}),s[50]||(s[50]=e("p",{class:"ml-4 text-lg text-gray-900"},"SMTP",-1))]),e("div",null,[l(V).check("settings.edit")?(d(),r("button",Ms,"Uložit")):x("",!0)])]),e("div",Is,[e("div",Zs,[s[51]||(s[51]=e("label",{for:"school-mail-host",class:"block text-sm font-normal leading-6 text-gray-900"},"Host:",-1)),e("div",Cs,[o(l(u),{modelValue:t.value.school_mail_host,"onUpdate:modelValue":s[7]||(s[7]=a=>t.value.school_mail_host=a),id:"school-mail-host",name:"school-mail-host",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte host k SMTP serveru..."},null,8,["modelValue"])])]),e("div",zs,[s[52]||(s[52]=e("label",{for:"school-mail-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Port:",-1)),e("div",Ns,[o(l(u),{modelValue:t.value.school_mail_port,"onUpdate:modelValue":s[8]||(s[8]=a=>t.value.school_mail_port=a),id:"school-mail-port",name:"school-mail-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port k SMTP serveru..."},null,8,["modelValue"])])]),e("div",Ps,[s[53]||(s[53]=e("label",{for:"school-mail-username",class:"block text-sm font-normal leading-6 text-gray-900"},"Přihlašovací jméno:",-1)),e("div",Es,[o(l(u),{modelValue:t.value.school_mail_username,"onUpdate:modelValue":s[9]||(s[9]=a=>t.value.school_mail_username=a),id:"school-mail-username",name:"school-mail-username",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno..."},null,8,["modelValue"])])]),e("div",Rs,[s[54]||(s[54]=e("label",{for:"school-mail-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1)),e("div",Gs,[o(l(u),{modelValue:t.value.school_mail_password,"onUpdate:modelValue":s[10]||(s[10]=a=>t.value.school_mail_password=a),id:"school-mail-password",name:"school-mail-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte heslo..."},null,8,["modelValue"])])]),e("div",Fs,[s[55]||(s[55]=e("label",{for:"school-mail-encryption",class:"block text-sm font-normal leading-6 text-gray-900"},"Šifrování:",-1)),e("div",Os,[o(l(u),{modelValue:t.value.school_mail_encryption,"onUpdate:modelValue":s[11]||(s[11]=a=>t.value.school_mail_encryption=a),id:"school-mail-encryption",name:"school-mail-encryption",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte šifrování (tls)..."},null,8,["modelValue"])])])])]))]),_:1})]),e("div",Bs,[o(l(Z),{onSubmit:s[18]||(s[18]=w=>ee())},{default:c(({values:w})=>[v.value?(d(),r("div",nt,[o(R,{class:"mx-auto text-spinner-color",size:"40"})])):(d(),r("div",Hs,[e("div",Ts,[e("div",As,[o(l(fe),{class:"w-7"}),s[56]||(s[56]=e("p",{class:"ml-4 text-lg text-gray-900"},"Gsm brána",-1))]),e("div",null,[l(V).check("settings.edit")?(d(),r("button",Ds,"Uložit")):x("",!0)])]),e("div",Js,[e("div",qs,[s[57]||(s[57]=e("label",{for:"gsm-gate-ip",class:"block text-sm font-normal leading-6 text-gray-900"},"Ip adresa:",-1)),e("div",Ws,[o(l(u),{modelValue:t.value.gsm_gate_ip,"onUpdate:modelValue":s[13]||(s[13]=a=>t.value.gsm_gate_ip=a),id:"dgsm-gate-ip",name:"gsm-gate-ip",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte IP adresu GSM brány..."},null,8,["modelValue"])])]),e("div",Qs,[s[58]||(s[58]=e("label",{for:"gsm-gate-sim-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Sim port:",-1)),e("div",Xs,[o(l(u),{modelValue:t.value.gsm_gate_sim_port,"onUpdate:modelValue":s[14]||(s[14]=a=>t.value.gsm_gate_sim_port=a),id:"gsm-gate-sim-port",name:"gsm-gate-sim-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port SIM karty, který se má používat..."},null,8,["modelValue"])])]),e("div",Ks,[s[59]||(s[59]=e("label",{for:"gsm-gate-account",class:"block text-sm font-normal leading-6 text-gray-900"},"Uživatelské jméno:",-1)),e("div",Ys,[o(l(u),{modelValue:t.value.gsm_gate_account,"onUpdate:modelValue":s[15]||(s[15]=a=>t.value.gsm_gate_account=a),id:"gsm-gate-account",name:"gsm-gate-account",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte uživatelské jméno k GSM bráně..."},null,8,["modelValue"])])]),e("div",et,[s[60]||(s[60]=e("label",{for:"gsm-gate-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1)),e("div",st,[o(l(u),{modelValue:t.value.gsm_gate_password,"onUpdate:modelValue":s[16]||(s[16]=a=>t.value.gsm_gate_password=a),id:"gsm-gate-password",name:"gsm-gate-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte heslo k GSM bráně...."},null,8,["modelValue"])])]),e("div",tt,[s[62]||(s[62]=e("label",{for:"",class:"block text-sm font-normal leading-6 text-gray-900"},"Ostatní:",-1)),e("div",lt,[e("div",ot,[e("div",at,[o(l(u),{modelValue:t.value.gsm_send_sms,"onUpdate:modelValue":s[17]||(s[17]=a=>t.value.gsm_send_sms=a),value:!t.value.gsm_send_sms,id:"gsm-send-sms",name:"gsm-send-sms",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"])]),s[61]||(s[61]=e("div",{class:"text-sm leading-6"},[e("label",{for:"gsm-send-sms",class:"font-medium text-gray-900"},"Posílání SMS přes definouvanou GSM bránu")],-1))])])])])]))]),_:1})]),e("div",it,[o(l(Z),{onSubmit:s[21]||(s[21]=w=>K())},{default:c(({values:w})=>[v.value?(d(),r("div",ht,[o(R,{class:"mx-auto text-spinner-color",size:"40"})])):(d(),r("div",dt,[e("div",rt,[e("div",ut,[e("div",mt,[o(l(_e),{class:"w-7"}),s[63]||(s[63]=e("p",{class:"ml-4 text-lg text-gray-900"},"Fortinet",-1))]),e("div",null,[l(V).check("settings.edit")?(d(),r("button",ct,"Uložit")):x("",!0)])]),e("div",pt,[e("div",gt,[s[64]||(s[64]=e("label",{for:"fortinet-ip",class:"block text-sm font-normal leading-6 text-gray-900"},"IP adresa:",-1)),e("div",vt,[o(l(u),{modelValue:t.value.fortinet_ip,"onUpdate:modelValue":s[19]||(s[19]=a=>t.value.fortinet_ip=a),id:"fortinet-ip",name:"fortinet-ip",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte IP adresu fortinet..."},null,8,["modelValue"])])]),e("div",xt,[s[65]||(s[65]=e("label",{for:"fortinet-token",class:"block text-sm font-normal leading-6 text-gray-900"},"Token:",-1)),e("div",bt,[o(l(u),{modelValue:t.value.fortinet_token,"onUpdate:modelValue":s[20]||(s[20]=a=>t.value.fortinet_token=a),id:"fortinet-token",name:"fortinet-token",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přístupový token..."},null,8,["modelValue"])])])])]),e("div",ft,[s[66]||(s[66]=e("span",{class:"text-sm font-semibold inline-block pb-4"},"Skupiny blokace nevhodných stránek:",-1)),e("div",_t,[(d(!0),r(O,null,B(S.value,a=>(d(),r("div",{key:a,class:"flex items-center gap-5"},[l(V).check("settings.edit")?(d(),r("button",{key:0,onClick:P(G=>i.$refs.webFiltersRef.openModal(a),["prevent"]),class:"rounded-md bg-main-color-200/75 w-8 h-8 flex justify-center items-center text-main-color-600 shadow-sm hover:bg-main-color-200"},[o(l(ye),{class:"h-4 w-4 text-main-color-600","aria-hidden":"true"})],8,yt)):x("",!0),e("span",null,C(a),1)]))),128))])])]))]),_:1})])]),e("div",null,[e("div",kt,[o(l(Z),{onSubmit:s[33]||(s[33]=w=>X())},{default:c(({values:w})=>[v.value?(d(),r("div",Xt,[o(R,{class:"mx-auto text-spinner-color",size:"40"})])):(d(),r("div",wt,[e("div",Vt,[e("div",jt,[o(l(he),{class:"w-7"}),s[67]||(s[67]=e("p",{class:"ml-4 text-lg text-gray-900"},"Active Directory",-1))]),e("div",null,[e("button",{onClick:s[22]||(s[22]=P(a=>U(),["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},s[68]||(s[68]=[e("span",null,"Otestovat připojení",-1)])),l(V).check("settings.edit")?(d(),r("button",St,"Uložit")):x("",!0)])]),e("div",Ut,[e("div",$t,[s[69]||(s[69]=e("label",{for:"ldap-username",class:"block text-sm font-normal leading-6 text-gray-900"},"Přihlašovací jméno:",-1)),e("div",Mt,[o(l(u),{modelValue:t.value.ldap_username,"onUpdate:modelValue":s[23]||(s[23]=a=>t.value.ldap_username=a),id:"ldap-username",name:"ldap-username",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno k active directory..."},null,8,["modelValue"])])]),e("div",It,[s[70]||(s[70]=e("label",{for:"ldap-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1)),e("div",Zt,[o(l(u),{modelValue:t.value.ldap_password,"onUpdate:modelValue":s[24]||(s[24]=a=>t.value.ldap_password=a),id:"ldap-password",name:"ldap-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte heslo k active directory..."},null,8,["modelValue"])])]),e("div",Ct,[s[71]||(s[71]=e("label",{for:"ldap-account-prefix",class:"block text-sm font-normal leading-6 text-gray-900"},"Prefix:",-1)),e("div",zt,[o(l(u),{modelValue:t.value.ldap_account_prefix,"onUpdate:modelValue":s[25]||(s[25]=a=>t.value.ldap_account_prefix=a),id:"ldap-account-prefix",name:"ldap-account-prefix",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte prefix..."},null,8,["modelValue"])])]),e("div",Nt,[s[72]||(s[72]=e("label",{for:"ldap-account-suffix",class:"block text-sm font-normal leading-6 text-gray-900"},"Suffix:",-1)),e("div",Pt,[o(l(u),{modelValue:t.value.ldap_account_suffix,"onUpdate:modelValue":s[26]||(s[26]=a=>t.value.ldap_account_suffix=a),id:"ldap-account-suffix",name:"ldap-account-suffix",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte suffix..."},null,8,["modelValue"])])]),e("div",Et,[s[73]||(s[73]=e("label",{for:"ldap-hosts",class:"block text-sm font-normal leading-6 text-gray-900"},"Host:",-1)),e("div",Rt,[o(l(u),{modelValue:t.value.ldap_hosts,"onUpdate:modelValue":s[27]||(s[27]=a=>t.value.ldap_hosts=a),id:"ldap-hosts",name:"ldap-hosts",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte hosts na službu active directory..."},null,8,["modelValue"])])]),e("div",Gt,[s[74]||(s[74]=e("label",{for:"ldap-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Port:",-1)),e("div",Ft,[o(l(u),{modelValue:t.value.ldap_port,"onUpdate:modelValue":s[28]||(s[28]=a=>t.value.ldap_port=a),id:"ldap-port",name:"ldap-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port na službu active directory"},null,8,["modelValue"])])]),e("div",Ot,[s[75]||(s[75]=e("label",{for:"ldap-base-dn",class:"block text-sm font-normal leading-6 text-gray-900"},"Base DN:",-1)),e("div",Lt,[o(l(u),{modelValue:t.value.ldap_base_dn,"onUpdate:modelValue":s[29]||(s[29]=a=>t.value.ldap_base_dn=a),id:"ldap-base-dn",name:"ldap-base-dn",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte base dn služby active directory..."},null,8,["modelValue"])])]),e("div",Bt,[s[78]||(s[78]=e("label",{for:"",class:"block text-sm font-normal leading-6 text-gray-900"},"Protokoly:",-1)),e("div",Ht,[e("div",Tt,[e("div",At,[o(l(u),{modelValue:t.value.ldap_use_ssl,"onUpdate:modelValue":s[30]||(s[30]=a=>t.value.ldap_use_ssl=a),value:!t.value.ldap_use_ssl,id:"ldap-use-ssl",name:"ldap-use-ssl",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"])]),s[76]||(s[76]=e("div",{class:"text-sm leading-6"},[e("label",{for:"ldap-use-ssl",class:"font-medium text-gray-900"},"SSL")],-1))]),e("div",Dt,[e("div",Jt,[o(l(u),{modelValue:t.value.ldap_use_tls,"onUpdate:modelValue":s[31]||(s[31]=a=>t.value.ldap_use_tls=a),value:!t.value.ldap_use_tls,id:"ldap-use-tls",name:"ldap-use-tls",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"])]),s[77]||(s[77]=e("div",{class:"text-sm leading-6"},[e("label",{for:"ldap-use-tls",class:"font-medium text-gray-900"},"TLS")],-1))])])])]),e("div",qt,[e("div",Wt,[s[79]||(s[79]=e("label",{for:"ldap-username-scheme",class:"block text-sm font-normal leading-6 text-gray-900"},"Schéma přihlašovacího jména:",-1)),e("div",Qt,[o(l(u),{modelValue:t.value.ldap_username_scheme,"onUpdate:modelValue":s[32]||(s[32]=a=>t.value.ldap_username_scheme=a),id:"ldap-username-scheme",name:"ldap-username-scheme",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno k active directory..."},null,8,["modelValue"])]),s[80]||(s[80]=e("div",null,[e("span",{class:"text-sm font-semibold inline-block pb-4"},"Dostupné možnosti jsou:"),e("ul",{class:"list-disc ml-6 text-sm space-y-1 mb-4"},[e("li",null,[e("span",null,"{JMENO} a {PRIJMENI} - dosadí celé jméno nebo příjmení")]),e("li",null,[e("span",null,"{PRVNI.PISMENO.JMENO} a {PRVNI.PISMENO.PRIJMENI} - dosadí první písmeno ze jména nebo příjmení")]),e("li",null,[e("span",null,"{JMENO_X} a {PRIJMENI_X}"),$(" - dosadí X (počet písmen) ze jména nebo příjmení")])]),e("span",{class:"text-sm font-semibold inline-block pb-4"},"Příklady použití:"),e("ul",{class:"list-disc ml-6 text-sm space-y-1"},[e("li",null,[$("{JMENO}.{PRIJMENI} = "),e("span",null,"petr.novak")]),e("li",null,[$("{PRIJMENI}{PRVNI.PISMENO.JMENO} = "),e("span",null,"novakp")]),e("li",null,[$("{JMENO_2}.{PRIJMENI_3} = "),e("span",null,"pe.nov")])])],-1))])])]))]),_:1})]),e("div",Kt,[o(l(Z),{onSubmit:s[39]||(s[39]=w=>oe())},{default:c(({values:w})=>[v.value?x("",!0):(d(),r("div",Yt,[e("div",el,[e("div",sl,[o(l(ke),{class:"w-7"}),s[81]||(s[81]=e("p",{class:"ml-4 text-lg text-gray-900"},"Výchozí nastavení typových rolí",-1))]),e("div",null,[l(V).check("settings.edit")?(d(),r("button",tl,"Uložit")):x("",!0)])]),e("div",ll,[e("div",ol,[e("div",al,[o(l(we),{class:"w-6 h-6"}),s[82]||(s[82]=e("span",null,"Žák",-1))])]),e("div",nl,[e("div",null,[s[83]||(s[83]=e("span",{class:"text-sm font-normal leading-6 text-gray-900 pb-2 inline-block"},"Skupiny:",-1)),o(l(u),{name:"selectedStudentGroups"},{default:c(({handleChange:a,value:G})=>[o(l(H),{name:"selectedStudentGroups",modelValue:M.value,"onUpdate:modelValue":[s[34]||(s[34]=I=>M.value=I),a],mode:"tags",label:"name","value-prop":"id",options:y.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg !text-sm"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1}),o(l(F),{name:"selectedStudentGroups",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",il,[e("div",dl,[e("div",rl,[o(l(Ve),{class:"w-6 h-6"}),s[84]||(s[84]=e("span",null,"Zaměstnanec",-1))])]),e("div",ul,[e("div",null,[s[85]||(s[85]=e("span",{class:"text-sm font-normal leading-6 text-gray-900 pb-2 inline-block"},"Skupiny:",-1)),o(l(u),{name:"selectedEmployeeGroups"},{default:c(({handleChange:a,value:G})=>[o(l(H),{name:"selectedEmployeeGroups",modelValue:g.value,"onUpdate:modelValue":[s[35]||(s[35]=I=>g.value=I),a],mode:"tags",label:"name","value-prop":"id",options:y.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg !text-sm"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1}),o(l(F),{name:"selectedEmployeeGroups",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",ml,[e("div",cl,[e("div",pl,[o(l(je),{class:"w-6 h-6"}),s[86]||(s[86]=e("span",null,"Host",-1))])]),e("div",gl,[e("div",null,[s[89]||(s[89]=e("span",{class:"text-sm font-normal leading-6 text-gray-900 pb-2 inline-block"},"Organizační jednotka:",-1)),o(l(Me),{modelValue:n.value,"onUpdate:modelValue":s[37]||(s[37]=a=>n.value=a)},{default:c(()=>[e("div",vl,[o(l(Ie),{class:"relative w-full text-left cursor-pointer block rounded-lg border-0 h-[42px] px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:c(()=>[n.value&&n.value.name?(d(),r("span",xl,[e("div",null,[$(C(n.value.name)+" ",1),n.value.is_class?(d(),r("span",bl,s[87]||(s[87]=[e("span",{class:"flex items-center"},[e("span",{class:"text-xs text-main-color-700 font-semibold"},"Třída")],-1)]))):x("",!0)])])):(d(),r("span",fl,"Zvolte OU")),e("span",_l,[o(l(Ue),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),o(l(u),{modelValue:n.value,"onUpdate:modelValue":s[36]||(s[36]=a=>n.value=a),name:"selectedHostOu",rules:"required",class:"hidden"},null,8,["modelValue"]),o(l(F),{name:"selectedHostOu",class:"text-rose-400 text-sm block pt-1"}),o(re,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:c(()=>[_.value&&_.value.length?(d(),L(l(Ce),{key:0,class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:c(()=>[(d(!0),r(O,null,B(_.value,a=>(d(),L(l(Ze),{key:a.name,value:a,as:"template"},{default:c(({active:G,selected:I})=>[e("li",{class:J([G?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[e("span",{class:J([I?"font-medium":"font-normal","block truncate"])},[e("div",null,[$(C(a.name)+" ",1),a.is_class?(d(),r("span",yl,s[88]||(s[88]=[e("span",{class:"flex items-center"},[e("span",{class:"text-xs text-main-color-700 font-semibold"},"Třída")],-1)]))):x("",!0)])],2),I?(d(),r("span",hl,[o(l($e),{class:"h-5 w-5","aria-hidden":"true"})])):x("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):x("",!0)]),_:1})])]),_:1},8,["modelValue"])]),e("div",null,[s[90]||(s[90]=e("span",{class:"text-sm font-normal leading-6 text-gray-900 pb-2 inline-block"},"Skupiny:",-1)),o(l(u),{name:"selectedHostGroups"},{default:c(({handleChange:a,value:G})=>[o(l(H),{name:"selectedHostGroups",modelValue:h.value,"onUpdate:modelValue":[s[38]||(s[38]=I=>h.value=I),a],mode:"tags",label:"name","value-prop":"id",options:y.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg !text-sm"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1}),o(l(F),{name:"selectedHostGroups",class:"text-rose-400 text-sm block pt-1"})])])])]))]),_:1})]),e("div",kl,[o(l(Z),{onSubmit:s[42]||(s[42]=w=>le())},{default:c(({values:w})=>[v.value?x("",!0):(d(),r("div",wl,[e("div",Vl,[e("div",jl,[o(l(Se),{class:"w-7"}),s[91]||(s[91]=e("p",{class:"ml-4 text-lg text-gray-900"},"Licence",-1))]),e("div",null,[l(V).check("settings.edit")?(d(),r("button",Sl,"Uložit")):x("",!0)])]),e("div",Ul,[e("div",$l,[s[92]||(s[92]=e("label",{for:"licence_key",class:"block text-sm font-normal leading-6 text-gray-900"},"Licenční klíč:",-1)),e("div",Ml,[o(l(u),{modelValue:t.value.license_key,"onUpdate:modelValue":s[40]||(s[40]=a=>t.value.license_key=a),id:"licence_key",name:"licence_key",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte licenční klíč..."},null,8,["modelValue"])])]),e("div",null,[l(V).check("settings.edit")?(d(),r("button",{key:0,onClick:s[41]||(s[41]=P(a=>te(),["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"}," Ověřit ")):x("",!0)])])]))]),_:1})])])])]),l(N)?(d(),r("div",Il,[$(C(t.value)+" ",1),s[93]||(s[93]=e("br",null,null,-1)),s[94]||(s[94]=e("br",null,null,-1)),e("span",null,"webfilters: "+C(S.value),1)])):x("",!0),o(qe,{ref_key:"webFiltersRef",ref:E,onReloadWebFilters:D},null,512),o(as,{ref_key:"backupsRef",ref:z},null,512)],64)}}};export{Al as default};

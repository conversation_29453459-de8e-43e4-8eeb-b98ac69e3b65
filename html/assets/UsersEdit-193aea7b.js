import{y as le,a as i,o as n,c as h,w as u,d as e,e as a,u as s,s as _,E as b,x as ne,h as S,k as w,q as G,j as ye,B as fe,r as xe,b as d,t as c,v as m,F as A,P as R,T as X,f as J,n as z}from"./index-f770c8ab.js";import{_ as be}from"./AppTopbar-bf151018.js";import{d as K,C as O,X as ke}from"./index-0d59a700.js";import{L as re,E as Q,a as W,S as we,e as Ve,B as ee,s as te,t as Ce,u as Ue}from"./index-3a3ac444.js";import{S as je}from"./vue-tailwind-datepicker-7114d3b6.js";import{s as ze}from"./default.css_vue_type_style_index_1_src_true_lang-abba3fec.js";import{c as B}from"./checkPermission.service-5e6704d7.js";import{E as se,A as ae,F as oe,B as Y}from"./listbox-331f328c.js";import"./hidden-ab0c4efa.js";import"./use-tracked-pointer-f932b80e.js";import"./use-resolve-button-type-23b48367.js";import"./use-controllable-eb3312b6.js";const $e={class:"space-y-4 bg-white border border-zinc-200/70 rounded-md p-5"},Pe={class:"flex justify-between items-center"},Ee={class:"flex items-center"},Se=e("p",{class:"ml-4 text-lg text-gray-900"},"Změna hesla",-1),Ze=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Uložit ",-1),De=e("label",{for:"userPassword",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1),Ie={class:"mt-2 relative"},Me={class:"grid grid-cols-12 items-end gap-4 border-b pb-8 mb-6"},Le={class:"col-span-12 sm:col-span-8"},Be=e("label",{for:"userPasswordConfirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakovat heslo:",-1),qe={class:"mt-2 relative"},Ae={class:"col-span-12 sm:col-span-4 mb-2"},Oe={class:"flex h-6 md:justify-end md:items-center"},Ye=["checked"],Ge=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"sendSms",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Zaslat do SMS")],-1),He={class:"col-span-12 sm:col-span-12 mb-2"},Fe={class:"flex h-6 justify-start items-center"},Te=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"mustChangePassword",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Vyžádat změnu hesla po přihlášení")],-1),Ne=e("div",null,[e("span",{class:"text-sm font-semibold inline-block pb-4"},"Požadavky na nové heslo:"),e("ul",{class:"list-disc ml-6 text-sm space-y-1"},[e("li",null,[S("Heslo musí mít alespoň "),e("span",null,"8 znaků")]),e("li",null,"Doporučujeme využít kombinaci malých a velkých písmen"),e("li",null,"Doporučujeme využít v hesle i číslici")])],-1),Re={__name:"usersEditChangeAdPassword",setup(ie){const q=le();i({});const V=i(null),$=i(null),f=i(0),t=i(0),p=i(!1),Z=()=>{p.value=!p.value};function D(){f.value=!f.value}function I(){w.post("/api/users/"+q.params.id+"/ad-password-update",{password:V.value,password_confirmation:$.value,must_change_password:t.value,send_sms:f.value,show_password:1}).then(x=>{G.success(x.data.message),getTableData(),closeCreateUserModal()}).catch(x=>{console.log(x)})}const M=i([{id:1,name:"A"},{id:2,name:"B"},{id:3,name:"C"},{id:4,name:"D"},{id:5,name:"E"}]);return i(M.value[0]),(x,y)=>(n(),h(s(ne),{onSubmit:I,class:"col-span-8 md:col-span-4 order-2 2xl:order-3 2xl:col-span-3"},{default:u(({values:H})=>[e("div",$e,[e("div",Pe,[e("div",Ee,[a(s(re),{class:"w-7"}),Se]),Ze]),e("div",null,[De,e("div",Ie,[a(s(_),{rules:"required|password",modelValue:V.value,"onUpdate:modelValue":y[0]||(y[0]=k=>V.value=k),id:"userPassword",name:"userPassword",type:p.value?"text":"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte heslo..."},null,8,["modelValue","type"]),a(s(b),{name:"userPassword",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:Z},[p.value?(n(),h(s(Q),{key:0,name:"eye-off",class:"h-5 w-5"})):(n(),h(s(W),{key:1,name:"eye",class:"h-5 w-5"}))])])]),e("div",Me,[e("div",Le,[Be,e("div",qe,[a(s(_),{rules:"required|password|isEqual:"+V.value,modelValue:$.value,"onUpdate:modelValue":y[1]||(y[1]=k=>$.value=k),id:"userPasswordConfirmation",name:"userPasswordConfirmation",type:p.value?"text":"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte heslo..."},null,8,["rules","modelValue","type"]),a(s(b),{name:"userPasswordConfirmation",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:Z},[p.value?(n(),h(s(Q),{key:0,name:"eye-off",class:"h-5 w-5"})):(n(),h(s(W),{key:1,name:"eye",class:"h-5 w-5"}))])])]),e("div",Ae,[e("div",Oe,[e("input",{id:"sendSms","aria-describedby":"sendSms",name:"sendSms",onClick:y[2]||(y[2]=k=>D()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",checked:f.value},null,8,Ye),Ge])]),e("div",He,[e("div",Fe,[a(s(_),{id:"mustChangePassword","aria-describedby":"mustChangePassword",name:"mustChangePassword",modelValue:t.value,"onUpdate:modelValue":y[3]||(y[3]=k=>t.value=k),value:!t.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),Te])])]),Ne])]),_:1}))}};const Xe={key:0,type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},Je={class:"md:flex justify-between items-center"},Ke={class:"pb-6 pl-2 flex items-center gap-6"},Qe={class:"text-xl"},We={key:0},et={key:1},tt={key:2},st={key:0,class:"flex gap-2 items-center"},at={class:"bg-main-color-100 rounded-full p-1"},ot=e("span",{class:"text-xs text-main-color-600"},"Lokální účet",-1),lt={class:"flex"},nt={class:"mb-6 sm:border sm:border-zinc-200/70 rounded-md sm:flex gap-x-0.5 sm:bg-zinc-200/70 space-y-2 sm:space-y-0"},rt=e("span",{class:"flex items-center"},"Základní informace ",-1),it={class:"flex items-center"},dt=e("p",{class:"ml-2"},"Role a oprávnění",-1),ct={class:"grid grid-cols-8 gap-6"},ut={class:"col-span-8 md:col-span-4 order-1 2xl:order-1 2xl:col-span-3"},mt={class:"space-y-4 bg-white border border-zinc-200/70 rounded-md p-5"},pt={class:"flex items-center"},_t=e("p",{class:"ml-4 text-lg text-gray-900"},"Základní informace",-1),vt={class:"grid grid-cols-1 sm:grid-cols-2 gap-6 gap-y-4"},gt=e("label",{for:"first-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Jméno:",-1),ht={class:"mt-2"},yt=e("label",{for:"last-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Příjmení:",-1),ft={class:"mt-2"},xt=e("label",{for:"",class:"block text-sm font-normal leading-6 text-gray-900"},"Login:",-1),bt={class:"mt-2"},kt={class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 bg-gray-100 placeholder:text-gray-400 text-sm sm:leading-6 outline-none"},wt=e("label",{for:"last-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Telefonní číslo:",-1),Vt={class:"mt-2"},Ct=e("label",{for:"new-email",class:"block text-sm font-normal leading-6 text-gray-900"},"Emailová adresa:",-1),Ut={class:"mt-2"},jt={class:"grid grid-cols-12 items-end gap-4"},zt={class:"col-span-12 sm:col-span-8"},$t=e("label",{for:"new-email-confirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakovat email:",-1),Pt={class:"mt-2"},Et={class:"col-span-12 sm:col-span-4 mb-2"},St={class:"flex h-6 md:justify-end md:items-center"},Zt=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"verifiedEmail",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Ověřený email")],-1),Dt={key:0,class:"col-span-8 md:col-span-4 order-3 2xl:order-2 2xl:col-span-2"},It={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Mt={key:0,class:"border-b pb-6"},Lt={class:"flex items-center my-4"},Bt=e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do OU",-1),qt={class:"relative mt-1"},At={key:0,class:"block truncate"},Ot={key:1,class:"block truncate text-gray-400"},Yt={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Gt={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},Ht={key:1},Ft={class:"flex items-center my-4"},Tt=e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do skupin",-1),Nt={key:2,class:"space-y-4 border-t pt-6 mt-6"},Rt={class:"flex items-center"},Xt=e("p",{class:"ml-4 text-lg text-gray-900"},"Host / expirace účtu",-1),Jt={class:"flex h-6 justify-start items-center"},Kt=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"visitor",class:"text-gray-900 cursor-pointer text-sm"},[S("Uživatelský účet je "),e("strong",null,"účet hosta")])],-1),Qt={class:"rounded-l-full w-full"},Wt={type:"button",class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},es={class:"flex items-center w-full"},ts={class:"flex-1 text-left"},ss={key:0,class:"text-gray-900"},as={key:1,class:"text-gray-400 text-sm"},os={class:"w-6 h-6"},ls={class:"flex gap-2"},ns=e("span",{class:"font-light text-sm"},"V případě nenastavení data, bude účet neomezeně platný.",-1),rs={key:0,class:"bg-white border border-zinc-200/70 rounded-md p-5 mt-6"},is={class:"flex items-center my-4"},ds=e("p",{class:"ml-4 text-lg text-gray-900"},"Doplňující informace",-1),cs={class:"grid grid-cols-3 gap-4"},us={class:"col-span-3 lg:col-span-1"},ms=e("label",{for:"profile_path",class:"block text-sm font-normal leading-6 text-gray-900"},"Cesta k profilu:",-1),ps={class:"mt-2"},_s={class:"col-span-3 lg:col-span-1"},vs=e("label",{for:"script_path",class:"block text-sm font-normal leading-6 text-gray-900"},"Logon script:",-1),gs={class:"mt-2"},hs={class:"col-span-3 lg:col-span-1 flex items-end gap-4"},ys={class:"grow"},fs=e("label",{for:"home_directory",class:"block text-sm font-normal leading-6 text-gray-900"},"Domovská složka:",-1),xs={class:"mt-2"},bs={class:"flex items-center"},ks={class:"relative mt-1"},ws={key:0,class:"block truncate"},Vs={key:1,class:"block truncate text-gray-400"},Cs={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Us={key:0,class:"absolute inset-y-0 right-0 flex items-center pl-3 text-main-color-600"},js={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},zs={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block mt-6"},$s=e("span",null,"New password styles",-1),Ps=e("br",null,null,-1),Es=e("br",null,null,-1),Ss=e("br",null,null,-1),Zs=e("br",null,null,-1),Ds=e("br",null,null,-1),Is=e("br",null,null,-1),Ms=e("br",null,null,-1),Ls=e("br",null,null,-1),Bs=e("br",null,null,-1),qs=e("br",null,null,-1),As=e("br",null,null,-1),Os=e("br",null,null,-1),ea={__name:"UsersEdit",setup(ie){const q=ye("debugModeGlobalVar"),V=i(["skolasys-root","users-edit"]),$=i(),f=le(),t=i({}),p=i(),Z=i({}),D=i({}),I=i({}),M=i(Z.value.id),x=i("email"),y=i([]),H=i([]),k=i(null),de=i(null),ce=i(0);fe(()=>{ue(),me(),_e(),pe()});function ue(){w.get("/api/users/"+f.params.id).then(r=>{t.value=r.data.data,t.value.email_confirmation=r.data.data.email,t.value.email_verified_at?p.value=!0:p.value=!1,t.value.active_directory==1?(he(),x.value="email"):x.value="required|email"}).catch(r=>{console.log(r)})}function me(){w.get("/api/organization-units?perpage=9999").then(r=>{D.value=r.data.data,M.value=r.data.data[0]}).catch(r=>{console.log(r)})}function pe(){w.get("/api/account-control-codes?listing=1").then(r=>{I.value=r.data.data}).catch(r=>{console.log(r)})}function _e(){w.get("/api/groups?perpage=9999").then(r=>{y.value=r.data.data}).catch(r=>{console.log(r)})}function ve(){var r,l,U,L,o,v,g,P,T,N;if(t.value.active_directory==1){let j={first_name:t.value.first_name,last_name:t.value.last_name,email:t.value.email,email_verified:p.value,phone:t.value.phone,organization_unit:((U=(l=(r=t.value)==null?void 0:r.active_directory_data)==null?void 0:l.organization_unit)==null?void 0:U.id)||null,visitor:t.value.active_directory_data.visitors,groups:t.value.active_directory_data.groups.map(E=>E.id),visitor:t.value.active_directory_data.visitor,profile_path:((o=(L=t.value)==null?void 0:L.active_directory_data)==null?void 0:o.profile_path)||null,script_path:((g=(v=t.value)==null?void 0:v.active_directory_data)==null?void 0:g.script_path)||null,home_directory:((T=(P=t.value)==null?void 0:P.active_directory_data)==null?void 0:T.home_directory)||null,home_drive:((N=C.value)==null?void 0:N.name)||null};t.value.active_directory_data.expire_date&&t.value.active_directory_data.expire_date[0]&&t.value.active_directory_data.expire_date[0].length&&(j.expire=R(t.value.active_directory_data.expire_date[0]).format("YYYY-MM-DD HH:MM")),w.post("/api/users/"+f.params.id+"/ad-update",j).then(E=>{G.success(E.data.message)}).catch(E=>{console.log(E)})}else w.post("/api/users/"+f.params.id+"/update",{first_name:t.value.first_name,last_name:t.value.last_name,email:t.value.email,email_verified:p.value,phone:t.value.phone}).then(j=>{G.success(j.data.message)}).catch(j=>{console.log(j)})}function ge(){t.value.active_directory_data.visitor=!t.value.active_directory_data.visitor}const F=i(Array.from({length:26},(r,l)=>({id:l+1,name:String.fromCharCode(65+l)+":"}))),C=i(null);function he(){t.value.active_directory_data.home_drive&&(C.value=F.value.find(r=>r.name==t.value.active_directory_data.home_drive))}return(r,l)=>{const U=xe("router-link");return n(),d(A,null,[a(s(ne),{onSubmit:l[16]||(l[16]=L=>ve())},{default:u(({values:L})=>[a(be,{breadCrumbs:V.value},{topbarButtons:u(()=>[a(U,{to:{name:"users"},class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200 flex items-center"},{default:u(()=>[S(" Zpět ")]),_:1}),s(B).check("users.edit")?(n(),d("button",Xe,"Uložit ")):m("",!0)]),_:1},8,["breadCrumbs"]),e("div",Je,[e("div",Ke,[e("h2",Qe,[t.value.first_name?(n(),d("span",We,c(t.value.first_name+" "),1)):m("",!0),t.value.midle_name?(n(),d("span",et,c(t.value.midle_name+" "),1)):m("",!0),t.value.last_name?(n(),d("span",tt,c(t.value.last_name),1)):m("",!0)]),t.value.active_directory==0?(n(),d("div",st,[e("span",at,[a(s(we),{class:"h-4 w-4 text-main-color-600","aria-hidden":"true"})]),ot])):m("",!0)]),e("div",lt,[e("div",nt,[a(U,{class:"flex rounded-md sm:rounded-r-none sm:rounded-t-none sm:rounded-b-none sm:rounded-l-md px-8 h-10 text-center text-sm font-medium text-gray-900 bg-white border-b-2 border-zinc-200 sm:border-white hover:bg-gray-50 hover:border-gray-50","exact-active-class":"!border-main-color-600",to:{name:"users-edit",params:{id:s(f).params.id}}},{default:u(()=>[rt]),_:1},8,["to"]),s(B).check("users.read_roles_permissions")&&s(B).check("roles.read")&&s(B).check("permissions.read")?(n(),h(U,{key:0,class:"flex rounded-md sm:rounded-l-none sm:rounded-t-none sm:rounded-b-none sm:rounded-r-md px-8 h-10 text-center text-sm font-medium text-gray-900 bg-white border-b-2 border-zinc-200 sm:border-white hover:bg-gray-50 hover:border-gray-50","exact-active-class":"!border-main-color-600",to:{name:"users-roles-permissions",params:{id:s(f).params.id}}},{default:u(()=>[e("span",it,[a(s(re),{class:"h-4"}),dt])]),_:1},8,["to"])):m("",!0)])])]),e("div",ct,[e("div",ut,[e("div",mt,[e("div",pt,[a(s(Ve),{class:"w-7"}),_t]),e("div",vt,[e("div",null,[gt,e("div",ht,[a(s(_),{rules:"required|minLength:2|maxLength:50",modelValue:t.value.first_name,"onUpdate:modelValue":l[0]||(l[0]=o=>t.value.first_name=o),type:"text",name:"first-name",id:"first-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte jméno..."},null,8,["modelValue"]),a(s(b),{name:"first-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[yt,e("div",ft,[a(s(_),{rules:"required|minLength:2|maxLength:50",modelValue:t.value.last_name,"onUpdate:modelValue":l[1]||(l[1]=o=>t.value.last_name=o),type:"text",name:"last-name",id:"last-name",autocomplete:"family-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte Příjmení..."},null,8,["modelValue"]),a(s(b),{name:"last-name",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",null,[xt,e("div",bt,[e("p",kt,c(t.value.account_name),1)])]),e("div",null,[wt,e("div",Vt,[a(s(_),{rules:"phone",modelValue:t.value.phone,"onUpdate:modelValue":l[2]||(l[2]=o=>t.value.phone=o),type:"tel",name:"phone",id:"phone",autocomplete:"phone",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte tel. číslo..."},null,8,["modelValue"]),a(s(b),{name:"phone",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[Ct,e("div",Ut,[a(s(_),{rules:x.value,modelValue:t.value.email,"onUpdate:modelValue":l[3]||(l[3]=o=>t.value.email=o),id:"new-email",name:"new-email",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte emailovou adresu..."},null,8,["rules","modelValue"]),a(s(b),{name:"new-email",class:"text-rose-400 text-sm block pt-1"})])]),e("div",jt,[e("div",zt,[$t,e("div",Pt,[a(s(_),{rules:x.value+"|isEqual:"+t.value.email,modelValue:t.value.email_confirmation,"onUpdate:modelValue":l[4]||(l[4]=o=>t.value.email_confirmation=o),id:"new-email-confirmation",name:"new-email-confirmation",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte emailovou adresu..."},null,8,["rules","modelValue"]),a(s(b),{name:"new-email-confirmation",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Et,[e("div",St,[a(s(_),{id:"verifiedEmail","aria-describedby":"verifiedEmail",name:"verifiedEmail",modelValue:p.value,"onUpdate:modelValue":l[5]||(l[5]=o=>p.value=o),value:!p.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),Zt])])])])]),t.value.active_directory==1?(n(),d("div",Dt,[e("div",It,[t.value.active_directory==1?(n(),d("div",Mt,[e("div",Lt,[a(s(ee),{class:"w-7"}),Bt]),a(s(_),{rules:"required",modelValue:t.value.active_directory_data.organization_unit,"onUpdate:modelValue":l[6]||(l[6]=o=>t.value.active_directory_data.organization_unit=o),type:"text",name:"selected_ou",id:"selected_ou",class:"hidden"},null,8,["modelValue"]),a(s(se),{modelValue:t.value.active_directory_data.organization_unit,"onUpdate:modelValue":l[7]||(l[7]=o=>t.value.active_directory_data.organization_unit=o)},{default:u(()=>[e("div",qt,[a(s(ae),{class:"relative w-full text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:u(()=>{var o,v,g,P;return[(v=(o=t.value.active_directory_data)==null?void 0:o.organization_unit)!=null&&v.name?(n(),d("span",At,c((P=(g=t.value.active_directory_data)==null?void 0:g.organization_unit)==null?void 0:P.name),1)):(n(),d("span",Ot,"Vyberte organizační jednotku...")),e("span",Yt,[a(s(K),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]}),_:1}),a(X,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:u(()=>[a(s(oe),{class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:u(()=>[(n(!0),d(A,null,J(D.value,o=>(n(),h(s(Y),{key:o.id,value:o,as:"template"},{default:u(({active:v,selected:g})=>[e("li",{class:z([v?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[e("span",{class:z([g?"font-medium":"font-normal","block truncate"])},c(o.name),3),g?(n(),d("span",Gt,[a(s(O),{class:"h-5 w-5","aria-hidden":"true"})])):m("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"]),a(s(b),{name:"selected_ou",class:"text-rose-400 text-sm block pt-1"})])):m("",!0),t.value.active_directory==1?(n(),d("div",Ht,[e("div",Ft,[a(s(te),{class:"w-7"}),Tt]),a(s(_),{name:"selectedGroups",value:t.value.active_directory_data.groups},{default:u(({handleChange:o,value:v})=>[a(s(ze),{name:"selectedGroups",modelValue:t.value.active_directory_data.groups,"onUpdate:modelValue":[l[8]||(l[8]=g=>t.value.active_directory_data.groups=g),o],mode:"tags",label:"name","value-prop":"id",options:y.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1},8,["value"]),a(s(b),{name:"selectedGroups",class:"text-rose-400 text-sm block pt-1"})])):m("",!0),t.value.active_directory==1?(n(),d("div",Nt,[e("div",Rt,[a(s(te),{class:"w-7"}),Xt]),e("div",Jt,[a(s(_),{id:"visitor","aria-describedby":"visitor",name:"visitor",onClick:l[9]||(l[9]=o=>ge()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",modelValue:t.value.active_directory_data.visitor,"onUpdate:modelValue":l[10]||(l[10]=o=>t.value.active_directory_data.visitor=o),value:!t.value.active_directory_data.visitor},null,8,["modelValue","value"]),Kt]),e("div",null,[a(s(je),{i18n:"cs","as-single":"",shortcuts:!1,modelValue:t.value.active_directory_data.expire_date,"onUpdate:modelValue":l[11]||(l[11]=o=>t.value.active_directory_data.expire_date=o),placeholder:"Zvolte datum, do kdy je účet aktivní"},{default:u(({clear:o})=>[e("div",null,[e("div",Qt,[e("button",Wt,[e("div",es,[e("div",ts,[t.value.active_directory_data.expire_date&&t.value.active_directory_data.expire_date.length?(n(),d("span",ss,[e("span",null,c(s(R)(t.value.active_directory_data.expire_date[0]).format("DD.MM.YYYY")),1)])):(n(),d("span",as,"Zvolte datum, do kdy je účet aktivní"))]),e("div",os,[t.value.active_directory_data.expire_date&&t.value.active_directory_data.expire_date.length?(n(),h(s(ke),{key:0,onClick:o,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(n(),h(s(Ce),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))])])])])])]),_:1},8,["modelValue"])]),e("div",ls,[e("div",null,[a(s(Ue),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),ns])])):m("",!0)])])):m("",!0),t.value.active_directory==1?(n(),h(Re,{key:1})):m("",!0)]),t.value.active_directory==1?(n(),d("div",rs,[e("div",is,[a(s(ee),{class:"w-7"}),ds]),e("div",cs,[e("div",us,[ms,e("div",ps,[a(s(_),{modelValue:t.value.active_directory_data.profile_path,"onUpdate:modelValue":l[12]||(l[12]=o=>t.value.active_directory_data.profile_path=o),type:"text",name:"profile_path",id:"profile_path",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cestu k profilu..."},null,8,["modelValue"])])]),e("div",_s,[vs,e("div",gs,[a(s(_),{modelValue:t.value.active_directory_data.script_path,"onUpdate:modelValue":l[13]||(l[13]=o=>t.value.active_directory_data.script_path=o),type:"text",name:"script_path",id:"script_path",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte logon script..."},null,8,["modelValue"])])]),e("div",hs,[e("div",ys,[fs,e("div",xs,[a(s(_),{modelValue:t.value.active_directory_data.home_directory,"onUpdate:modelValue":l[14]||(l[14]=o=>t.value.active_directory_data.home_directory=o),type:"text",name:"home_directory",id:"home_directory",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cestu domovské s..."},null,8,["modelValue"])])]),e("div",bs,[a(s(se),{modelValue:C.value,"onUpdate:modelValue":l[15]||(l[15]=o=>C.value=o)},{default:u(()=>[e("div",ks,[a(s(ae),{class:"relative w-16 text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:u(()=>[C.value?(n(),d("span",ws,c(C.value.name),1)):(n(),d("span",Vs,"Zvolte disk...")),e("span",Cs,[a(s(K),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),a(X,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:u(()=>[a(s(oe),{class:"absolute right-0 mt-1 max-h-60 w-46 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:u(()=>[(n(),h(s(Y),{key:"uncheck",value:null,as:"template"},{default:u(({active:o,selected:v})=>[e("li",{class:z([o?"bg-main-color-100 text-main-color-900":"text-gray-900","relative select-none py-2 pl-4 pr-10 cursor-pointer"])},[e("span",{class:z([v?"font-medium":"font-normal","block truncate"])},"Žádný",2),v?(n(),d("span",Us,[a(s(O),{class:"h-5 w-5","aria-hidden":"true"})])):m("",!0)],2)]),_:1})),(n(!0),d(A,null,J(F.value,o=>(n(),h(s(Y),{key:o.name,value:o,as:"template"},{default:u(({active:v,selected:g})=>[e("li",{class:z([v?"bg-main-color-100 text-main-color-900":"text-gray-900","relative select-none py-2 pr-10 pl-4 cursor-pointer"])},[e("span",{class:z([g?"font-medium":"font-normal","block truncate"])},c(o.name),3),g?(n(),d("span",js,[a(s(O),{class:"h-5 w-5","aria-hidden":"true"})])):m("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])])])])):m("",!0)]),_:1}),s(q)?(n(),d("div",zs,[$s,Ps,e("span",null,c(k.value),1),Es,e("span",null,c(de.value),1),Ss,e("span",null,c(ce.value),1),Zs,Ds,e("span",null,[S("user: "),e("pre",null,c(t.value),1)]),Is,e("span",null,"selected account type: "+c(r.selectedAccountType),1),Ms,e("span",null,"verified email: "+c(p.value),1),Ls,e("span",null,"send sms: "+c(r.sendSms),1),Bs,e("span",null,"selected ou: "+c(M.value),1),qs,e("span",null,[S("groups: "),e("pre",null,c(H.value),1)]),As,e("span",null,"account control codes: "+c(I.value),1),Os,e("span",null,"selected account control code: "+c($.value),1)])):m("",!0)],64)}}};export{ea as default};

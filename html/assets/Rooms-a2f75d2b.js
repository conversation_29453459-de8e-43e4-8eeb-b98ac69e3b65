import{a as p,j as J,K as se,G as O,r as A,o as s,c as Z,w as u,e as l,h as ee,d as e,u as i,z as ae,y as W,E as Y,b as a,H as V,l as x,F,f as E,n as B,t as k,T as le,m as M,x as oe,O as xe,D as be,B as he,a4 as _e,_ as ke}from"./index-cab53f7d.js";import{_ as we}from"./AppTopbar-2a35dfdd.js";import{A as $e,g as Re,u as Ce,$ as Ve,a4 as ze,o as Me}from"./index-8f3a2088.js";import{X as N,e as ne,d as ie}from"./index-0317b1f5.js";import{_ as Se}from"./pagination-384e6659.js";import{c as C}from"./checkPermission.service-d20d14d2.js";import{_ as te}from"./basicModal-4aa23279.js";import{d as re}from"./debounce-4cb23abc.js";import{N as de,$ as ue,K as me,U as ce,_ as pe}from"./combobox-6d43725e.js";import{S as Q}from"./transition-2588fa9b.js";import{g as ve,S as ge,b as fe,M as ye}from"./menu-850c33d3.js";import"./listbox-15f8154e.js";import"./hidden-cf911bf3.js";import"./use-tracked-pointer-9f23e63e.js";import"./use-resolve-button-type-8037d2d3.js";import"./use-controllable-4c28b367.js";import"./dialog-cd0c3a73.js";import"./use-tree-walker-48962991.js";const Ue={class:"p-6 gap-4 space-y-4"},Ie={class:"mt-2"},je={class:"mt-2"},Ne={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},Pe={class:"mt-1 w-full"},Be={class:"relative z-20"},Ze={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},De={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},Ke={key:0},Le={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},qe={key:1,class:"h-20 max-h-20 overflow-hidden"},Fe={key:2},Te={key:0},Ge={class:"w-full text-left"},Oe={key:0,class:"text-gray-900"},Ae={key:1,class:"text-gray-400"},Ee={key:0,class:"absolute top-2 right-9"},Qe=["onClick"],Xe={class:"border-t p-5"},He={class:"text-right space-x-3"},Je={__name:"createRoomModal",emits:["reloadItems"],setup(D,{expose:S,emit:P}){const U=P,w=p(!1);J("debugModeGlobalVar");const c=p(""),f=p(""),h=p(!1),b=p({}),_=p({}),g=p(null),n=p(null),d=p(""),m=p(!1),R=se(()=>d.value===""?b.value:b.value.filter(r=>`${r.first_name} ${r.last_name}`.toLowerCase().replace(/\s+/g,"").includes(d.value.toLowerCase().replace(/\s+/g,"")))),I=re(async()=>{try{const r=await M.get("/api/users?page=1&perpage=50&search="+d.value);b.value=r.data.data}catch(r){console.error(r)}finally{m.value=!1}},300);O(d,()=>{m.value=!0,I()});function T(){I(),G(),w.value=!0,c.value="",f.value="",g.value="",n.value=""}function j(){w.value=!1}async function G(){h.value=!0,await M.get("/api/buildings?page=1&search=").then(r=>{_.value=r.data.data}).catch(r=>{console.log(r)})}function y(r){n.value=r}async function v(){var r;C.check("rooms.create")||C.check("property.master")?await M.post("api/rooms",{name:c.value,code:f.value,user_id:g.value.id,building_id:((r=n==null?void 0:n.value)==null?void 0:r.id)||""}).then(t=>{oe.success(t.data.message)}).catch(t=>{console.log(t)}):h.value=!1,j(),U("reloadItems",!0)}return S({openModal:T}),(r,t)=>{const z=A("VueSpinner"),K=A("CheckIcon");return s(),Z(i(Q),{appear:"",show:w.value,as:"template",onClose:t[10]||(t[10]=L=>j())},{default:u(()=>[l(te,{size:"xs"},{"modal-title":u(()=>t[11]||(t[11]=[ee("Založení nové místnosti")])),"modal-close-button":u(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:t[0]||(t[0]=L=>j())},[l(i(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":u(()=>[l(i(ae),{onSubmit:t[9]||(t[9]=L=>v())},{default:u(({values:L})=>[e("div",Ue,[e("div",null,[t[12]||(t[12]=e("label",{for:"room-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název místnosti:",-1)),e("div",Ie,[l(i(W),{rules:"required",modelValue:c.value,"onUpdate:modelValue":t[1]||(t[1]=o=>c.value=o),type:"text",name:"room-name",id:"room-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název místnosti..."},null,8,["modelValue"]),l(i(Y),{name:"room-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[t[13]||(t[13]=e("label",{for:"item-count",class:"block text-sm font-normal leading-6 text-gray-900"},"Kód místnosti:",-1)),e("div",je,[l(i(W),{rules:"required",modelValue:f.value,"onUpdate:modelValue":t[2]||(t[2]=o=>f.value=o),type:"text",name:"item-code",id:"item-code",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte kód místnosti..."},null,8,["modelValue"]),l(i(Y),{name:"item-code",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Ne,[e("div",null,[t[14]||(t[14]=e("span",{class:"block text-sm font-normal leading-6 text-gray-900"},"Přiřazený uživatel (nepovinné):",-1)),e("div",Pe,[l(i(de),{modelValue:g.value,"onUpdate:modelValue":t[6]||(t[6]=o=>g.value=o)},{default:u(()=>[e("div",Be,[e("div",Ze,[l(i(ue),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte uživatele...",displayValue:o=>o?`${o.first_name||""} ${o.middle_name?o.middle_name+" ":""}${o.last_name||""}`.trim():"",onChange:t[3]||(t[3]=o=>d.value=o.target.value)},null,8,["displayValue"]),g.value&&g.value.first_name?(s(),a("div",De,[e("button",{onClick:t[4]||(t[4]=V(o=>g.value=null,["prevent"]))},[l(i(N),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):x("",!0),l(i(me),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:u(()=>[l(i(ne),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(i(Q),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:t[5]||(t[5]=o=>d.value="")},{default:u(()=>[l(i(ce),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:u(()=>[m.value?x("",!0):(s(),a("div",Ke,[R.value.length===0&&d.value!==""?(s(),a("div",Le," Žádný uživatel nenalezen. ")):x("",!0)])),m.value?(s(),a("div",qe,[l(z,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(s(),a("div",Fe,[(s(!0),a(F,null,E(R.value,o=>(s(),Z(i(pe),{as:"template",key:o.id,value:o},{default:u(({active:$})=>{var q,X,H;return[e("li",{class:B(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":$,"text-gray-900":!$}])},[e("span",{class:B(["block truncate",{"font-medium":((q=g.value)==null?void 0:q.id)==o.id,"font-normal":((X=g.value)==null?void 0:X.id)!=o.id}])},k((o==null?void 0:o.first_name)+" "+(o!=null&&o.middle_name?(o==null?void 0:o.middle_name)+" ":"")+(o==null?void 0:o.last_name)),3),((H=g.value)==null?void 0:H.id)==o.id?(s(),a("span",{key:0,class:B(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":$,"text-main-color-600":!$}])},[l(K,{class:"h-5 w-5","aria-hidden":"true"})],2)):x("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])]),_.value&&_.value.length>0?(s(),a("div",Te,[t[15]||(t[15]=e("span",{class:"block text-sm font-normal leading-6 text-gray-900"},"Přiřazená budova (nepovinné):",-1)),l(i(ve),{as:"div",class:"relative w-full text-left mt-1 z-10"},{default:u(()=>[e("div",null,[l(i(ge),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm text-white border border-gray-300"},{default:u(()=>{var o;return[e("div",Ge,[n.value&&n.value.name?(s(),a("span",Oe,k(n.value.name),1)):(s(),a("span",Ae,"Přiřazená budova.."))]),(o=n.value)!=null&&o.id?(s(),a("div",Ee,[e("button",{onClick:t[7]||(t[7]=V($=>y(null),["prevent"]))},[l(i(N),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg","aria-hidden":"true"})])])):x("",!0),l(i(ie),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]}),_:1})]),l(le,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:u(()=>[l(i(fe),{class:"absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:u(()=>[(s(!0),a(F,null,E(_.value,o=>(s(),a("div",{key:o==null?void 0:o.id,class:"px-1 py-1"},[l(i(ye),null,{default:u(({active:$})=>[e("button",{type:"button",onClick:q=>y(o),class:B([$?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},k(o.name),11,Qe)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})])):x("",!0)])]),e("div",Xe,[e("div",He,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:t[8]||(t[8]=V(o=>j(),["prevent"]))}," Zavřít "),t[16]||(t[16]=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"}," Vytvořit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"])}}},We={key:0,class:"p-6 space-y-4"},Ye={class:"mt-2"},et={class:"mt-2"},tt={class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},ot={class:"mt-1 w-full"},st={class:"relative z-20"},at={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},lt={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},nt={key:0},it={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},rt={key:1,class:"h-20 max-h-20 overflow-hidden"},dt={key:2},ut={key:0},mt={class:"w-full text-left"},ct={key:0,class:"text-gray-900"},pt={key:1,class:"text-gray-400"},vt={key:0,class:"absolute top-2 right-9"},gt=["onClick"],ft={class:"border-t p-5"},yt={class:"text-right space-x-3"},xt={__name:"editRoomModal",props:{selectedRoom:{type:Object,required:!0}},emits:["reloadRooms"],setup(D,{expose:S,emit:P}){const U=P,w=D,c=p(!1);J("debugModeGlobalVar");const f=p(""),h=p(!1);O(()=>w.selectedRoom,(r,t)=>{f.value={},d.value={id:""},m.value="",f.value=r,r.user&&(d.value=r.user),r.building&&(m.value=r.building)});function b(){c.value=!1}async function _(){j(),G(),c.value=!0}const g=p({}),n=p({}),d=p(null),m=p(null),R=p(""),I=p(!1),T=se(()=>R.value===""?g.value:g.value.filter(r=>`${r.first_name} ${r.last_name}`.toLowerCase().replace(/\s+/g,"").includes(R.value.toLowerCase().replace(/\s+/g,"")))),j=re(async()=>{try{const r=await M.get("/api/users?page=1&perpage=50&search="+R.value);g.value=r.data.data}catch(r){console.error(r)}finally{I.value=!1}},300);async function G(){h.value=!0,await M.get("/api/buildings?page=1&search=").then(r=>{n.value=r.data.data}).catch(r=>{console.log(r)})}function y(r){m.value=r}O(R,()=>{I.value=!0,j()});async function v(){var r,t;C.check("items.edit")||C.check("property.master")?(console.log(w.selectedRoom),await M.post("api/rooms/"+w.selectedRoom.id+"/update",{name:f.value.name,code:f.value.code,user_id:((r=d==null?void 0:d.value)==null?void 0:r.id)||"",building_id:((t=m==null?void 0:m.value)==null?void 0:t.id)||""}).then(z=>{oe.success(z.data.message)}).catch(z=>{console.log(z)})):h.value=!1,b(),U("reloadRooms",!0)}return S({openModal:_}),(r,t)=>{const z=A("VueSpinner"),K=A("CheckIcon");return s(),Z(i(Q),{appear:"",show:c.value,as:"template",onClose:t[10]||(t[10]=L=>b())},{default:u(()=>[l(te,{size:"sm"},{"modal-title":u(()=>t[11]||(t[11]=[ee("Úprava místnosti")])),"modal-close-button":u(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:t[0]||(t[0]=L=>b())},[l(i(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":u(()=>[l(i(ae),{onSubmit:t[9]||(t[9]=L=>v())},{default:u(({values:L})=>[f.value?(s(),a("div",We,[e("div",null,[t[12]||(t[12]=e("label",{for:"room-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název místnosti:",-1)),e("div",Ye,[l(i(W),{rules:"required",modelValue:f.value.name,"onUpdate:modelValue":t[1]||(t[1]=o=>f.value.name=o),type:"text",name:"room-name",id:"room-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název místnosti..."},null,8,["modelValue"]),l(i(Y),{name:"room-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[t[13]||(t[13]=e("label",{for:"room-code",class:"block text-sm font-normal leading-6 text-gray-900"},"Kód místnosti:",-1)),e("div",et,[l(i(W),{rules:"required",modelValue:f.value.code,"onUpdate:modelValue":t[2]||(t[2]=o=>f.value.code=o),type:"text",name:"room-code",id:"room-code",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte kód místnosti..."},null,8,["modelValue"]),l(i(Y),{name:"room-code",class:"text-rose-400 text-sm block pt-1"})])]),e("div",tt,[e("div",null,[t[14]||(t[14]=e("span",{class:"block text-sm font-normal leading-6 text-gray-900"},"Přiřazený uživatel (nepovinné):",-1)),e("div",ot,[l(i(de),{modelValue:d.value,"onUpdate:modelValue":t[6]||(t[6]=o=>d.value=o)},{default:u(()=>[e("div",st,[e("div",at,[l(i(ue),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte uživatele...",displayValue:o=>o?`${o.first_name||""} ${o.middle_name?o.middle_name+" ":""}${o.last_name||""}`.trim():"",onChange:t[3]||(t[3]=o=>R.value=o.target.value)},null,8,["displayValue"]),d.value&&d.value.first_name?(s(),a("div",lt,[e("button",{onClick:t[4]||(t[4]=V(o=>d.value=null,["prevent"]))},[l(i(N),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):x("",!0),l(i(me),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:u(()=>[l(i(ne),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(i(Q),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:t[5]||(t[5]=o=>R.value="")},{default:u(()=>[l(i(ce),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:u(()=>[I.value?x("",!0):(s(),a("div",nt,[T.value.length===0&&R.value!==""?(s(),a("div",it," Žádný uživatel nenalezen. ")):x("",!0)])),I.value?(s(),a("div",rt,[l(z,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(s(),a("div",dt,[(s(!0),a(F,null,E(T.value,o=>(s(),Z(i(pe),{as:"template",key:o.id,value:o},{default:u(({active:$})=>{var q,X,H;return[e("li",{class:B(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":$,"text-gray-900":!$}])},[e("span",{class:B(["block truncate",{"font-medium":((q=d.value)==null?void 0:q.id)==o.id,"font-normal":((X=d.value)==null?void 0:X.id)!=o.id}])},k((o==null?void 0:o.first_name)+" "+(o!=null&&o.middle_name?(o==null?void 0:o.middle_name)+" ":"")+(o==null?void 0:o.last_name)),3),((H=d.value)==null?void 0:H.id)==o.id?(s(),a("span",{key:0,class:B(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":$,"text-main-color-600":!$}])},[l(K,{class:"h-5 w-5","aria-hidden":"true"})],2)):x("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])]),n.value&&n.value.length>0?(s(),a("div",ut,[t[15]||(t[15]=e("span",{class:"block text-sm font-normal leading-6 text-gray-900"},"Přiřazená budova (nepovinné):",-1)),l(i(ve),{as:"div",class:"relative w-full text-left mt-1 z-10"},{default:u(()=>[e("div",null,[l(i(ge),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm text-white border border-gray-300"},{default:u(()=>{var o;return[e("div",mt,[m.value&&m.value.name?(s(),a("span",ct,k(m.value.name),1)):(s(),a("span",pt,"Přiřazená budova.."))]),(o=m.value)!=null&&o.id?(s(),a("div",vt,[e("button",{onClick:t[7]||(t[7]=V($=>y(null),["prevent"]))},[l(i(N),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg","aria-hidden":"true"})])])):x("",!0),l(i(ie),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]}),_:1})]),l(le,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:u(()=>[l(i(fe),{class:"absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:u(()=>[(s(!0),a(F,null,E(n.value,o=>(s(),a("div",{key:o==null?void 0:o.id,class:"px-1 py-1"},[l(i(ye),null,{default:u(({active:$})=>[e("button",{type:"button",onClick:q=>y(o),class:B([$?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},k(o.name),11,gt)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})])):x("",!0)])])):x("",!0),e("div",ft,[e("div",yt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:t[8]||(t[8]=V(o=>b(),["prevent"]))}," Zavřít "),t[16]||(t[16]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Upravit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"])}}},bt={key:0,class:"p-6"},ht={class:"border-t p-5"},_t={class:"text-right space-x-3"},kt={__name:"deleteRoomModal",props:{selectedRoom:{type:Object,required:!0}},emits:["reloadRooms"],setup(D,{expose:S,emit:P}){const U=P,w=D,c=p(!1);J("debugModeGlobalVar");const f=p(""),h=p(!1);O(()=>w.selectedRoom,(n,d)=>{f.value=n});function b(){c.value=!1}async function _(){c.value=!0}async function g(){C.check("rooms.delete")||C.check("property.master")?await M.post("api/rooms/"+f.value.id+"/delete").then(n=>{oe.success(n.data.message)}).catch(n=>{console.log(n)}):h.value=!1,b(),U("reloadRooms",!0)}return S({openModal:_}),(n,d)=>(s(),Z(i(Q),{appear:"",show:c.value,as:"template",onClose:d[3]||(d[3]=m=>b())},{default:u(()=>[l(te,{size:"sm"},{"modal-title":u(()=>d[4]||(d[4]=[ee("Smazat místnost")])),"modal-close-button":u(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:d[0]||(d[0]=m=>b())},[l(i(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":u(()=>[f.value?(s(),a("div",bt,d[5]||(d[5]=[e("span",null,"Opravdu si přejete místnost smazat?",-1)]))):x("",!0),e("div",ht,[e("div",_t,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:d[1]||(d[1]=V(m=>b(),["prevent"]))}," Zavřít "),e("button",{onClick:d[2]||(d[2]=V(m=>g(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}},wt={key:0,class:"p-6"},$t={key:0},Rt={class:"grid grid-cols-2 gap-x-4"},Ct={class:"flex items-center gap-2"},Vt={class:"mt-4 flex flex-wrap gap-x-10 gap-y-4"},zt={key:0},Mt={key:1},St={key:0},Ut={key:1},It={key:0},jt={key:1},Nt={key:0},Pt={class:"flex items-center gap-2"},Bt={class:"mt-4 flex gap-10"},Zt={key:0},Dt={key:1},Kt={key:0},Lt={key:1},qt={key:0},Ft={key:0},Tt={key:1},Gt={class:"pt-8"},Ot={class:"flex items-center gap-2 pb-4"},At={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},Et={class:"sm:-mx-6 lg:-mx-8"},Qt={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},Xt={class:"min-w-full divide-y divide-gray-200"},Ht={key:0,class:"divide-y divide-gray-200"},Jt={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},Wt={key:0},Yt={key:1},eo={class:"pl-3 pr-5 text-sm"},to={key:0},oo={key:1},so={key:1},ao={key:1,class:"py-20"},lo={class:"border-t p-5"},no={class:"text-right space-x-3"},io={__name:"detailRoomModal",props:{selectedRoom:{type:Object,required:!0}},emits:["reloadRooms"],setup(D,{expose:S,emit:P}){const U=D,w=p(!1);J("debugModeGlobalVar");const c=p(""),f=p(!1);O(()=>U.selectedRoom,(g,n)=>{_(g.id)});function h(){w.value=!1}async function b(){w.value=!0}async function _(g){f.value=!1,await M.get("/api/rooms/"+g).then(n=>{c.value=n.data.data}).catch(n=>{console.log(n)}),f.value=!1}return S({openModal:b}),(g,n)=>{const d=A("VueSpinner");return s(),Z(i(Q),{appear:"",show:w.value,as:"template",onClose:n[2]||(n[2]=m=>h())},{default:u(()=>[l(te,{size:"sm"},{"modal-title":u(()=>n[3]||(n[3]=[ee("Detail místnosti")])),"modal-close-button":u(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:n[0]||(n[0]=m=>h())},[l(i(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":u(()=>[!f.value&&c.value?(s(),a("div",wt,[c.value&&c.value.id?(s(),a("div",$t,[e("div",Rt,[e("div",null,[e("div",Ct,[l(i($e),{class:"h-6 w-6","aria-hidden":"true"}),n[4]||(n[4]=e("span",null,"Informace o místnosti",-1))]),e("div",Vt,[e("div",null,[n[5]||(n[5]=e("span",{class:"block text-xs text-gray-400"},"Kód místnosti:",-1)),c.value.id?(s(),a("span",zt,k(c.value.code),1)):(s(),a("span",Mt,"-"))]),e("div",null,[n[6]||(n[6]=e("span",{class:"block text-xs text-gray-400"},"Název místnosti:",-1)),c.value.name?(s(),a("span",St,k(c.value.name),1)):(s(),a("span",Ut,"-"))]),e("div",null,[n[7]||(n[7]=e("span",{class:"block text-xs text-gray-400"},"Budova místnosti:",-1)),c.value.building?(s(),a("span",It,k(c.value.building.name),1)):(s(),a("span",jt,"-"))])])]),c.value.user?(s(),a("div",Nt,[e("div",Pt,[l(i(Re),{class:"h-6 w-6","aria-hidden":"true"}),n[8]||(n[8]=e("span",null,"Správce místnosti",-1))]),e("div",Bt,[e("div",null,[n[9]||(n[9]=e("span",{class:"block text-xs text-gray-400"},"Jméno uživatele:",-1)),c.value.user.full_name?(s(),a("span",Zt,k(c.value.user.full_name),1)):(s(),a("span",Dt,"-"))]),e("div",null,[n[10]||(n[10]=e("span",{class:"block text-xs text-gray-400"},"E-mail uživatele:",-1)),c.value.user.email?(s(),a("span",Kt,k(c.value.user.email),1)):(s(),a("span",Lt,"-"))]),c.value.user.organization_unit?(s(),a("div",qt,[n[11]||(n[11]=e("span",{class:"block text-xs text-gray-400"},"Organizace:",-1)),c.value.user.organization_unit.name?(s(),a("span",Ft,k(c.value.user.organization_unit.name),1)):(s(),a("span",Tt,"-"))])):x("",!0)])])):x("",!0)]),e("div",Gt,[e("div",Ot,[l(i(Ce),{class:"h-6 w-6","aria-hidden":"true"}),n[12]||(n[12]=e("span",null,"Položky místnosti",-1))]),e("div",At,[e("div",Et,[e("div",Qt,[e("table",Xt,[n[14]||(n[14]=e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Ev. číslo "),e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Název položky ")])],-1)),c.value.items&&c.value.items.length?(s(),a("tbody",Ht,[(s(!0),a(F,null,E(c.value.items,m=>(s(),a("tr",{key:m.id},[e("td",Jt,[m.evidence_number?(s(),a("span",Wt,k(m.evidence_number),1)):(s(),a("span",Yt,"-"))]),e("td",eo,[m.name?(s(),a("span",to,k(m.name),1)):(s(),a("span",oo,"-"))])]))),128))])):(s(),a("tbody",so,n[13]||(n[13]=[e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné položky.")],-1)])))])])])])])])):x("",!0)])):(s(),a("div",ao,[l(d,{class:"mx-auto text-spinner-color",size:"40"})])),e("div",lo,[e("div",no,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:n[1]||(n[1]=V(m=>h(),["prevent"]))}," Zavřít ")])])]),_:1})]),_:1},8,["show"])}}},ro={class:"space-y-6"},uo={class:"px-0"},mo={class:"bg-white border border-zinc-200/70 rounded-md p-5"},co={class:"sm:flex justify-between items-center gap-4"},po={class:"w-80"},vo={class:"flex items-center gap-4"},go={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},fo={class:"sm:-mx-6 lg:-mx-8"},yo={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},xo={key:0,class:"min-w-full divide-y divide-gray-200"},bo={key:0,class:"divide-y divide-gray-200"},ho={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},_o=["onClick"],ko={class:"whitespace-nowrap py-4 px-3 text-sm text-gray-600"},wo={key:0},$o={key:1},Ro={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Co={key:0},Vo={key:1},zo={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Mo={key:0},So={key:1},Uo={class:"text-end pr-5 w-40"},Io=["onClick"],jo=["onClick"],No=["onClick"],Po={key:1},es={__name:"Rooms",setup(D){const S=p(""),P=p(""),U=p(""),w=p(""),c=xe();J("debugModeGlobalVar");const f=p(["rooms"]),h=p(!1),b=p(""),_=p(1),g=p({}),n=p({}),d=p({});be(()=>{m()}),O(()=>c.perPage,(y,v)=>{h.value=!0,_.value=1,m()});async function m(){h.value=!0,await M.get("/api/rooms?page="+_.value+"&search="+b.value).then(y=>{n.value=y.data.data,g.value=y.data.meta}).catch(y=>{console.log(y)}),h.value=!1}function R(y){d.value=y}function I(y){M.post("api/rooms/generate-pdf",{rooms:[y.id]},{responseType:"blob"}).then(v=>{const r=window.URL.createObjectURL(new Blob([v.data])),t=document.createElement("a");t.href=r,t.setAttribute("download","Položky-místnosti.pdf"),document.body.appendChild(t),t.click()}).catch(v=>{console.error("Chyba při získávání souboru:",v)})}function T(y){_.value=y,m()}function j(){h.value=!0,_.value=1,b.value="",m()}function G(){h.value=!0,_.value=1,m()}return(y,v)=>{const r=A("VueSpinner");return s(),a(F,null,[l(we,{breadCrumbs:f.value},{topbarButtons:u(()=>[i(C).check("rooms.create")||i(C).check("property.master")?(s(),a("button",{key:0,onClick:v[0]||(v[0]=V(t=>y.$refs.createRoomRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nová místnost ")):x("",!0)]),_:1},8,["breadCrumbs"]),e("div",ro,[e("div",uo,[e("div",mo,[e("div",co,[e("div",po,[he(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":v[1]||(v[1]=t=>b.value=t),onKeyup:v[2]||(v[2]=ke(t=>G(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[_e,b.value]])]),e("div",vo,[e("button",{onClick:v[3]||(v[3]=t=>j()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},v[9]||(v[9]=[e("span",null,"Resetovat",-1)])),e("button",{onClick:v[4]||(v[4]=t=>G()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",go,[e("div",fo,[e("div",yo,[h.value==!1?(s(),a("table",xo,[v[11]||(v[11]=e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md w-16"}),e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Kód místnosti "),e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Název místnosti "),e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Budova místnosti "),e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"})])],-1)),n.value&&n.value.length?(s(),a("tbody",bo,[(s(!0),a(F,null,E(n.value,t=>{var z;return s(),a("tr",{key:t.id},[e("td",ho,[i(C).check("rooms.read")||i(C).check("property.master")?(s(),a("button",{key:0,onClick:V(K=>(R(t),y.$refs.detailRoomRef.openModal()),["prevent"]),class:"mr-2"},[l(i(Ve),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,_o)):x("",!0)]),e("td",ko,[t.code?(s(),a("span",wo,k(t.code),1)):(s(),a("span",$o,"-"))]),e("td",Ro,[t.name?(s(),a("span",Co,k(t.name),1)):(s(),a("span",Vo,"-"))]),e("td",zo,[t.building?(s(),a("span",Mo,k((z=t.building)==null?void 0:z.name),1)):(s(),a("span",So,"-"))]),e("td",Uo,[e("button",{onClick:V(K=>I(t),["prevent"]),class:"mr-2"},[l(i(ze),{class:"h-8 w-8 text-white bg-main-color-600 hover:bg-main-color-700 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,Io),i(C).check("rooms.edit")||i(C).check("property.master")?(s(),a("button",{key:0,onClick:V(K=>(R(t),y.$refs.editRoomRef.openModal()),["prevent"]),class:"mr-2"},[l(i(Me),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,jo)):x("",!0),i(C).check("rooms.delete")||i(C).check("property.master")?(s(),a("button",{key:1,onClick:V(K=>(R(t),y.$refs.deleteRoomRef.openModal()),["prevent"])},[l(i(N),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,No)):x("",!0)])])}),128))])):(s(),a("tbody",Po,v[10]||(v[10]=[e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné místnosti.")],-1)])))])):(s(),Z(r,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),g.value!==null?(s(),Z(Se,{key:0,meta:g.value,onSetPage:T,modelValue:_.value,"onUpdate:modelValue":v[5]||(v[5]=t=>_.value=t)},null,8,["meta","modelValue"])):x("",!0)])]),l(Je,{ref_key:"createRoomRef",ref:S,onReloadItems:v[6]||(v[6]=t=>m())},null,512),l(xt,{ref_key:"editRoomRef",ref:P,selectedRoom:d.value,onReloadRooms:v[7]||(v[7]=t=>m())},null,8,["selectedRoom"]),l(kt,{ref_key:"deleteRoomRef",ref:U,selectedRoom:d.value,onReloadRooms:v[8]||(v[8]=t=>m())},null,8,["selectedRoom"]),l(io,{ref_key:"detailRoomRef",ref:w,selectedRoom:d.value},null,8,["selectedRoom"])],64)}}};export{es as default};

import{a as x,r as E,o as n,c as B,w as _,e as l,h as Z,d as e,u as o,x as I,t as z,b as r,F as R,f as F,E as H,s as c,C,k as b,q as g,v as f,j as T,B as G}from"./index-3de9bee7.js";import{_ as W}from"./AppTopbar-27ebc835.js";import{l as O,m as Q,E as X,N as K,n as Y,o as ee,p as se,q as te,r as oe,H as le,s as ae,t as ne,u as de}from"./index-ddf5f523.js";import{c as V}from"./checkPermission.service-9981644d.js";import{_ as J}from"./basicModal-d721522e.js";import{S as D}from"./transition-97ad7178.js";import"./dialog-ddee7d0d.js";import"./hidden-7b234e84.js";const ie={class:"p-6 web-filter-modal-data"},re=e("span",{class:"text-lg text-gray-400 font-light"},"Zvolená kategorie:",-1),ce={class:"text-xl"},ue={key:0,class:"space-y-5 border-t pt-5 mt-5 pr-5 max-h-96 overflow-y-scroll"},me={class:"space-y-4 sm:flex sm:items-center sm:space-x-10 sm:space-y-0"},pe={class:"flex items-center"},ge=["for"],_e=e("span",null,"Allow",-1),he={class:"flex items-center"},xe=["for"],be=e("span",null,"Monitor",-1),ve={class:"flex items-center"},fe=["for"],ye=e("span",null,"Block",-1),ke={class:"flex items-center"},we=["for"],Ve=e("span",null,"Warning",-1),je={key:1,class:"h-40 flex items-center"},$e={class:"border-t p-5"},Se={class:"text-right space-x-3"},Ue=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ",-1),Me={__name:"webFiltersModal",emits:["reloadWebFilter"],setup(L,{expose:N,emit:S}){const j=x(!0),$=x(""),s=x({}),m=x(!1);function w(){m.value=!1}function U(y){j.value=!0,$.value=y,b.get("/api/fortinet/webfilters/"+y).then(u=>{s.value=u.data.data.filters,j.value=!1}).catch(u=>{console.log(u)}),m.value=!0}function M(){b.post("/api/fortinet/webfilters/"+$.value+"/update",{filters:s.value}).then(y=>{g.success(y.data.message),S("reloadWebFilter",!0),w()}).catch(y=>{console.log(y),g.error("Změny se nepovedlo uložit")})}return N({openModal:U}),(y,u)=>{const h=E("VueSpinner");return n(),B(o(D),{appear:"",show:m.value,as:"template",onClose:u[3]||(u[3]=v=>w())},{default:_(()=>[l(J,{size:"lg"},{"modal-title":_(()=>[Z("Editace kategorie nevhodných stránek")]),"modal-close-button":_(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:u[0]||(u[0]=v=>w())},[l(o(O),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":_(()=>[l(o(I),{onSubmit:u[2]||(u[2]=v=>M())},{default:_(({values:v})=>[e("div",ie,[e("div",null,[re,e("p",ce,z($.value),1)]),j.value?(n(),r("div",je,[l(h,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),r("div",ue,[(n(!0),r(R,null,F(s.value,d=>(n(),r("div",{key:d.id,class:"flex justify-between items-center"},[e("span",null,z(d.name),1),l(o(H),{name:"filter-id"+d.id,class:"text-rose-400 text-sm block pt-1"},null,8,["name"]),e("fieldset",null,[e("div",me,[e("div",pe,[l(o(c),{rules:"requiredRadio",id:"allow-id-"+d.id,name:"filter-id"+d.id,type:"radio",value:"allow",modelValue:d.action,"onUpdate:modelValue":p=>d.action=p,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"allow-id-"+d.id,class:"ml-2 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[l(o(Q),{class:"mx-auto h-5 w-5 text-green-500","aria-hidden":"true"}),_e],8,ge)]),e("div",he,[l(o(c),{rules:"requiredRadio",id:"monitor-id-"+d.id,name:"filter-id"+d.id,type:"radio",value:"monitor",modelValue:d.action,"onUpdate:modelValue":p=>d.action=p,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"monitor-id-"+d.id,class:"ml-3 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[l(o(X),{class:"mx-auto h-5 w-5 text-sky-500","aria-hidden":"true"}),be],8,xe)]),e("div",ve,[l(o(c),{rules:"requiredRadio",id:"block-id-"+d.id,name:"filter-id"+d.id,type:"radio",value:"block",modelValue:d.action,"onUpdate:modelValue":p=>d.action=p,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"block-id-"+d.id,class:"ml-3 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[l(o(K),{class:"mx-auto h-5 w-5 text-red-500","aria-hidden":"true"}),ye],8,fe)]),e("div",ke,[l(o(c),{rules:"requiredRadio",id:"warning-id-"+d.id,name:"filter-id"+d.id,type:"radio",value:"warning",modelValue:d.action,"onUpdate:modelValue":p=>d.action=p,class:"h-4 w-4 border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["id","name","modelValue","onUpdate:modelValue"]),e("label",{for:"warning-id-"+d.id,class:"ml-3 text-sm font-medium leading-6 text-gray-900 cursor-pointer flex items-center gap-1"},[l(o(Y),{class:"mx-auto h-5 w-5 text-amber-500","aria-hidden":"true"}),Ve],8,we)])])])]))),128))]))]),e("div",$e,[e("div",Se,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:u[1]||(u[1]=C(d=>w(),["prevent"]))}," Zavřít "),Ue])])]),_:1})]),_:1})]),_:1},8,["show"])}}},Ie={class:"p-6"},Ze={key:0,class:"min-w-full divide-y divide-gray-200"},Ce=e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"py-4 pl-5 pr-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Název souboru "),e("th",{scope:"col",class:"py-4 pl-10 pr-5 text-right text-sm font-semibold text-gray-900 rounded-tr-md bg-gray-100/70"}," Akce ")])],-1),ze={key:0,class:"divide-y divide-gray-200"},Ne={class:"whitespace-nowrap py-3 pl-5 pr-3 text-left text-sm"},Pe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-600"},Re=["onClick"],Ee={key:1},Fe=e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné zálohy...")],-1),Le=[Fe],Be={key:1,class:"h-40 flex items-center"},Oe={class:"border-t p-5"},Je={class:"text-right space-x-3"},De={__name:"backupsModal",setup(L,{expose:N}){const S=x(!1),j=x(!1);function $(){j.value=!1}function s(){j.value=!0,w()}const m=x({});async function w(){S.value=!0,await b.get("/api/backups/").then(u=>{m.value=u.data.data}).catch(u=>{console.log(u)}),S.value=!1}const U=x(null),M=x(null);function y(u){b.post("/api/backups/download",{name:u}).then(h=>{U.value=h.headers["file-name"],M.value=window.URL.createObjectURL(new Blob([h.data],{type:h.headers["content-type"]}));var v=U.value;v=decodeURIComponent(v),v=v.replaceAll("+"," ");var d=M.value,p=document.createElement("a");p.href=d,p.setAttribute("download",v),document.body.appendChild(p),p.click(),U.value=null,M.value=null,g.success("Soubor byl úspěšně stáhnut.")}).catch(h=>{g.error("Soubor se nepodařil stáhnout.")})}return N({openModal:s}),(u,h)=>{const v=E("VueSpinner");return n(),B(o(D),{appear:"",show:j.value,as:"template",onClose:h[2]||(h[2]=d=>$())},{default:_(()=>[l(J,{size:"lg"},{"modal-title":_(()=>[Z("Zálohy Databáze")]),"modal-close-button":_(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:h[0]||(h[0]=d=>$())},[l(o(O),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":_(()=>[e("div",Ie,[S.value==!1?(n(),r("table",Ze,[Ce,m.value&&m.value.length?(n(),r("tbody",ze,[(n(!0),r(R,null,F(m.value,d=>(n(),r("tr",{key:d},[e("td",Ne,z(d.name),1),e("td",Pe,[o(V).check("backup.download")?(n(),r("button",{key:0,type:"button",onClick:C(p=>y(d.name),["prevent"]),class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100 text-right ml-auto"},[l(o(ee),{class:"mx-auto h-9 w-9 p-2 text-main-color-600","aria-hidden":"true"})],8,Re)):f("",!0)])]))),128))])):(n(),r("tbody",Ee,Le))])):(n(),r("div",Be,[l(v,{class:"mx-auto text-spinner-color",size:"40"})]))]),e("div",Oe,[e("div",Je,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:h[1]||(h[1]=C(d=>$(),["prevent"]))}," Zavřít ")])])]),_:1})]),_:1},8,["show"])}}},qe={class:"space-y-12"},Ae={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},He={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},Te={key:0,class:"p-5 pb-8"},Ge={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3"},We={class:"flex items-center"},Qe=e("p",{class:"ml-4 text-lg text-gray-900"},"MySQL Databáze",-1),Xe={key:1,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},Ke={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},Ye={class:"sm:col-span-3"},es=e("label",{for:"db-username",class:"block text-sm font-normal leading-6 text-gray-900"},"Přihlašovací jméno:",-1),ss={class:"mt-2"},ts={class:"sm:col-span-3"},os=e("label",{for:"db-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1),ls={class:"mt-2"},as={class:"sm:col-span-3"},ns=e("label",{for:"db-database",class:"block text-sm font-normal leading-6 text-gray-900"},"Název databáze:",-1),ds={class:"mt-2"},is={class:"sm:col-span-3"},rs=e("label",{for:"db-host",class:"block text-sm font-normal leading-6 text-gray-900"},"Host:",-1),cs={class:"mt-2"},us={class:"sm:col-span-3"},ms=e("label",{for:"db-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Port:",-1),ps={class:"mt-2"},gs={key:1,class:"h-40 flex items-center"},_s={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},hs={key:0,class:"p-5 pb-8"},xs={class:"flex justify-between items-center"},bs={class:"flex items-center"},vs=e("p",{class:"ml-4 text-lg text-gray-900"},"SMTP",-1),fs={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},ys={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},ks={class:"sm:col-span-3"},ws=e("label",{for:"school-mail-host",class:"block text-sm font-normal leading-6 text-gray-900"},"Host:",-1),Vs={class:"mt-2"},js={class:"sm:col-span-3"},$s=e("label",{for:"school-mail-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Port:",-1),Ss={class:"mt-2"},Us={class:"sm:col-span-3"},Ms=e("label",{for:"school-mail-username",class:"block text-sm font-normal leading-6 text-gray-900"},"Přihlašovací jméno:",-1),Is={class:"mt-2"},Zs={class:"sm:col-span-3"},Cs=e("label",{for:"school-mail-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1),zs={class:"mt-2"},Ns={class:"sm:col-span-3"},Ps=e("label",{for:"school-mail-encryption",class:"block text-sm font-normal leading-6 text-gray-900"},"Šifrování:",-1),Rs={class:"mt-2"},Es={key:1,class:"h-40 flex items-center"},Fs={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},Ls={key:0,class:"p-5 pb-8"},Bs={class:"flex justify-between items-center"},Os={class:"flex items-center"},Js=e("p",{class:"ml-4 text-lg text-gray-900"},"Gsm brána",-1),Ds={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},qs={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},As={class:"sm:col-span-3"},Hs=e("label",{for:"gsm-gate-ip",class:"block text-sm font-normal leading-6 text-gray-900"},"Ip adresa:",-1),Ts={class:"mt-2"},Gs={class:"sm:col-span-3"},Ws=e("label",{for:"gsm-gate-sim-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Sim port:",-1),Qs={class:"mt-2"},Xs={class:"sm:col-span-3"},Ks=e("label",{for:"gsm-gate-account",class:"block text-sm font-normal leading-6 text-gray-900"},"Uživatelské jméno:",-1),Ys={class:"mt-2"},et={class:"sm:col-span-3"},st=e("label",{for:"gsm-gate-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1),tt={class:"mt-2"},ot={class:"sm:col-span-3"},lt=e("label",{for:"",class:"block text-sm font-normal leading-6 text-gray-900"},"Ostatní:",-1),at={class:"flex gap-x-6 mt-4"},nt={class:"flex gap-x-3"},dt={class:"flex h-6 items-center"},it=e("div",{class:"text-sm leading-6"},[e("label",{for:"gsm-send-sms",class:"font-medium text-gray-900"},"Posílání SMS přes definouvanou GSM bránu")],-1),rt={key:1,class:"h-40 flex items-center"},ct={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},ut={key:0},mt={class:"p-5"},pt={class:"flex justify-between items-center"},gt={class:"flex items-center"},_t=e("p",{class:"ml-4 text-lg text-gray-900"},"Fortinet",-1),ht={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},xt={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},bt={class:"sm:col-span-3"},vt=e("label",{for:"fortinet-ip",class:"block text-sm font-normal leading-6 text-gray-900"},"IP adresa:",-1),ft={class:"mt-2"},yt={class:"sm:col-span-3"},kt=e("label",{for:"fortinet-token",class:"block text-sm font-normal leading-6 text-gray-900"},"Token:",-1),wt={class:"mt-2"},Vt={class:"border-t mt-1 pt-6 px-5 pb-8"},jt=e("span",{class:"text-sm font-semibold inline-block pb-4"},"Skupiny blokace nevhodných stránek:",-1),$t={class:"space-y-3"},St=["onClick"],Ut={key:1,class:"h-40 flex items-center"},Mt={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},It={key:0,class:"p-5 pb-8"},Zt={class:"flex justify-between items-center"},Ct={class:"flex items-center"},zt=e("p",{class:"ml-4 text-lg text-gray-900"},"Active Directory",-1),Nt=e("span",null,"Otestovat připojení",-1),Pt=[Nt],Rt={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},Et={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6 border-b pb-8 mb-6"},Ft={class:"sm:col-span-3"},Lt=e("label",{for:"ldap-username",class:"block text-sm font-normal leading-6 text-gray-900"},"Přihlašovací jméno:",-1),Bt={class:"mt-2"},Ot={class:"sm:col-span-3"},Jt=e("label",{for:"ldap-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1),Dt={class:"mt-2"},qt={class:"sm:col-span-3"},At=e("label",{for:"ldap-account-prefix",class:"block text-sm font-normal leading-6 text-gray-900"},"Prefix:",-1),Ht={class:"mt-2"},Tt={class:"sm:col-span-3"},Gt=e("label",{for:"ldap-account-suffix",class:"block text-sm font-normal leading-6 text-gray-900"},"Suffix:",-1),Wt={class:"mt-2"},Qt={class:"sm:col-span-3"},Xt=e("label",{for:"ldap-hosts",class:"block text-sm font-normal leading-6 text-gray-900"},"Host:",-1),Kt={class:"mt-2"},Yt={class:"sm:col-span-3"},eo=e("label",{for:"ldap-port",class:"block text-sm font-normal leading-6 text-gray-900"},"Port:",-1),so={class:"mt-2"},to={class:"sm:col-span-3"},oo=e("label",{for:"ldap-base-dn",class:"block text-sm font-normal leading-6 text-gray-900"},"Base DN:",-1),lo={class:"mt-2"},ao={class:"sm:col-span-3"},no=e("label",{for:"",class:"block text-sm font-normal leading-6 text-gray-900"},"Protokoly:",-1),io={class:"flex gap-x-6 mt-4"},ro={class:"flex gap-x-3"},co={class:"flex h-6 items-center"},uo=e("div",{class:"text-sm leading-6"},[e("label",{for:"ldap-use-ssl",class:"font-medium text-gray-900"},"SSL")],-1),mo={class:"flex gap-x-3"},po={class:"flex h-6 items-center"},go=e("div",{class:"text-sm leading-6"},[e("label",{for:"ldap-use-tls",class:"font-medium text-gray-900"},"TLS")],-1),_o={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},ho={class:"sm:col-span-6"},xo=e("label",{for:"ldap-username-scheme",class:"block text-sm font-normal leading-6 text-gray-900"},"Schéma přihlašovacího jména:",-1),bo={class:"mt-2 mb-6"},vo=e("div",null,[e("span",{class:"text-sm font-semibold inline-block pb-4"},"Dostupné možnosti jsou:"),e("ul",{class:"list-disc ml-6 text-sm space-y-1 mb-4"},[e("li",null,[e("span",null,"{JMENO} a {PRIJMENI} - dosadí celé jméno nebo příjmení")]),e("li",null,[e("span",null,"{PRVNI.PISMENO.JMENO} a {PRVNI.PISMENO.PRIJMENI} - dosadí první písmeno ze jména nebo příjmení")]),e("li",null,[e("span",null,"{JMENO_X} a {PRIJMENI_X}"),Z(" - dosadí X (počet písmen) ze jména nebo příjmení")])]),e("span",{class:"text-sm font-semibold inline-block pb-4"},"Příklady použití:"),e("ul",{class:"list-disc ml-6 text-sm space-y-1"},[e("li",null,[Z("{JMENO}.{PRIJMENI} = "),e("span",null,"petr.novak")]),e("li",null,[Z("{PRIJMENI}{PRVNI.PISMENO.JMENO} = "),e("span",null,"novakp")]),e("li",null,[Z("{JMENO_2}.{PRIJMENI_3} = "),e("span",null,"pe.nov")])])],-1),fo={key:1,class:"h-40 flex items-center"},yo={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},ko={key:0,class:"p-5"},wo={class:"flex justify-between items-center"},Vo={class:"flex items-center"},jo=e("p",{class:"ml-4 text-lg text-gray-900"},"Licence",-1),$o={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},So={class:"mt-6 pb-4 flex justify-center items-end gap-4"},Uo={class:"grow"},Mo=e("label",{for:"licence_key",class:"block text-sm font-normal leading-6 text-gray-900"},"license_key:",-1),Io={class:"mt-2"},Zo={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block mb-6"},Co=e("br",null,null,-1),zo=e("br",null,null,-1),Do={__name:"Settings",setup(L){const N=x(),S=x(),j=T("debugModeGlobalVar"),$=x(["skolasys-root","settings"]),s=x({}),m=x(!0),w=x({});G(()=>{m.value=!0,U(),p(),m.value=!1});function U(){b.get("/api/settings").then(i=>{s.value=i.data.data}).catch(i=>{console.log(i)})}function M(){b.post("/api/settings/active-directory/check",{ldap_hosts:s.value.ldap_hosts,ldap_port:s.value.ldap_port,ldap_base_dn:s.value.ldap_base_dn,ldap_username:s.value.ldap_username,ldap_password:s.value.ldap_password,ldap_account_prefix:s.value.ldap_account_prefix,ldap_account_suffix:s.value.ldap_account_suffix,ldap_use_ssl:s.value.ldap_use_ssl,ldap_use_tls:s.value.ldap_use_tls}).then(i=>{g.success(i.data.message)}).catch(i=>{})}function y(){b.post("/api/settings/active-directory",{ldap_hosts:s.value.ldap_hosts,ldap_port:s.value.ldap_port,ldap_base_dn:s.value.ldap_base_dn,ldap_username:s.value.ldap_username,ldap_password:s.value.ldap_password,ldap_account_prefix:s.value.ldap_account_prefix,ldap_account_suffix:s.value.ldap_account_suffix,ldap_use_ssl:s.value.ldap_use_ssl,ldap_use_tls:s.value.ldap_use_tls,ldap_username_scheme:s.value.ldap_username_scheme}).then(i=>{g.success(i.data.message)}).catch(i=>{})}function u(){b.post("/api/settings/fortinet",{fortinet_ip:s.value.fortinet_ip,fortinet_token:s.value.fortinet_token}).then(i=>{g.success(i.data.message)}).catch(i=>{g.error("Změny se nepovedlo uložit")})}function h(){b.post("/api/settings/school-smtp",{school_mail_host:s.value.school_mail_host,school_mail_port:s.value.school_mail_port,school_mail_username:s.value.school_mail_username,school_mail_password:s.value.school_mail_password,school_mail_encryption:s.value.school_mail_encryption}).then(i=>{g.success(i.data.message)}).catch(i=>{g.error("Změny se nepovedlo uložit")})}function v(){b.post("/api/settings/gsm-gate",{gsm_gate_ip:s.value.gsm_gate_ip,gsm_gate_account:s.value.gsm_gate_account,gsm_gate_password:s.value.gsm_gate_password,gsm_gate_sim_port:s.value.gsm_gate_sim_port,gsm_send_sms:s.value.gsm_send_sms}).then(i=>{g.success(i.data.message)}).catch(i=>{g.error("Změny se nepovedlo uložit")})}function d(){b.post("/api/settings/mysql",{db_host:s.value.db_host,db_port:s.value.db_port,db_database:s.value.db_database,db_username:s.value.db_username,db_password:s.value.db_password}).then(i=>{g.success(i.data.message)}).catch(i=>{console.log(i),g.error("Změny se nepovedlo uložit")})}function p(){b.get("/api/fortinet/webfilters").then(i=>{w.value=i.data.data.webfilters}).catch(i=>{console.log(i)})}function q(){b.post("/api/settings/license/check",{license_key:s.value.license_key}).then(i=>{g.success(i.data.message)}).catch(i=>{})}function A(){b.post("/api/settings/license",{license_key:s.value.license_key}).then(i=>{g.success(i.data.message)}).catch(i=>{})}return(i,t)=>{const P=E("VueSpinner");return n(),r(R,null,[l(W,{breadCrumbs:$.value},{topbarButtons:_(()=>[]),_:1},8,["breadCrumbs"]),e("div",qe,[e("div",Ae,[e("div",null,[e("div",He,[m.value?(n(),r("div",gs,[l(P,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),r("div",Te,[l(o(I),{onSubmit:t[6]||(t[6]=k=>d())},{default:_(({values:k})=>[e("div",Ge,[e("div",We,[l(o(se),{class:"w-7"}),Qe]),e("div",null,[o(V).check("backup.read")?(n(),r("button",{key:0,onClick:t[0]||(t[0]=C(a=>i.$refs.backupsRef.openModal(),["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"}," Zálohy ")):f("",!0),o(V).check("settings.edit")?(n(),r("button",Xe,"Uložit")):f("",!0)])]),e("div",Ke,[e("div",Ye,[es,e("div",ss,[l(o(c),{modelValue:s.value.db_username,"onUpdate:modelValue":t[1]||(t[1]=a=>s.value.db_username=a),id:"db-username",name:"db-username",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno k MySQL databázi..."},null,8,["modelValue"])])]),e("div",ts,[os,e("div",ls,[l(o(c),{modelValue:s.value.db_password,"onUpdate:modelValue":t[2]||(t[2]=a=>s.value.db_password=a),id:"db-password",name:"db-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte heslo k MySQL databázi..."},null,8,["modelValue"])])]),e("div",as,[ns,e("div",ds,[l(o(c),{modelValue:s.value.db_database,"onUpdate:modelValue":t[3]||(t[3]=a=>s.value.db_database=a),id:"db-database",name:"db-database",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název databáze..."},null,8,["modelValue"])])]),e("div",is,[rs,e("div",cs,[l(o(c),{modelValue:s.value.db_host,"onUpdate:modelValue":t[4]||(t[4]=a=>s.value.db_host=a),id:"db-host",name:"db-host",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte host MySQL databáze..."},null,8,["modelValue"])])]),e("div",us,[ms,e("div",ps,[l(o(c),{modelValue:s.value.db_port,"onUpdate:modelValue":t[5]||(t[5]=a=>s.value.db_port=a),id:"db-port",name:"db-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port MySQL databáze..."},null,8,["modelValue"])])])])]),_:1})]))]),e("div",_s,[l(o(I),{onSubmit:t[12]||(t[12]=k=>h())},{default:_(({values:k})=>[m.value?(n(),r("div",Es,[l(P,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),r("div",hs,[e("div",xs,[e("div",bs,[l(o(te),{class:"w-7"}),vs]),e("div",null,[o(V).check("settings.edit")?(n(),r("button",fs,"Uložit")):f("",!0)])]),e("div",ys,[e("div",ks,[ws,e("div",Vs,[l(o(c),{modelValue:s.value.school_mail_host,"onUpdate:modelValue":t[7]||(t[7]=a=>s.value.school_mail_host=a),id:"school-mail-host",name:"school-mail-host",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte host k SMTP serveru..."},null,8,["modelValue"])])]),e("div",js,[$s,e("div",Ss,[l(o(c),{modelValue:s.value.school_mail_port,"onUpdate:modelValue":t[8]||(t[8]=a=>s.value.school_mail_port=a),id:"school-mail-port",name:"school-mail-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port k SMTP serveru..."},null,8,["modelValue"])])]),e("div",Us,[Ms,e("div",Is,[l(o(c),{modelValue:s.value.school_mail_username,"onUpdate:modelValue":t[9]||(t[9]=a=>s.value.school_mail_username=a),id:"school-mail-username",name:"school-mail-username",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno..."},null,8,["modelValue"])])]),e("div",Zs,[Cs,e("div",zs,[l(o(c),{modelValue:s.value.school_mail_password,"onUpdate:modelValue":t[10]||(t[10]=a=>s.value.school_mail_password=a),id:"school-mail-password",name:"school-mail-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte heslo..."},null,8,["modelValue"])])]),e("div",Ns,[Ps,e("div",Rs,[l(o(c),{modelValue:s.value.school_mail_encryption,"onUpdate:modelValue":t[11]||(t[11]=a=>s.value.school_mail_encryption=a),id:"school-mail-encryption",name:"school-mail-encryption",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte šifrování (tls)..."},null,8,["modelValue"])])])])]))]),_:1})]),e("div",Fs,[l(o(I),{onSubmit:t[18]||(t[18]=k=>v())},{default:_(({values:k})=>[m.value?(n(),r("div",rt,[l(P,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),r("div",Ls,[e("div",Bs,[e("div",Os,[l(o(oe),{class:"w-7"}),Js]),e("div",null,[o(V).check("settings.edit")?(n(),r("button",Ds,"Uložit")):f("",!0)])]),e("div",qs,[e("div",As,[Hs,e("div",Ts,[l(o(c),{modelValue:s.value.gsm_gate_ip,"onUpdate:modelValue":t[13]||(t[13]=a=>s.value.gsm_gate_ip=a),id:"dgsm-gate-ip",name:"gsm-gate-ip",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte IP adresu GSM brány..."},null,8,["modelValue"])])]),e("div",Gs,[Ws,e("div",Qs,[l(o(c),{modelValue:s.value.gsm_gate_sim_port,"onUpdate:modelValue":t[14]||(t[14]=a=>s.value.gsm_gate_sim_port=a),id:"gsm-gate-sim-port",name:"gsm-gate-sim-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port SIM karty, který se má používat..."},null,8,["modelValue"])])]),e("div",Xs,[Ks,e("div",Ys,[l(o(c),{modelValue:s.value.gsm_gate_account,"onUpdate:modelValue":t[15]||(t[15]=a=>s.value.gsm_gate_account=a),id:"gsm-gate-account",name:"gsm-gate-account",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte uživatelské jméno k GSM bráně..."},null,8,["modelValue"])])]),e("div",et,[st,e("div",tt,[l(o(c),{modelValue:s.value.gsm_gate_password,"onUpdate:modelValue":t[16]||(t[16]=a=>s.value.gsm_gate_password=a),id:"gsm-gate-password",name:"gsm-gate-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejteheslo k GSM bráně...."},null,8,["modelValue"])])]),e("div",ot,[lt,e("div",at,[e("div",nt,[e("div",dt,[l(o(c),{modelValue:s.value.gsm_send_sms,"onUpdate:modelValue":t[17]||(t[17]=a=>s.value.gsm_send_sms=a),value:!s.value.gsm_send_sms,id:"gsm-send-sms",name:"gsm-send-sms",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"])]),it])])])])]))]),_:1})]),e("div",ct,[l(o(I),{onSubmit:t[21]||(t[21]=k=>u())},{default:_(({values:k})=>[m.value?(n(),r("div",Ut,[l(P,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),r("div",ut,[e("div",mt,[e("div",pt,[e("div",gt,[l(o(le),{class:"w-7"}),_t]),e("div",null,[o(V).check("settings.edit")?(n(),r("button",ht,"Uložit")):f("",!0)])]),e("div",xt,[e("div",bt,[vt,e("div",ft,[l(o(c),{modelValue:s.value.fortinet_ip,"onUpdate:modelValue":t[19]||(t[19]=a=>s.value.fortinet_ip=a),id:"fortinet-ip",name:"fortinet-ip",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte IP adresu fortinet..."},null,8,["modelValue"])])]),e("div",yt,[kt,e("div",wt,[l(o(c),{modelValue:s.value.fortinet_token,"onUpdate:modelValue":t[20]||(t[20]=a=>s.value.fortinet_token=a),id:"fortinet-token",name:"fortinet-token",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přístupový token..."},null,8,["modelValue"])])])])]),e("div",Vt,[jt,e("div",$t,[(n(!0),r(R,null,F(w.value,a=>(n(),r("div",{key:a,class:"flex items-center gap-5"},[o(V).check("settings.edit")?(n(),r("button",{key:0,onClick:C(No=>i.$refs.webFiltersRef.openModal(a),["prevent"]),class:"rounded-md bg-main-color-200/75 w-8 h-8 flex justify-center items-center text-main-color-600 shadow-sm hover:bg-main-color-200"},[l(o(ae),{class:"h-4 w-4 text-main-color-600","aria-hidden":"true"})],8,St)):f("",!0),e("span",null,z(a),1)]))),128))])])]))]),_:1})])]),e("div",null,[e("div",Mt,[l(o(I),{onSubmit:t[33]||(t[33]=k=>y())},{default:_(({values:k})=>[m.value?(n(),r("div",fo,[l(P,{class:"mx-auto text-spinner-color",size:"40"})])):(n(),r("div",It,[e("div",Zt,[e("div",Ct,[l(o(ne),{class:"w-7"}),zt]),e("div",null,[e("button",{onClick:t[22]||(t[22]=C(a=>M(),["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},Pt),o(V).check("settings.edit")?(n(),r("button",Rt,"Uložit")):f("",!0)])]),e("div",Et,[e("div",Ft,[Lt,e("div",Bt,[l(o(c),{modelValue:s.value.ldap_username,"onUpdate:modelValue":t[23]||(t[23]=a=>s.value.ldap_username=a),id:"ldap-username",name:"ldap-username",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno k active directory..."},null,8,["modelValue"])])]),e("div",Ot,[Jt,e("div",Dt,[l(o(c),{modelValue:s.value.ldap_password,"onUpdate:modelValue":t[24]||(t[24]=a=>s.value.ldap_password=a),id:"ldap-password",name:"ldap-password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte heslo k active directory..."},null,8,["modelValue"])])]),e("div",qt,[At,e("div",Ht,[l(o(c),{modelValue:s.value.ldap_account_prefix,"onUpdate:modelValue":t[25]||(t[25]=a=>s.value.ldap_account_prefix=a),id:"ldap-account-prefix",name:"ldap-account-prefix",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte prefix..."},null,8,["modelValue"])])]),e("div",Tt,[Gt,e("div",Wt,[l(o(c),{modelValue:s.value.ldap_account_suffix,"onUpdate:modelValue":t[26]||(t[26]=a=>s.value.ldap_account_suffix=a),id:"ldap-account-suffix",name:"ldap-account-suffix",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte suffix..."},null,8,["modelValue"])])]),e("div",Qt,[Xt,e("div",Kt,[l(o(c),{modelValue:s.value.ldap_hosts,"onUpdate:modelValue":t[27]||(t[27]=a=>s.value.ldap_hosts=a),id:"ldap-hosts",name:"ldap-hosts",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte hosts na službu active directory..."},null,8,["modelValue"])])]),e("div",Yt,[eo,e("div",so,[l(o(c),{modelValue:s.value.ldap_port,"onUpdate:modelValue":t[28]||(t[28]=a=>s.value.ldap_port=a),id:"ldap-port",name:"ldap-port",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte port na službu active directory"},null,8,["modelValue"])])]),e("div",to,[oo,e("div",lo,[l(o(c),{modelValue:s.value.ldap_base_dn,"onUpdate:modelValue":t[29]||(t[29]=a=>s.value.ldap_base_dn=a),id:"ldap-base-dn",name:"ldap-base-dn",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte base dn služby active directory..."},null,8,["modelValue"])])]),e("div",ao,[no,e("div",io,[e("div",ro,[e("div",co,[l(o(c),{modelValue:s.value.ldap_use_ssl,"onUpdate:modelValue":t[30]||(t[30]=a=>s.value.ldap_use_ssl=a),value:!s.value.ldap_use_ssl,id:"ldap-use-ssl",name:"ldap-use-ssl",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"])]),uo]),e("div",mo,[e("div",po,[l(o(c),{modelValue:s.value.ldap_use_tls,"onUpdate:modelValue":t[31]||(t[31]=a=>s.value.ldap_use_tls=a),value:!s.value.ldap_use_tls,id:"ldap-use-tls",name:"ldap-use-tls",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"])]),go])])])]),e("div",_o,[e("div",ho,[xo,e("div",bo,[l(o(c),{modelValue:s.value.ldap_username_scheme,"onUpdate:modelValue":t[32]||(t[32]=a=>s.value.ldap_username_scheme=a),id:"ldap-username-scheme",name:"ldap-username-scheme",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte přihlašovací jméno k active directory..."},null,8,["modelValue"])]),vo])])]))]),_:1})]),e("div",yo,[l(o(I),{onSubmit:t[36]||(t[36]=k=>A())},{default:_(({values:k})=>[m.value?f("",!0):(n(),r("div",ko,[e("div",wo,[e("div",Vo,[l(o(de),{class:"w-7"}),jo]),e("div",null,[o(V).check("settings.edit")?(n(),r("button",$o,"Uložit")):f("",!0)])]),e("div",So,[e("div",Uo,[Mo,e("div",Io,[l(o(c),{modelValue:s.value.license_key,"onUpdate:modelValue":t[34]||(t[34]=a=>s.value.license_key=a),id:"licence_key",name:"licence_key",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte license_key..."},null,8,["modelValue"])])]),e("div",null,[o(V).check("settings.edit")?(n(),r("button",{key:0,onClick:t[35]||(t[35]=C(a=>q(),["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"}," Ověřit ")):f("",!0)])])]))]),_:1})])])])]),o(j)?(n(),r("div",Zo,[Z(z(s.value)+" ",1),Co,zo,e("span",null,"webfilters: "+z(w.value),1)])):f("",!0),l(Me,{ref_key:"webFiltersRef",ref:N,onReloadWebFilters:p},null,512),l(De,{ref_key:"backupsRef",ref:S},null,512)],64)}}};export{Do as default};

import{a as x,o as m,c as M,w as i,e as s,h as U,d as e,u as t,y as I,v as k,E as w,k as V,s as S,b as g,f as q,n as N,t as Z,x as $,F as j,j as Y,C as ee,G as D}from"./index-e505f2bc.js";import{_ as te}from"./AppTopbar-3ffa7ee1.js";import{t as G,o as H}from"./index-079bbc30.js";import{c as z}from"./checkPermission.service-1648a8ba.js";import{_ as F}from"./basicModal-3b1f6411.js";import{X as K,e as O,C as T}from"./index-84528390.js";import{S as E}from"./transition-feb753d8.js";import{N as X,H as L,$ as A,K as J,U as Q,_ as W}from"./combobox-ff620e97.js";import"./dialog-a220f9aa.js";import"./hidden-a70e7379.js";import"./use-tracked-pointer-22b1e651.js";import"./use-resolve-button-type-b3f8121e.js";import"./use-tree-walker-5ee0cc2f.js";import"./use-controllable-a2a436f1.js";const se={class:"p-6"},oe={class:"sm:col-span-3 mb-5"},ne={class:"mt-2"},ae={class:"sm:col-span-3"},le={class:"mt-2"},re={class:"border-t p-5"},ie={class:"text-right"},de={__name:"createCategoryModal",emits:["reloadPermissions"],setup(R,{expose:C,emit:_}){const y=_,c=x({}),l=x(!1);function v(){l.value=!1}function f(){l.value=!0,c.value={}}function P(){V.post("/api/permission-categories",{name:c.value.name,position:c.value.position}).then(p=>{S.success(p.data.message),y("reloadPermissions",!0),v()}).catch(p=>{console.log(p)})}return C({openModal:f}),(p,n)=>(m(),M(t(E),{appear:"",show:l.value,as:"template",onClose:n[5]||(n[5]=a=>v())},{default:i(()=>[s(F,null,{"modal-title":i(()=>n[6]||(n[6]=[U("Vytvoření nové kategorie")])),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:n[0]||(n[0]=a=>v())},[s(t(K),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[s(t(I),{onSubmit:n[4]||(n[4]=a=>P())},{default:i(({values:a})=>[e("div",se,[e("div",oe,[n[7]||(n[7]=e("label",{for:"name",class:"block leading-6 text-gray-900"},"Název kategorie:",-1)),e("div",ne,[s(t(k),{rules:"required",modelValue:c.value.name,"onUpdate:modelValue":n[1]||(n[1]=o=>c.value.name=o),type:"text",name:"name",id:"name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název kategorie oprávnění"},null,8,["modelValue"]),s(t(w),{name:"name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",ae,[n[8]||(n[8]=e("label",{for:"position",class:"block leading-6 text-gray-900"},"Pozice kategorie:",-1)),e("div",le,[s(t(k),{rules:"required",modelValue:c.value.position,"onUpdate:modelValue":n[2]||(n[2]=o=>c.value.position=o),type:"number",name:"position",id:"position",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte číselnou pozici kategorie"},null,8,["modelValue"]),s(t(w),{name:"position",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",re,[e("div",ie,[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:n[3]||(n[3]=o=>v())},n[9]||(n[9]=[e("span",{class:"text-main-color-600"},"Zavřít",-1)])),n[10]||(n[10]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},me={class:"p-6"},ue={class:"sm:col-span-3 mb-5"},ce={class:"mt-2"},pe={class:"sm:col-span-3 mb-5"},ge={class:"mt-2"},ve={class:"sm:col-span-3 mb-5"},be={class:"mt-2"},fe={class:"relative mt-2"},xe={class:"border-t p-5"},ye={class:"text-right"},he={__name:"createPermissionModal",props:{categoriesProp:Object},emits:["reloadPermissions"],setup(R,{expose:C,emit:_}){const y=R,c=_,l=x({}),v=x(!1);function f(){v.value=!1}function P(){v.value=!0,l.value={}}function p(){V.post("/api/permissions",{name:l.value.name,human_name:l.value.human_name,position:l.value.position,category_id:l.value.selected_category.id}).then(n=>{S.success(n.data.message),c("reloadPermissions",!0),f()}).catch(n=>{console.log(n)})}return C({openModal:P}),(n,a)=>(m(),M(t(E),{appear:"",show:v.value,as:"template",onClose:a[7]||(a[7]=o=>f())},{default:i(()=>[s(F,null,{"modal-title":i(()=>a[8]||(a[8]=[U("Vytvoření nového oprávnění")])),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:a[0]||(a[0]=o=>f())},[s(t(K),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[s(t(I),{onSubmit:a[6]||(a[6]=o=>p())},{default:i(({values:o})=>[e("div",me,[e("div",ue,[a[9]||(a[9]=e("label",{for:"name",class:"block leading-6 text-gray-900"},"Systémový název",-1)),e("div",ce,[s(t(k),{rules:"required",modelValue:l.value.name,"onUpdate:modelValue":a[1]||(a[1]=d=>l.value.name=d),type:"text",name:"name",id:"name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte kód oprávnění, např. user.create"},null,8,["modelValue"]),s(t(w),{name:"name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",pe,[a[10]||(a[10]=e("label",{for:"human_name",class:"block leading-6 text-gray-900"},"Název oprávnění",-1)),e("div",ge,[s(t(k),{rules:"required",modelValue:l.value.human_name,"onUpdate:modelValue":a[2]||(a[2]=d=>l.value.human_name=d),type:"text",name:"human_name",id:"human_name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"zadejte název oprávnění, např. Úprava uživatele"},null,8,["modelValue"]),s(t(w),{name:"human_name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",ve,[a[11]||(a[11]=e("label",{for:"position",class:"block leading-6 text-gray-900"},"Pozice oprávnění",-1)),e("div",be,[s(t(k),{rules:"required",modelValue:l.value.position,"onUpdate:modelValue":a[3]||(a[3]=d=>l.value.position=d),type:"number",name:"position",id:"position",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte číselnou pozici oprávnění"},null,8,["modelValue"]),s(t(w),{name:"position",class:"text-rose-400 text-sm block pt-1"})])]),s(t(k),{name:"category",rules:"requiredRadio"},{default:i(({handleChange:d,value:r})=>[s(t(X),{as:"div",modelValue:l.value.selected_category,"onUpdate:modelValue":[a[4]||(a[4]=u=>l.value.selected_category=u),d]},{default:i(()=>[s(t(L),{class:"block leading-6 text-gray-900"},{default:i(()=>a[12]||(a[12]=[U("Kategorie oprávnění")])),_:1}),e("div",fe,[s(t(A),{class:"placeholder-gray-400 w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",onChange:u=>n.query=u.target.value,"display-value":u=>u==null?void 0:u.name,placeholder:"Zvolte kategorii oprávnění"},null,8,["onChange","display-value"]),s(t(J),{class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"},{default:i(()=>[s(t(O),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1}),y.categoriesProp.length>0?(m(),M(t(Q),{key:0,class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:i(()=>[(m(!0),g(j,null,q(y.categoriesProp,u=>(m(),M(t(W),{key:u.id,value:u,as:"template"},{default:i(({active:b,selected:h})=>[e("li",{class:N(["relative cursor-default select-none py-2 pl-3 pr-9",b?"bg-main-color-600 text-white":"text-gray-900"])},[e("span",{class:N(["block truncate",h&&"font-semibold"])},Z(u.name),3),h?(m(),g("span",{key:0,class:N(["absolute inset-y-0 right-0 flex items-center pr-4",b?"text-white":"text-main-color-600"])},[s(t(T),{class:"h-5 w-5","aria-hidden":"true"})],2)):$("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):$("",!0)])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),s(t(w),{name:"category",class:"text-rose-400 text-sm block pt-1"})]),e("div",xe,[e("div",ye,[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:a[5]||(a[5]=d=>f())},a[13]||(a[13]=[e("span",{class:"text-main-color-600"},"Zavřít",-1)])),a[14]||(a[14]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},ke={class:"p-6"},we={class:"sm:col-span-3 mb-5"},_e={class:"mt-2"},$e={class:"sm:col-span-3"},Ce={class:"mt-2"},Pe={__name:"editCategoryModal",emits:["reloadPermissions"],setup(R,{expose:C,emit:_}){const y=_,c=x({}),l=x(!1);function v(){l.value=!1}function f(p){l.value=!0,c.value=p}function P(){V.post("/api/permission-categories/"+c.value.id+"/update",{name:c.value.name,position:c.value.position}).then(p=>{S.success(p.data.message),y("reloadPermissions",!0),v()}).catch(p=>{console.log(p)})}return C({openModal:f}),(p,n)=>(m(),M(t(E),{appear:"",show:l.value,as:"template",onClose:v},{default:i(()=>[s(F,null,{"modal-title":i(()=>n[2]||(n[2]=[U("Úprava kategorie oprávnění")])),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:v},[s(t(K),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[s(t(I),{onSubmit:P},{default:i(({values:a})=>[e("div",ke,[e("div",we,[n[3]||(n[3]=e("label",{for:"name",class:"block leading-6 text-gray-900"},"Název kategorie:",-1)),e("div",_e,[s(t(k),{rules:"required",value:c.value.name,modelValue:c.value.name,"onUpdate:modelValue":n[0]||(n[0]=o=>c.value.name=o),type:"text",name:"name",id:"name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte název kategorie oprávnění"},null,8,["value","modelValue"]),s(t(w),{name:"name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",$e,[n[4]||(n[4]=e("label",{for:"position",class:"block leading-6 text-gray-900"},"Pozice kategorie:",-1)),e("div",Ce,[s(t(k),{rules:"required",value:c.value.position,modelValue:c.value.position,"onUpdate:modelValue":n[1]||(n[1]=o=>c.value.position=o),type:"number",name:"position",id:"position",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte číselnou pozici kategorie"},null,8,["value","modelValue"]),s(t(w),{name:"position",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",{class:"border-t p-5"},[e("div",{class:"text-right"},[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:v},n[5]||(n[5]=[e("span",{class:"text-main-color-600"},"Zavřít",-1)])),n[6]||(n[6]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Ve={class:"p-6"},ze={class:"sm:col-span-3"},Me={class:"mt-2"},je={class:"sm:col-span-3"},Ue={class:"mt-2"},Re={class:"sm:col-span-3"},qe={class:"mt-2"},Ze={class:"relative mt-2"},Ne={class:"border-t p-5"},Se={class:"text-right"},Be={__name:"editPermissionModal",props:{categoriesProp:Object},emits:["reloadPermissions"],setup(R,{expose:C,emit:_}){const y=R,c=_,l=x({}),v=x(!1);function f(){v.value=!1}function P(a){v.value=!0,n(a)}function p(){V.post("/api/permissions/"+l.value.id+"/update",{name:l.value.name,human_name:l.value.human_name,position:l.value.position,category_id:l.value.selected_category.id}).then(a=>{S.success(a.data.message),c("reloadPermissions",!0),f()}).catch(a=>{console.log(a)})}function n(a){V.get("/api/permissions/"+a.id).then(o=>{l.value=o.data.data,l.value.selected_category=o.data.data.category}).catch(o=>{console.log(o)})}return C({openModal:P}),(a,o)=>(m(),M(t(E),{appear:"",show:v.value,as:"template",onClose:o[7]||(o[7]=d=>f())},{default:i(()=>[s(F,null,{"modal-title":i(()=>o[8]||(o[8]=[U("Úprava oprávnění")])),"modal-close-button":i(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:o[0]||(o[0]=d=>f())},[s(t(K),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":i(()=>[s(t(I),{onSubmit:o[6]||(o[6]=d=>p())},{default:i(({values:d})=>[e("div",Ve,[e("div",ze,[o[9]||(o[9]=e("label",{for:"name",class:"block text-sm font-medium leading-6 text-gray-900"},"Systémový název",-1)),e("div",Me,[s(t(k),{rules:"required",modelValue:l.value.name,"onUpdate:modelValue":o[1]||(o[1]=r=>l.value.name=r),type:"text",name:"name",id:"name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte kód oprávnění, např. user.create"},null,8,["modelValue"]),s(t(w),{name:"name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",je,[o[10]||(o[10]=e("label",{for:"human_name",class:"block text-sm font-medium leading-6 text-gray-900"},"Název oprávnění",-1)),e("div",Ue,[s(t(k),{rules:"required",modelValue:l.value.human_name,"onUpdate:modelValue":o[2]||(o[2]=r=>l.value.human_name=r),type:"text",name:"human_name",id:"human_name",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"zadejte název oprávnění, např. Úprava uživatele"},null,8,["modelValue"]),s(t(w),{name:"human_name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Re,[o[11]||(o[11]=e("label",{for:"position",class:"block text-sm font-medium leading-6 text-gray-900"},"Pozice oprávnění",-1)),e("div",qe,[s(t(k),{rules:"required",modelValue:l.value.position,"onUpdate:modelValue":o[3]||(o[3]=r=>l.value.position=r),type:"number",name:"position",id:"position",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte číselnou pozici oprávnění"},null,8,["modelValue"]),s(t(w),{name:"position",class:"text-rose-400 text-sm block pt-1"})])]),s(t(k),{name:"category",rules:"requiredRadio"},{default:i(({handleChange:r,value:u})=>[s(t(X),{as:"div",modelValue:l.value.selected_category,"onUpdate:modelValue":[o[4]||(o[4]=b=>l.value.selected_category=b),r]},{default:i(()=>[s(t(L),{class:"block text-sm font-medium leading-6 text-gray-900"},{default:i(()=>o[12]||(o[12]=[U("Kategorie oprávnění")])),_:1}),e("div",Ze,[s(t(A),{class:"w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-10 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",onChange:b=>a.query=b.target.value,"display-value":b=>b==null?void 0:b.name,placeholder:"Zvolte kategorii oprávnění"},null,8,["onChange","display-value"]),s(t(J),{class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none"},{default:i(()=>[s(t(O),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1}),y.categoriesProp.length>0?(m(),M(t(Q),{key:0,class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:i(()=>[(m(!0),g(j,null,q(y.categoriesProp,b=>(m(),M(t(W),{key:b.id,value:b,as:"template"},{default:i(({active:h,selected:B})=>[e("li",{class:N(["relative cursor-default select-none py-2 pl-3 pr-9",h?"bg-main-color-600 text-white":"text-gray-900"])},[e("span",{class:N(["block truncate",B&&"font-semibold"])},Z(b.name),3),B?(m(),g("span",{key:0,class:N(["absolute inset-y-0 right-0 flex items-center pr-4",h?"text-white":"text-main-color-600"])},[s(t(T),{class:"h-5 w-5","aria-hidden":"true"})],2)):$("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})):$("",!0)])]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),s(t(w),{name:"category",class:"text-rose-400 text-sm block pt-1"})]),e("div",Ne,[e("div",Se,[e("button",{type:"button",class:"rounded-md px-4 py-2.5 text-sm bg-main-color-50 shadow-sm hover:bg-main-color-100 mr-4",onClick:o[5]||(o[5]=r=>f())},o[13]||(o[13]=[e("span",{class:"text-main-color-600"},"Zavřít",-1)])),o[14]||(o[14]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Uložit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Ie={class:"px-4 sm:px-6 lg:px-8"},Fe={class:"mt-8 flow-root"},Ke={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},Ee={class:"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-8"},De={class:"flex justify-between items-center gap-3 mb-4"},Ge={class:"text-base font-semibold leading-5 text-main-color-600"},He={class:"pr-3"},Oe=["onClick"],Te=["onClick"],Xe={class:"relative flex justify-between items-center"},Le={class:"font-light"},Ae={class:"flex gap-2"},Je=["onClick"],Qe=["onClick"],We={key:1,class:"text-gray-900 text-sm text-center col-span-3"},Ye={key:1,class:"text-gray-900 text-sm text-center col-span-3"},gt={__name:"Permissions",setup(R){const C=Y("debugModeGlobalVar"),_=x({}),y=x({}),c=x(["permissions"]),l=x(null),v=x(null),f=x(null),P=x(null);ee(()=>{p(),o()});function p(){V.get("/api/permission-categories/permissions").then(d=>{_.value=d.data}).catch(d=>{console.log(d)})}async function n(d){await V.post("/api/permissions/"+d+"/delete").then(r=>{S.success(r.data.message),p()}).catch(r=>{console.log(r)})}async function a(d){await V.post("/api/permission-categories/"+d+"/delete").then(r=>{S.success(r.data.message),p()}).catch(r=>{console.log(r)})}function o(){V.get("/api/permission-categories").then(d=>{y.value=d.data.data}).catch(d=>{console.log(d)})}return(d,r)=>(m(),g(j,null,[s(te,{breadCrumbs:c.value},{topbarButtons:i(()=>[t(z).check("permissions_categories.create")?(m(),g("button",{key:0,class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:r[0]||(r[0]=u=>d.$refs.createCategoryRef.openModal())},"Přidat kategorii")):$("",!0),t(z).check("permissions.create")?(m(),g("button",{key:1,class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",onClick:r[1]||(r[1]=u=>d.$refs.createPermissionRef.openModal())},"Přidat oprávnění")):$("",!0)]),_:1},8,["breadCrumbs"]),t(C)==!0?(m(!0),g(j,{key:0},q(_.value,u=>(m(),g("div",{key:u.id},[(m(!0),g(j,null,q(u.permissions,b=>(m(),g("div",{key:b},Z(b.name),1))),128))]))),128)):$("",!0),e("div",Ie,[e("div",Fe,[e("div",Ke,[e("div",Ee,[t(z).check("permissions_categories.read")?(m(!0),g(j,{key:0},q(_.value,(u,b)=>(m(),g("div",{class:"bg-white border border-zinc-200/70 rounded-md p-5",key:u.id},[e("div",De,[e("h2",Ge,[e("span",He,Z(b+1),1),U(Z(u.name),1)]),e("div",null,[t(z).check("permissions_categories.delete")?(m(),g("button",{key:0,class:"mr-2",onClick:h=>a(u.id)},[s(t(G),{class:"mx-auto h-9 w-9 p-2 text-red-600 bg-red-200/70 hover:bg-red-200 rounded-lg","aria-hidden":"true"})],8,Oe)):$("",!0),t(z).check("permissions_categories.edit")?(m(),g("button",{key:1,onClick:h=>d.$refs.editCategoryRef.openModal(u)},[s(t(H),{class:"mx-auto h-9 w-9 p-2 bg-main-color-50 text-main-color-600 hover:bg-main-color-100 rounded-lg","aria-hidden":"true"})],8,Te)):$("",!0)])]),t(z).check("permissions.read")?(m(!0),g(j,{key:0},q(u.permissions,h=>(m(),g("div",{class:"mt-2 space-y-2",key:h.id},[e("div",Xe,[e("span",Le,Z(h.human_name),1),e("div",Ae,[t(z).check("permissions.delete")?(m(),g("button",{key:0,onClick:D(B=>n(h.id),["prevent"])},[s(t(G),{class:"h-9 w-9 text-red-600 hover:bg-red-100/50 p-2 rounded-md","aria-hidden":"true"})],8,Je)):$("",!0),t(z).check("permissions.edit")?(m(),g("button",{key:1,onClick:D(B=>d.$refs.editPermissionRef.openModal(h),["prevent"])},[s(t(H),{class:"h-9 w-9 text-main-color-600 hover:bg-main-color-100/50 p-2 rounded-md","aria-hidden":"true"})],8,Qe)):$("",!0)])])]))),128)):(m(),g("div",We,r[6]||(r[6]=[e("span",null,"Na zobrazení oprávnění nemáte dostatečná práva.",-1)])))]))),128)):(m(),g("div",Ye,r[7]||(r[7]=[e("span",null,"Na zobrazení kategorií oprávnění nemáte dostatečná práva.",-1)])))])])])]),s(de,{ref_key:"createCategoryRef",ref:l,onReloadPermissions:r[2]||(r[2]=u=>(p(),o()))},null,512),s(he,{ref_key:"createPermissionRef",ref:v,onReloadPermissions:r[3]||(r[3]=u=>(p(),o())),categoriesProp:y.value},null,8,["categoriesProp"]),s(Pe,{ref_key:"editCategoryRef",ref:f,onReloadPermissions:r[4]||(r[4]=u=>(p(),o()))},null,512),s(Be,{ref_key:"editPermissionRef",ref:P,onReloadPermissions:r[5]||(r[5]=u=>(p(),o())),categoriesProp:y.value},null,8,["categoriesProp"])],64))}};export{gt as default};

import{_ as o}from"./_plugin-vue_export-helper-c27b6911.js";import{r as n,o as a,b as r,d as t,e as l,w as i,h as m}from"./index-bca36496.js";const c={},d={class:"grid min-h-full bg-gray-50 place-items-center py-24 px-6 sm:py-32 lg:px-8"},u={class:"text-center"},x={class:"mt-10 flex items-center justify-center gap-x-6"};function f(p,e){const s=n("router-link");return a(),r("main",d,[t("div",u,[e[1]||(e[1]=t("p",{class:"text-base font-semibold text-main-color-600"},"404",-1)),e[2]||(e[2]=t("h1",{class:"mt-4 text-3xl font-bold tracking-tight text-gray-900 sm:text-5xl"},"<PERSON><PERSON><PERSON><PERSON> nenalezena",-1)),e[3]||(e[3]=t("p",{class:"mt-6 text-base leading-7 text-gray-600"},"Omlouváme se, ale požadovaná stránka nebyla nalezena. Prosím, zkontrolujte URL adresu nebo se vraťte na úvodní stránku.",-1)),t("div",x,[l(s,{to:{name:"users"},href:"#",class:"rounded-md bg-main-color-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-main-color-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-main-color-600"},{default:i(()=>e[0]||(e[0]=[m("Zpět na výpis")])),_:1})])])])}const g=o(c,[["render",f]]);export{g as default};

import{a as u,o as l,c as z,w as g,e as r,h as B,d as e,u as a,D as y,B as I,k as $,r as j,b as d,F as S,q as M,v as T,f as F,t as O,s as V,E as R,x as E}from"./index-ad469968.js";import{_ as q}from"./AppTopbar-c961872c.js";import{t as D}from"./index-15865cd0.js";import{X,P as Z}from"./index-f6faa48e.js";import{c as _}from"./checkPermission.service-bd53b7df.js";import{_ as H}from"./basicModal-5d7ba3ca.js";import{S as J}from"./transition-335d453f.js";import{_ as L}from"./_plugin-vue_export-helper-c27b6911.js";import"./dialog-8577fd56.js";import"./hidden-f039557c.js";const A={class:"border-t p-5"},G={class:"text-right space-x-3"},K={__name:"deleteTimetableModal",props:{timetableProp:Object},emits:["removeTimetable"],setup(C,{expose:f,emit:k}){const i=k,w=C;function v(x){i("removeTimetable",x)}const m=u(!1);function c(){m.value=!1}function b(){m.value=!0}return f({openModal:b,closeModal:c}),(x,s)=>(l(),z(a(J),{appear:"",show:m.value,as:"template",onClose:s[3]||(s[3]=h=>c())},{default:g(()=>[r(H,null,{"modal-title":g(()=>s[4]||(s[4]=[B("Založení nového požadavku")])),"modal-close-button":g(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:s[0]||(s[0]=h=>c())},[r(a(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":g(()=>[s[5]||(s[5]=e("div",{class:"p-6 space-y-6"},[e("p",null,"Opravdu si přejete vyučovací hodinu vymazat?")],-1)),e("div",A,[e("div",G,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:s[1]||(s[1]=y(h=>c(),["prevent"]))}," Zavřít "),e("button",{class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",onClick:s[2]||(s[2]=y(h=>v(w.timetableProp),["prevent"]))}," Odebrat ")])])]),_:1})]),_:1},8,["show"]))}};const Q={key:0,type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},W={key:0},Y={key:0,class:"grid grid-cols-2 gap-x-10 gap-y-6"},ee={class:"col-span-12 md:col-span-4 xl:col-span-12 2xl:col-span-4 flex items-center gap-4"},te={class:"w-10 h-9 bg-main-color-200/75 flex justify-center items-center text-main-color-600 font-semibold text-lg rounded-lg"},oe={class:"col-span-12 md:col-span-8 xl:col-span-12 2xl:col-span-8 bg-white border border-zinc-200/70 rounded-md p-5 flex flex-col xs:flex-row justify-between xs:items-center gap-2 flex-wrap"},se={class:"flex flex-col xs:flex-row xs:items-center gap-2"},ae=["for"],ne={class:"flex flex-col xs:flex-row xs:items-center gap-2"},le=["for"],re=["onClick"],ie={key:1,class:"text-center text-gray-900 text-sm"},de={class:"mt-10"},ue={__name:"SchoolTimetable",setup(C){const f=u(),k=u(["school-timetable-root","school-timetable"]),i=u(),w=u(),v=u(!1),m=u(null),c=u();I(()=>{v.value=!0,b()});function b(){$.get("/api/timetables").then(o=>{i.value=o.data.data,w.value=o.data.meta,v.value=!1}).catch(o=>{console.log(o)})}function x(){m.value=i.value.some(o=>o.teaching_hour_number===null||o.teaching_hour_number===""),m.value?(m.value=null,M.error("U některých záznamů chybí číslo vyučovací hodiny.")):$.post("/api/timetables/update",{timetables:JSON.stringify(i.value)}).then(o=>{b(),M.success(o.data.message)}).catch(o=>{console.log(o)})}function s(){i.value.push({id:null,teaching_hour_number:null,start:"00:00:00",end:"00:00:00"})}function h(o){i.value=i.value.filter(n=>n!==o),x(),f.value.closeModal()}function P(o){c.value=o}return(o,n)=>{const U=j("VueSpinner");return l(),d(S,null,[r(a(E),{onSubmit:n[1]||(n[1]=N=>x())},{default:g(({values:N})=>[r(q,{breadCrumbs:k.value},{topbarButtons:g(()=>[a(_).check("timetables.edit")?(l(),d("button",Q,"Uložit rozvrh ")):T("",!0)]),_:1},8,["breadCrumbs"]),v.value?(l(),z(U,{key:1,class:"mx-auto text-spinner-color",size:"40"})):(l(),d("div",W,[a(_).check("timetables.read")?(l(),d("div",Y,[(l(!0),d(S,null,F(i.value,t=>(l(),d("div",{key:t.di,class:"grid grid-cols-12 gap-y-2 col-span-1 xs:col-span-2 xl:col-span-1"},[e("div",ee,[e("div",te,[e("span",null,O(t.teaching_hour_number),1)]),n[2]||(n[2]=e("div",null,"Vyučovací hodina",-1))]),e("div",oe,[e("div",null,[r(a(V),{rules:"required|minMax:0,999",modelValue:t.teaching_hour_number,"onUpdate:modelValue":p=>t.teaching_hour_number=p,type:"number",name:"teaching_hour"+t.id,id:"teaching_hour"+t.id,class:"w-12 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,8,["modelValue","onUpdate:modelValue","name","id"])]),e("div",se,[e("label",{for:"start_time"+t.id,class:"block text-sm leading-6 text-gray-900"},"VH -",8,ae),r(a(V),{modelValue:t.start,"onUpdate:modelValue":p=>t.start=p,type:"time",name:"start_time"+t.id,id:"start_time"+t.id,class:"block w-full xs:w-24 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,8,["modelValue","onUpdate:modelValue","name","id"])]),e("div",ne,[e("label",{for:"end_time"+t.id,class:"block text-sm leading-6 text-gray-900"},"do",8,le),r(a(V),{modelValue:t.end,"onUpdate:modelValue":p=>t.end=p,type:"time",name:"end_time"+t.id,id:"end_time"+t.id,class:"block w-full xs:w-24 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,8,["modelValue","onUpdate:modelValue","name","id"])]),a(_).check("timetables.edit")?(l(),d("button",{key:0,onClick:y(p=>(P(t),o.$refs.deleteTimetableRef.openModal()),["prevent"])},[r(a(D),{class:"h-7 w-7 text-red-500 hover:bg-red-200/75 duration-150 p-1 rounded-md","aria-hidden":"true"})],8,re)):T("",!0),r(a(R),{name:"teaching_hour"+t.id,class:"text-rose-400 text-sm block pt-1"},null,8,["name"])])]))),128))])):(l(),d("div",ie,n[3]||(n[3]=[e("span",null,"Nemáte dostatečná práva na zobrazení časového rozvrhu školy.",-1)]))),e("div",de,[a(_).check("timetables.edit")?(l(),d("button",{key:0,class:"border-2 border-green-500 flex items-center mx-auto p-2 pr-4 duration-150 rounded-lg hover:bg-green-500 group",onClick:n[0]||(n[0]=y(t=>s(),["prevent"]))},[r(a(Z),{class:"h-8 w-8 text-green-500 group-hover:text-white","aria-hidden":"true"}),n[4]||(n[4]=e("span",{class:"text-green-500 group-hover:text-white"},"Přidat hodinu",-1))])):T("",!0)])]))]),_:1}),r(K,{ref_key:"deleteTimetableRef",ref:f,onRemoveTimetable:h,timetableProp:c.value},null,8,["timetableProp"])],64)}}},ye=L(ue,[["__scopeId","data-v-6905783c"]]);export{ye as default};

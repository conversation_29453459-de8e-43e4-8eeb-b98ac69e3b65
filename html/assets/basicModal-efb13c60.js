import{U as d,Y as u,q as m}from"./dialog-abb0eee4.js";import{a as f,B as p,o as v,c as x,w as t,e as s,u as l,d as a,n as _,W as i}from"./index-bfe6943f.js";import{h as n}from"./transition-78618ab0.js";const h=a("div",{class:"fixed inset-0 bg-black bg-opacity-30"},null,-1),y={class:"fixed inset-0 overflow-y-auto lg:pl-72"},w={class:"flex min-h-full items-center justify-center p-4 text-center"},g={class:"flex justify-between items-center border-b p-5"},q={__name:"basicModal",props:{size:{type:String,required:!1}},setup(r){const c=r,e=f("");return p(()=>{e.value=c.size}),(o,b)=>(v(),x(l(m),{as:"div",class:"relative z-10"},{default:t(()=>[s(l(n),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0","enter-to":"opacity-100",leave:"duration-200 ease-in","leave-from":"opacity-100","leave-to":"opacity-0"},{default:t(()=>[h]),_:1}),a("div",y,[a("div",w,[s(l(n),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0 scale-95","enter-to":"opacity-100 scale-100",leave:"duration-200 ease-in","leave-from":"opacity-100 scale-100","leave-to":"opacity-0 scale-95"},{default:t(()=>[s(l(d),{class:_(["w-full transform rounded-lg bg-white text-left align-middle shadow-xl transition-all",{"max-w-5xl":e.value==="xl","max-w-4xl":e.value==="lg","max-w-2xl":e.value==="md","max-w-xl":e.value==="sm","max-w-3xl":!e.value||e.value!=="lg"}])},{default:t(()=>[a("div",g,[s(l(u),{as:"h3",class:"text-2xl font-light leading-6"},{default:t(()=>[i(o.$slots,"modal-title")]),_:3}),i(o.$slots,"modal-close-button")]),a("div",null,[a("div",null,[i(o.$slots,"modal-content")])])]),_:3},8,["class"])]),_:3})])])]),_:3}))}};export{q as _};

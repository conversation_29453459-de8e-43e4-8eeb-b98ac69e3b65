import{a as d,j as me,q as De,K as fe,r as ze,o as t,c as E,w as c,e as o,h as N,d as e,u as s,z as Ne,y as F,E as X,b as l,t as x,a4 as we,H as D,l as y,F as _,f as ee,n as A,m as K,x as Ve,D as Se,G as ye,P as Be,B as je,a5 as Fe,$ as qe,T as Ke,a9 as He}from"./index-9d9f1067.js";import{_ as Qe}from"./AppTopbar-f7fb50ba.js";import{R as Oe,a0 as Te,i as ue,L as Je,a1 as Pe,A as Xe,g as We,$ as et,o as tt,k as at}from"./index-0c6a7c95.js";import{X as H,d as lt,e as Me}from"./index-adce43bb.js";import{_ as st}from"./pagination-ccc83ede.js";import{c as P}from"./checkPermission.service-d7c9bc43.js";import{d as Re}from"./debounce-e3c39a65.js";import{_ as $e}from"./basicModal-f66ed136.js";import{S as Ye}from"./vue-tailwind-datepicker-18daaf6c.js";/* empty css             */import{N as be,$ as xe,K as he,U as ke,_ as _e}from"./combobox-e323b379.js";import{S as re}from"./transition-a42de4b5.js";import{S as ot,b as nt,M as it,g as rt}from"./menu-0b19fe78.js";import"./listbox-9038424e.js";import"./hidden-b1ebec83.js";import"./use-tracked-pointer-f803765e.js";import"./use-resolve-button-type-13e1cf97.js";import"./use-controllable-3025fab5.js";import"./dialog-71363bda.js";import"./use-tree-walker-0792b2cb.js";const dt="/assets/barcodeIconWhite-4a7321ad.svg",ut={class:"p-6 grid grid-cols-2 gap-4"},mt={class:"mt-2"},ct={class:"mt-2"},vt={class:"mt-2"},pt={class:"mt-2"},gt={class:"mt-2"},ft={class:"rounded-l-full"},yt={class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},bt={class:"flex items-center w-full"},xt={class:"w-6 h-6"},ht={class:"flex-1"},kt={key:0,class:"text-gray-900"},_t={key:1,class:"text-gray-400"},wt={class:"mt-2"},$t={class:"relative"},Ct={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},zt={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},Vt={key:0},It={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Mt={key:1,class:"h-20 max-h-20 overflow-hidden"},Dt={key:2},St={class:"col-span-2"},Ut={class:"mt-2"},jt={key:0,class:"col-span-2"},Rt=["for"],Nt={class:"mt-2"},Ot={class:"border-t p-5"},Tt={class:"text-right space-x-3"},Pt={__name:"createItemModal",emits:["reloadItems"],setup(oe,{expose:te,emit:le}){const Q=le,G=d(!1);me("debugModeGlobalVar");const O=d(null),v=d(null),z=d(null),j=d(""),V=d(null),T=d(null),w=d(null),n=De(),g=d(null),b=d(null),Y=d(""),ie=d(!1),S=d([]),M=d(!1),C=d({date:"YYYY-MM-DD",month:"MM"}),ne=fe(()=>Y.value===""?g.value:g.value.filter($=>$.name.toLowerCase().replace(/\s+/g,"").includes(Y.value.toLowerCase().replace(/\s+/g,""))));function L(){G.value=!1}function J(){G.value=!0,O.value="",v.value=null,z.value=null,j.value="",T.value="",w.value="",V.value="",b.value=null,de(),n.custom_columns&&n.custom_columns.length&&(S.value=n.custom_columns.filter($=>$.enable===1).map($=>({...$,value:""})))}async function de(){await K.get("/api/accounting-categories?page=1&perpage=99999").then($=>{g.value=$.data.data}).catch($=>{console.log($)})}async function ce(){var $;if(P.check("items.create")||P.check("property.master")){const u={};S.value.forEach(r=>{u[r.name]=r.value||""});const h={name:O.value,count:v.value,price:T.value,invoice_number:z.value,buyed_at:j.value,purchased_from:V.value,description:w.value,accounting_category_id:(($=b==null?void 0:b.value)==null?void 0:$.id)||"",...u};await K.post("api/items",h).then(r=>{Ve.success(r.data.message)}).catch(r=>{console.log(r)})}else M.value=!1;L(),Q("reloadItems",!0)}return te({openModal:J}),($,u)=>{const h=ze("VueSpinner");return t(),E(s(re),{appear:"",show:G.value,as:"template",onClose:u[14]||(u[14]=r=>L())},{default:c(()=>[o($e,{size:"md"},{"modal-title":c(()=>u[15]||(u[15]=[N("Vytvoření nové položky")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:u[0]||(u[0]=r=>L())},[o(s(H),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[o(s(Ne),{onSubmit:u[13]||(u[13]=r=>ce())},{default:c(({values:r})=>[e("div",ut,[e("div",null,[u[16]||(u[16]=e("label",{for:"item-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název položky:",-1)),e("div",mt,[o(s(F),{rules:"required",modelValue:O.value,"onUpdate:modelValue":u[1]||(u[1]=m=>O.value=m),type:"text",name:"item-name",id:"item-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název položky..."},null,8,["modelValue"]),o(s(X),{name:"item-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[u[17]||(u[17]=e("label",{for:"item-count",class:"block text-sm font-normal leading-6 text-gray-900"},"Počet položek:",-1)),e("div",ct,[o(s(F),{rules:"required|minMax:1,99999",modelValue:v.value,"onUpdate:modelValue":u[2]||(u[2]=m=>v.value=m),type:"number",name:"item-count",id:"item-count",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte počet položek..."},null,8,["modelValue"]),o(s(X),{name:"item-count",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[u[18]||(u[18]=e("label",{for:"item-price",class:"block text-sm font-normal leading-6 text-gray-900"},"Cena za kus:",-1)),e("div",vt,[o(s(F),{rules:"minMax:1,9999999999",modelValue:T.value,"onUpdate:modelValue":u[3]||(u[3]=m=>T.value=m),type:"number",name:"item-price",id:"item-price",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cenu za kus..."},null,8,["modelValue"]),o(s(X),{name:"item-price",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[u[19]||(u[19]=e("label",{for:"item-invoice",class:"block text-sm font-normal leading-6 text-gray-900"},"Číslo faktury:",-1)),e("div",pt,[o(s(F),{modelValue:z.value,"onUpdate:modelValue":u[4]||(u[4]=m=>z.value=m),type:"text",name:"item-invoice",id:"item-invoice",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte číslo faktury..."},null,8,["modelValue"]),o(s(X),{name:"item-invoice",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[u[21]||(u[21]=e("label",{for:"item-buyed-at",class:"block text-sm font-normal leading-6 text-gray-900"},"Datum zakoupení:",-1)),e("div",gt,[o(s(F),{name:"blockedTimetableDate"},{default:c(({handleChange:m,value:I})=>[o(s(Ye),{name:"blockedTimetableDate",i18n:"cs","as-single":"",shortcuts:!1,modelValue:j.value,"onUpdate:modelValue":[u[5]||(u[5]=p=>j.value=p),m],formatter:C.value,placeholder:"Zvolte datum zakoupení..."},{default:c(({clear:p})=>[e("div",null,[e("div",ft,[e("button",yt,[e("div",bt,[e("div",xt,[j.value?(t(),E(s(H),{key:0,onClick:p,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(t(),E(s(Oe),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",ht,[j.value?(t(),l("span",kt,[e("span",null,x(s(we)(j.value).format("DD.MM.YYYY")),1)])):(t(),l("span",_t,u[20]||(u[20]=[e("span",null,"Zvolte datum zakoupení...",-1)])))])])])])])]),_:2},1032,["modelValue","onUpdate:modelValue","formatter"])]),_:1})])]),e("div",null,[u[22]||(u[22]=e("label",{for:"item-buyed-place",class:"block text-sm font-normal leading-6 text-gray-900"},"Pořízeno z:",-1)),e("div",wt,[o(s(F),{modelValue:V.value,"onUpdate:modelValue":u[6]||(u[6]=m=>V.value=m),type:"text",name:"item-buyed-place",id:"item-buyed-place",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte pořízeno z..."},null,8,["modelValue"]),o(s(X),{name:"item-buyed-place",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[e("div",null,[u[23]||(u[23]=e("label",{for:"item-account-category",class:"block text-sm font-normal leading-6 text-gray-900"},"Účetní druh majetku:",-1)),o(s(be),{modelValue:b.value,"onUpdate:modelValue":u[10]||(u[10]=m=>b.value=m)},{default:c(()=>[e("div",$t,[e("div",Ct,[o(s(xe),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte druh majetku...",displayValue:m=>m?`${m.name}`.trim():"",onChange:u[7]||(u[7]=m=>Y.value=m.target.value)},null,8,["displayValue"]),b.value&&b.value.name?(t(),l("div",zt,[e("button",{onClick:u[8]||(u[8]=D(m=>b.value=null,["prevent"])),type:"button"},[o(s(H),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0),o(s(he),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:c(()=>[o(s(Te),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),o(s(re),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:u[9]||(u[9]=m=>Y.value="")},{default:c(()=>[o(s(ke),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:c(()=>[ie.value?y("",!0):(t(),l("div",Vt,[Y.value.length===0&&Y.value!==""?(t(),l("div",It," Žádný druh majetku nenalezen. ")):y("",!0)])),ie.value?(t(),l("div",Mt,[o(h,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(t(),l("div",Dt,[(t(!0),l(_,null,ee(ne.value,m=>(t(),E(s(_e),{as:"template",key:m.id,value:m},{default:c(({active:I})=>{var p,R,q;return[e("li",{class:A(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":I,"text-gray-900":!I}])},[e("span",{class:A(["block truncate",{"font-medium":((p=b.value)==null?void 0:p.id)==m.id,"font-normal":((R=b.value)==null?void 0:R.id)!=m.id}])},x(m==null?void 0:m.name),3),((q=b.value)==null?void 0:q.id)==m.id?(t(),l("span",{key:0,class:A(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":I,"text-main-color-600":!I}])},[o(s(ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):y("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])]),e("div",St,[u[24]||(u[24]=e("label",{for:"item-description",class:"block text-sm font-normal leading-6 text-gray-900"},"Poznámka:",-1)),e("div",Ut,[o(s(F),{modelValue:w.value,"onUpdate:modelValue":u[11]||(u[11]=m=>w.value=m),as:"textarea",name:"item-description",id:"item-description",cols:"30",rows:"3",class:"resize-none block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte poznámku..."},null,8,["modelValue"]),o(s(X),{name:"item-description",class:"text-rose-400 text-sm block pt-1"})])]),S.value.length?(t(!0),l(_,{key:0},ee(S.value,(m,I)=>(t(),l("div",{key:m.id},[m.enable?(t(),l("div",jt,[e("label",{for:"custom_column"+I,class:"block text-sm font-normal leading-6 text-gray-900"},x(m.custom_name||m.name)+": ",9,Rt),e("div",Nt,[o(s(F),{modelValue:S.value[I].value,"onUpdate:modelValue":p=>S.value[I].value=p,type:"text",name:"custom_column"+I,id:"custom_column"+I,class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte "+(m.custom_name?m.custom_name:m.name)+"..."},null,8,["modelValue","onUpdate:modelValue","name","id","placeholder"]),o(s(X),{name:"custom_column"+I,class:"text-rose-400 text-sm block pt-1"},null,8,["name"])])])):y("",!0)]))),128)):y("",!0)]),e("div",Ot,[e("div",Tt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:u[12]||(u[12]=D(m=>L(),["prevent"]))}," Zavřít "),u[25]||(u[25]=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"}," Vytvořit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"])}}},Yt={class:"border-t p-5"},Lt={class:"text-right space-x-3"},Zt={__name:"discardItemModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadItems"],setup(oe,{expose:te,emit:le}){const Q=oe,G=le,O=d(!1);me("debugModeGlobalVar");const v=d(null),z=d(""),j=d(!1);Se(()=>{});function V(){O.value=!1}function T(){O.value=!0,v.value="",z.value=null}async function w(){P.check("items.discard")||P.check("property.master")?(z.value=parseInt(z.value),await K.post("api/items/"+Q.selectedItem.id+"/discard").then(n=>{Ve.success(n.data.message)}).catch(n=>{console.log(n)})):j.value=!1,V(),G("reloadItems",!0)}return te({openModal:T}),(n,g)=>(t(),E(s(re),{appear:"",show:O.value,as:"template",onClose:g[3]||(g[3]=b=>V())},{default:c(()=>[o($e,{size:"sm"},{"modal-title":c(()=>g[4]||(g[4]=[N("Vyřadit položku")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:g[0]||(g[0]=b=>V())},[o(s(H),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[g[5]||(g[5]=e("div",{class:"p-6 gap-4"},[e("p",null,"Opravdu si přejete položku vyřadit?")],-1)),e("div",Yt,[e("div",Lt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:g[1]||(g[1]=D(b=>V(),["prevent"]))}," Zavřít "),e("button",{onClick:g[2]||(g[2]=D(b=>w(),["prevent"])),class:"rounded-md bg-orange-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-orange-600",type:"submit"}," Vyřadit ")])])]),_:1})]),_:1},8,["show"]))}},At={class:"border-t p-5"},Et={class:"text-right space-x-3"},Gt={__name:"deleteItemModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadItems"],setup(oe,{expose:te,emit:le}){const Q=oe,G=le,O=d(!1);me("debugModeGlobalVar");const v=d(null),z=d(""),j=d(!1);Se(()=>{});function V(){O.value=!1}function T(){O.value=!0,v.value="",z.value=null}async function w(){P.check("items.delete")||P.check("property.master")?(z.value=parseInt(z.value),await K.post("api/items/"+Q.selectedItem.id+"/delete").then(n=>{Ve.success(n.data.message)}).catch(n=>{console.log(n)})):j.value=!1,V(),G("reloadItems",!0)}return te({openModal:T}),(n,g)=>(t(),E(s(re),{appear:"",show:O.value,as:"template",onClose:g[3]||(g[3]=b=>V())},{default:c(()=>[o($e,{size:"sm"},{"modal-title":c(()=>g[4]||(g[4]=[N("Smazat položku")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:g[0]||(g[0]=b=>V())},[o(s(H),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[g[5]||(g[5]=e("div",{class:"p-6 gap-4"},[e("p",null,"Opravdu si přejete položku smazat?")],-1)),e("div",At,[e("div",Et,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:g[1]||(g[1]=D(b=>V(),["prevent"]))}," Zavřít "),e("button",{onClick:g[2]||(g[2]=D(b=>w(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}},Bt={class:"p-6 grid grid-cols-2 gap-4"},Ft={class:"mt-2"},qt={class:"mt-2"},Kt={class:"mt-2"},Ht={class:"mt-2"},Qt={class:"rounded-l-full"},Jt={class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},Xt={class:"flex items-center w-full"},Wt={class:"w-6 h-6"},ea={class:"flex-1"},ta={key:0,class:"text-gray-900"},aa={key:1,class:"text-gray-400"},la={class:"mt-2"},sa={class:"relative"},oa={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},na={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},ia={key:0},ra={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},da={key:1,class:"h-20 max-h-20 overflow-hidden"},ua={key:2},ma={class:"col-span-2"},ca={class:"mt-2"},va={key:0,class:"col-span-2"},pa=["for"],ga={class:"mt-2"},fa={class:"border-t p-5"},ya={class:"text-right space-x-3"},ba={__name:"editItemModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadItems"],setup(oe,{expose:te,emit:le}){const Q=le,G=oe,O=d({date:"YYYY-MM-DD",month:"MM"}),v=d(!1);me("debugModeGlobalVar");const z=De(),j=d(null),V=d([]),T=d(""),w=d(""),n=d(""),g=d(""),b=d(""),Y=d(""),ie=d(!1),S=d(null),M=d(null),C=d(""),ne=d(!1),L=fe(()=>C.value===""?S.value:S.value.filter(h=>h.name.toLowerCase().replace(/\s+/g,"").includes(C.value.toLowerCase().replace(/\s+/g,""))));ye(j,h=>{h&&(T.value=h.name,w.value=h.invoice_number,n.value=h.buyed_at||"",b.value=h.price,g.value=h.purchased_from,Y.value=h.description,h.accounting_category&&(M.value=S.value.find(r=>r.id===h.accounting_category.id)),z.custom_columns&&z.custom_columns.length&&(V.value=z.custom_columns.filter(r=>r.enable===1).map(r=>({name:r.name,custom_name:r.custom_name,value:h[r.name]||"",enable:r.enable}))))});function J(){v.value=!1}async function de(h){v.value=!0,M.value="",await u(),await ce(h)}async function ce(h){ie.value=!0;try{const r=await K.get(`api/items/${h.id}`);j.value=r.data.data}catch(r){console.error(r)}finally{ie.value=!1}}async function $(){var h;if(P.check("items.edit")||P.check("property.master")){const r={name:T.value,price:b.value,invoice_number:w.value,buyed_at:n.value,description:Y.value,purchased_from:g.value,accounting_category_id:((h=M==null?void 0:M.value)==null?void 0:h.id)||null};V.value.forEach(m=>{m.enable&&m.value&&(r[m.name]=m.value)});try{await K.post(`api/items/${G.selectedItem.id}/update`,r),Ve.success("Položka byla úspěšně aktualizována"),Q("reloadItems",!0)}catch(m){console.error(m)}}J()}async function u(){await K.get("/api/accounting-categories?page=1&perpage=99999").then(h=>{S.value=h.data.data}).catch(h=>{console.log(h)})}return te({openModal:de}),(h,r)=>{const m=ze("VueSpinner");return t(),E(s(re),{appear:"",show:v.value,as:"template",onClose:r[13]||(r[13]=I=>J())},{default:c(()=>[o($e,{size:"md"},{"modal-title":c(()=>r[14]||(r[14]=[N("Úprava položky")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=I=>J())},[o(s(H),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[o(s(Ne),{onSubmit:r[12]||(r[12]=I=>$())},{default:c(({values:I})=>[e("div",Bt,[e("div",null,[r[15]||(r[15]=e("label",{for:"item-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název položky:",-1)),e("div",Ft,[o(s(F),{rules:"required",modelValue:T.value,"onUpdate:modelValue":r[1]||(r[1]=p=>T.value=p),type:"text",name:"item-name",id:"item-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název skupiny..."},null,8,["modelValue"]),o(s(X),{name:"item-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[r[16]||(r[16]=e("label",{for:"item-price",class:"block text-sm font-normal leading-6 text-gray-900"},"Cena za kus:",-1)),e("div",qt,[o(s(F),{rules:"minMax:1,9999999999",modelValue:b.value,"onUpdate:modelValue":r[2]||(r[2]=p=>b.value=p),type:"number",name:"item-price",id:"item-price",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cenu za kus..."},null,8,["modelValue"]),o(s(X),{name:"item-price",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[r[17]||(r[17]=e("label",{for:"item-invoice",class:"block text-sm font-normal leading-6 text-gray-900"},"Číslo faktury:",-1)),e("div",Kt,[o(s(F),{modelValue:w.value,"onUpdate:modelValue":r[3]||(r[3]=p=>w.value=p),type:"text",name:"item-invoice",id:"item-invoice",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte číslo faktury..."},null,8,["modelValue"]),o(s(X),{name:"item-invoice",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[r[19]||(r[19]=e("label",{for:"item-buyed-at",class:"block text-sm font-normal leading-6 text-gray-900"},"Datum zakoupení:",-1)),e("div",Ht,[o(s(F),{name:"blockedTimetableDate"},{default:c(({handleChange:p,value:R})=>[o(s(Ye),{name:"blockedTimetableDate",i18n:"cs","as-single":"",shortcuts:!1,modelValue:n.value,"onUpdate:modelValue":[r[4]||(r[4]=q=>n.value=q),p],formatter:O.value,placeholder:"Zvolte datum zakoupení..."},{default:c(({clear:q})=>[e("div",null,[e("div",Qt,[e("button",Jt,[e("div",Xt,[e("div",Wt,[n.value?(t(),E(s(H),{key:0,onClick:q,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(t(),E(s(Oe),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",ea,[n.value?(t(),l("span",ta,[e("span",null,x(s(we)(n.value).format("DD.MM.YYYY")),1)])):(t(),l("span",aa,r[18]||(r[18]=[e("span",null,"Zvolte datum zakoupení...",-1)])))])])])])])]),_:2},1032,["modelValue","onUpdate:modelValue","formatter"])]),_:1})])]),e("div",null,[r[20]||(r[20]=e("label",{for:"item-buyed-place",class:"block text-sm font-normal leading-6 text-gray-900"},"Pořízeno z:",-1)),e("div",la,[o(s(F),{modelValue:g.value,"onUpdate:modelValue":r[5]||(r[5]=p=>g.value=p),type:"text",name:"item-buyed-place",id:"item-buyed-place",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte pořízeno z..."},null,8,["modelValue"]),o(s(X),{name:"item-buyed-place",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[r[21]||(r[21]=e("label",{for:"item-account-category",class:"block text-sm font-normal leading-6 text-gray-900 mb-2"},"Účetní druh majetku:",-1)),o(s(be),{modelValue:M.value,"onUpdate:modelValue":r[9]||(r[9]=p=>M.value=p)},{default:c(()=>[e("div",sa,[e("div",oa,[o(s(xe),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte druh majetku...",displayValue:p=>p?`${p.name}`.trim():"",onChange:r[6]||(r[6]=p=>C.value=p.target.value)},null,8,["displayValue"]),M.value&&M.value.name?(t(),l("div",na,[e("button",{onClick:r[7]||(r[7]=D(p=>M.value=null,["prevent"])),type:"button"},[o(s(H),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0),o(s(he),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:c(()=>[o(s(Te),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),o(s(re),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:r[8]||(r[8]=p=>C.value="")},{default:c(()=>[o(s(ke),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:c(()=>[ne.value?y("",!0):(t(),l("div",ia,[C.value.length===0&&C.value!==""?(t(),l("div",ra," Žádný druh majetku nenalezen. ")):y("",!0)])),ne.value?(t(),l("div",da,[o(m,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(t(),l("div",ua,[(t(!0),l(_,null,ee(L.value,p=>(t(),E(s(_e),{as:"template",key:p.id,value:p},{default:c(({active:R})=>{var q,ve,pe;return[e("li",{class:A(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":R,"text-gray-900":!R}])},[e("span",{class:A(["block truncate",{"font-medium":((q=M.value)==null?void 0:q.id)==p.id,"font-normal":((ve=M.value)==null?void 0:ve.id)!=p.id}])},x(p==null?void 0:p.name),3),((pe=M.value)==null?void 0:pe.id)==p.id?(t(),l("span",{key:0,class:A(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":R,"text-main-color-600":!R}])},[o(s(ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):y("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])]),e("div",ma,[r[22]||(r[22]=e("label",{for:"item-description",class:"block text-sm font-normal leading-6 text-gray-900"},"Poznámka:",-1)),e("div",ca,[o(s(F),{modelValue:Y.value,"onUpdate:modelValue":r[10]||(r[10]=p=>Y.value=p),as:"textarea",name:"item-description",id:"item-description",cols:"30",rows:"3",class:"resize-none block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte poznámku..."},null,8,["modelValue"]),o(s(X),{name:"item-description",class:"text-rose-400 text-sm block pt-1"})])]),V.value.length?(t(!0),l(_,{key:0},ee(V.value,(p,R)=>(t(),l("div",{key:p.id},[p.enable?(t(),l("div",va,[e("label",{for:"custom_column"+R,class:"block text-sm font-normal leading-6 text-gray-900"},x(p.custom_name||p.name)+": ",9,pa),e("div",ga,[o(s(F),{modelValue:V.value[R].value,"onUpdate:modelValue":q=>V.value[R].value=q,type:"text",name:"custom_column"+R,id:"custom_column"+R,class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte "+(p.custom_name?p.custom_name:p.name)+"..."},null,8,["modelValue","onUpdate:modelValue","name","id","placeholder"]),o(s(X),{name:"custom_column"+R,class:"text-rose-400 text-sm block pt-1"},null,8,["name"])])])):y("",!0)]))),128)):y("",!0)]),e("div",fa,[e("div",ya,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[11]||(r[11]=D(p=>J(),["prevent"]))}," Zavřít "),r[23]||(r[23]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Upravit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"])}}},xa={key:0},ha={class:"p-6"},ka={class:"grid grid-cols-3 gap-x-5 gap-y-4"},_a={key:0},wa={key:1,class:"text-red-600"},$a={key:0,class:"text-sm"},Ca={key:1,class:"text-red-600"},za={key:0,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},Va={class:"flex"},Ia={key:1,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Ma={key:2,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Da={key:3,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},Sa={class:"flex"},Ua={key:4},ja={class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},Ra={class:"flex"},Na={class:"text-xs mt-1"},Oa={key:0,class:"text-sm"},Ta={key:1},Pa={key:0,class:"text-sm"},Ya={key:1},La={key:0,class:"text-sm"},Za={key:1},Aa={class:"col-span-3"},Ea={key:0},Ga={key:1},Ba={key:0,class:"mt-6 space-y-6"},Fa={key:0},qa={class:"flex items-center gap-2"},Ka={class:"mt-4 flex gap-10"},Ha={key:0},Qa={key:1},Ja={key:0},Xa={key:1},Wa={key:1},el={class:"flex items-center gap-2"},tl={class:"mt-4 flex gap-10"},al={key:0},ll={key:1},sl={key:0},ol={key:1},nl={key:0},il={key:0},rl={key:1},dl={key:1,class:"pt-6 grid grid-cols-3 gap-4"},ul={key:0,class:"col-span-2"},ml={class:"block text-xs text-gray-400"},cl={class:"text-sm"},vl={class:"border-t p-5"},pl={class:"text-right space-x-3"},gl={__name:"itemDetailModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadGroups"],setup(oe,{expose:te,emit:le}){const Q=De(),G=d(!1);me("debugModeGlobalVar");const O=d(!1),v=d(null),z=d([]);ye(v,w=>{w&&Q.custom_columns&&Q.custom_columns.length&&(z.value=Q.custom_columns.filter(n=>n.enable===1).map(n=>({name:n.name,custom_name:n.custom_name,value:w[n.name]||"",enable:n.enable})))});function j(){G.value=!1}async function V(w){G.value=!0,await T(w)}async function T(w){O.value=!0;try{const n=await K.get(`api/items/${w.id}`);v.value=n.data.data}catch(n){console.error(n)}finally{O.value=!1}}return te({openModal:V}),(w,n)=>(t(),E(s(re),{appear:"",show:G.value,as:"template",onClose:n[2]||(n[2]=g=>j())},{default:c(()=>[o($e,{size:"xs"},{"modal-title":c(()=>n[3]||(n[3]=[N("Detail položky")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:n[0]||(n[0]=g=>j())},[o(s(H),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>{var g;return[v.value?(t(),l("div",xa,[e("div",ha,[e("div",ka,[e("div",null,[n[4]||(n[4]=e("span",{class:"block text-xs text-gray-400"},"Evidenční číslo:",-1)),v.value.evidence_number?(t(),l("span",_a,x(v.value.evidence_number),1)):(t(),l("span",wa,"CHYBÍ"))]),e("div",null,[n[5]||(n[5]=e("span",{class:"block text-xs text-gray-400"},"Název položky:",-1)),v.value.name?(t(),l("span",$a,x(v.value.name),1)):(t(),l("span",Ca,"CHYBÍ"))]),e("div",null,[n[11]||(n[11]=e("span",{class:"block text-xs text-gray-400 pb-1"},"Stav:",-1)),v.value.state=="NOT_ASSIGNED"?(t(),l("div",za,[e("div",Va,[o(s(ue),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),n[6]||(n[6]=e("span",null,"Nepřiřazeno",-1))])])):v.value.state=="ASSIGNED_TO_USER"?(t(),l("div",Ia,n[7]||(n[7]=[e("div",null,"Přiřazeno uživateli",-1)]))):v.value.state=="ASSIGNED_TO_ROOM"?(t(),l("div",Ma,n[8]||(n[8]=[e("div",null,"Přiřazeno místnosti",-1)]))):v.value.state=="LOCKED"?(t(),l("div",Da,[e("div",Sa,[o(s(Je),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),n[9]||(n[9]=e("span",null,"Uzamčeno",-1))])])):v.value.state=="DISCARDED"?(t(),l("div",Ua,[e("div",ja,[e("div",Ra,[o(s(Pe),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),n[10]||(n[10]=e("span",null,"Vyřazeno",-1))]),e("p",Na,x(s(we)(v.value.buyed_at).format("DD. MM. YYYY - hh:mm")),1)])])):y("",!0)]),e("div",null,[n[12]||(n[12]=e("span",{class:"block text-xs text-gray-400"},"Cena za ks:",-1)),v.value.evidence_number?(t(),l("span",Oa,x(v.value.price),1)):(t(),l("span",Ta,"-"))]),e("div",null,[n[13]||(n[13]=e("span",{class:"block text-xs text-gray-400"},"Číslo faktury:",-1)),v.value.evidence_number?(t(),l("span",Pa,x(v.value.invoice_number),1)):(t(),l("span",Ya,"-"))]),e("div",null,[n[14]||(n[14]=e("span",{class:"block text-xs text-gray-400"},"Datum zakoupení:",-1)),v.value.buyed_at?(t(),l("span",La,x(s(we)(v.value.buyed_at).format("DD. MM. YYYY")),1)):(t(),l("span",Za,"-"))]),e("div",Aa,[n[15]||(n[15]=e("span",{class:"block text-xs text-gray-400"},"Účetní druh majetku:",-1)),v.value.accounting_category?(t(),l("span",Ea,x((g=v.value.accounting_category)==null?void 0:g.name),1)):(t(),l("span",Ga,"-"))])]),v.value.user||v.value.room?(t(),l("div",Ba,[v.value.room?(t(),l("div",Fa,[e("div",qa,[o(s(Xe),{class:"h-6 w-6","aria-hidden":"true"}),n[16]||(n[16]=e("span",null,"Informace o přiřazené místnosti",-1))]),e("div",Ka,[e("div",null,[n[17]||(n[17]=e("span",{class:"block text-xs text-gray-400"},"Kód místnosti:",-1)),v.value.room.code?(t(),l("span",Ha,x(v.value.room.code),1)):(t(),l("span",Qa,"-"))]),e("div",null,[n[18]||(n[18]=e("span",{class:"block text-xs text-gray-400"},"Název místnosti:",-1)),v.value.room.name?(t(),l("span",Ja,x(v.value.room.name),1)):(t(),l("span",Xa,"-"))])])])):y("",!0),v.value.user?(t(),l("div",Wa,[e("div",el,[o(s(We),{class:"h-6 w-6","aria-hidden":"true"}),n[19]||(n[19]=e("span",null,"Informace o přiřazeném uživateli",-1))]),e("div",tl,[e("div",null,[n[20]||(n[20]=e("span",{class:"block text-xs text-gray-400"},"Jméno uživatele:",-1)),v.value.user.full_name?(t(),l("span",al,x(v.value.user.full_name),1)):(t(),l("span",ll,"-"))]),e("div",null,[n[21]||(n[21]=e("span",{class:"block text-xs text-gray-400"},"E-mail uživatele:",-1)),v.value.user.email?(t(),l("span",sl,x(v.value.user.email),1)):(t(),l("span",ol,"-"))]),v.value.user.organization_unit?(t(),l("div",nl,[n[22]||(n[22]=e("span",{class:"block text-xs text-gray-400"},"Organizace:",-1)),v.value.user.organization_unit.name?(t(),l("span",il,x(v.value.user.organization_unit.name),1)):(t(),l("span",rl,"-"))])):y("",!0)])])):y("",!0)])):y("",!0),z.value.length?(t(),l("div",dl,[(t(!0),l(_,null,ee(z.value,(b,Y)=>(t(),l("div",{key:b.id},[b.enable?(t(),l("div",ul,[e("span",ml,x((b==null?void 0:b.custom_name)||b.name)+":",1),e("p",cl,x(z.value[Y].value||"-"),1)])):y("",!0)]))),128))])):y("",!0)]),e("div",vl,[e("div",pl,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:n[1]||(n[1]=D(b=>j(),["prevent"]))}," Zavřít ")])])])):y("",!0)]}),_:1})]),_:1},8,["show"]))}},fl={class:"space-y-6"},yl={class:"px-0"},bl={class:"bg-white border border-zinc-200/70 rounded-md p-5"},xl={class:"sm:flex justify-between items-center gap-x-20 gap-y-4"},hl={class:"grid grid-cols-6 grow gap-4"},kl={class:"col-span-6"},_l={class:"col-span-2"},wl={class:"w-full text-left"},$l={key:0,class:"text-gray-900"},Cl={key:1,class:"text-gray-400"},zl={key:0,class:"flex ml-4"},Vl=["onClick"],Il={class:"col-span-2"},Ml={class:"relative"},Dl={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},Sl={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},Ul={key:0},jl={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Rl={key:1,class:"h-20 max-h-20 overflow-hidden"},Nl={key:2},Ol={class:"col-span-2"},Tl={class:"relative"},Pl={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},Yl={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},Ll={key:0},Zl={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Al={key:1,class:"h-20 max-h-20 overflow-hidden"},El={key:2},Gl={class:"col-span-3"},Bl={class:"relative"},Fl={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},ql={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},Kl={key:0},Hl={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Ql={key:1,class:"h-20 max-h-20 overflow-hidden"},Jl={key:2},Xl={class:"flex items-center gap-4"},Wl={class:"flow-root bg-white border border-zinc-200/70 rounded-md overflow-hidden"},es={class:"sm:-mx-6 lg:-mx-8"},ts={class:"inline-block overflow-x-auto w-full align-middle sm:px-6 lg:px-8"},as={key:0,class:"min-w-full divide-y divide-gray-200"},ls={key:0,class:"pl-2"},ss=["checked"],os={key:9,class:"pr-2"},ns={key:0,class:"divide-y divide-gray-200"},is={key:0,class:"flex items-center gap-3 pl-2"},rs=["value"],ds=["onClick"],us={key:0},ms={key:0,class:"text-green-600"},cs={key:1,class:"text-red-600"},vs={key:2,class:"text-red-600"},ps={key:3,class:"text-amber-600"},gs={key:4,class:"text-amber-600"},fs={key:9,class:"flex items-center justify-end pr-2"},ys=["onClick"],bs=["onClick"],xs=["onClick"],hs=["onClick"],ks={key:0},_s={key:1},ws={key:2,class:"bg-gray-100/70"},$s={colspan:"10",class:"py-4 pl-5 px-3 text-sm text-gray-900 bg-gray-100/70"},Cs={class:"flex items-center gap-2"},Fs={__name:"Property",setup(oe){const te=d(),le=d(),Q=d(),G=d(),O=d(),v=d(),z=Be();me("debugModeGlobalVar");const j=d(["property"]),T=d(Object.entries({barcodes:{active:!0,position:1,locked:!0,custom_name:"Čárový kód"},evidence_number:{active:!0,position:2,locked:!0,custom_name:"Ev. číslo"},invoice_number:{active:!0,position:3,locked:!1,custom_name:"Číslo faktury"},name:{active:!0,position:4,locked:!1,custom_name:"Název položky"},price:{active:!0,position:5,locked:!1,custom_name:"Cena za kus"},buyed_at:{active:!0,position:6,locked:!1,custom_name:"Datum zakoupení"},room:{active:!0,position:7,locked:!1,custom_name:"Přiřazená třída"},user:{active:!0,position:8,locked:!1,custom_name:"Přiřazený uživatel"},state:{active:!0,position:9,locked:!0,custom_name:"Stav"},actions:{active:!0,position:10,locked:!0,custom_name:"Akce"}}).map(([f,i])=>({name:f,...i}))),w=d(!1),n=d(!1),g=d(""),b=d(1),Y=d({}),ie=d([{id:"NOT_ASSIGNED",name:"Nepřiřazeno"},{id:"ASSIGNED_TO_ROOM",name:"Přiřazeno místnosti"},{id:"ASSIGNED_TO_USER",name:"Přiřazeno uživateli"},{id:"LOCKED",name:"Uzamčeno"},{id:"DISCARDED",name:"Vyřazeno"}]),S=d({id:"",name:""}),M=d({}),C=d([]);d(),Se(async()=>{n.value=!0,await pe(),await Ue(),await se(),await Le(),await Ae(),n.value=!1});const ne=d(null),L=d(null),J=d(""),de=d(!1),ce=fe(()=>J.value===""?ne.value:ne.value.filter(f=>f.name.toLowerCase().replace(/\s+/g,"").includes(J.value.toLowerCase().replace(/\s+/g,"")))),$=d(null),u=d(null),h=d([]),r=d([]),m=d(""),I=d(""),p=d(!1),R=d(!1),q=fe(()=>m.value===""?h.value:h.value.filter(f=>`${f.first_name} ${f.last_name}`.toLowerCase().replace(/\s+/g,"").includes(m.value.toLowerCase().replace(/\s+/g,"")))),ve=fe(()=>I.value===""?r.value:r.value.filter(f=>f.name.toLowerCase().replace(/\s+/g,"").includes(I.value.toLowerCase().replace(/\s+/g,"")))),pe=Re(async()=>{try{const f=await K.get("/api/users?page=1&perpage=50&search="+m.value+"&with_deleted=1");h.value=f.data.data}catch(f){console.error(f)}finally{p.value=!1}},300),Ue=Re(async()=>{try{const f=await K.get("/api/rooms?page=1&perpage=50&search="+I.value);r.value=f.data.data}catch(f){console.error(f)}finally{R.value=!1}},300);ye(m,()=>{p.value=!0,pe()}),ye(I,()=>{R.value=!0,Ue()});async function Le(){await K.get("/api/accounting-categories?page=1&perpage=99999").then(f=>{ne.value=f.data.data}).catch(f=>{console.log(f)})}ye(()=>z.perPage,(f,i)=>{w.value=!0,b.value=1,se()});async function se(){var f,i,Z,W;w.value=!0,P.check("items.read")?await K.get("api/items?page="+b.value+"&perpage="+z.perPage+"&search="+g.value+"&state="+(((f=S.value)==null?void 0:f.id)||"")+"&user_id="+(((i=$.value)==null?void 0:i.id)||"")+"&room_id="+(((Z=$.value)==null?void 0:Z.id)||"")+"&accounting_category_id="+(((W=L.value)==null?void 0:W.id)||"")).then(a=>{M.value=a.data.data,Y.value=a.data.meta,w.value=!1}).catch(a=>{console.log(a)}):w.value=!1}function Ce(f){v.value=f}function Ze(){C.value.length==M.value.length?C.value=[]:C.value=M.value.map(f=>f.id)}function Ie(f,i){console.log(C.value.length),K.post("api/items/barcodes",{items:C.value},{responseType:"blob"}).then(Z=>{const W=new Blob([Z.data],{type:"application/pdf"}),a=window.URL.createObjectURL(W);let k=i&&C.value.length===1?`${i}.pdf`:"Štítky.pdf";if(f==="download"){const U=document.createElement("a");U.href=a,U.setAttribute("download",k),document.body.appendChild(U),U.click(),document.body.removeChild(U)}else if(f==="open"){const U=window.open();U.document.write(`
                <html>
                    <head>
                        <title>Tisk štítků</title>
                    </head>
                    <body style="margin:0;">
                        <iframe src="${a}" style="width:100vw; height:100vh; border:none;"></iframe>
                    </body>
                </html>
            `),U.document.close()}setTimeout(()=>window.URL.revokeObjectURL(a),1e3),C.value=[]}).catch(Z=>{console.error("Chyba při získávání souboru:",Z)})}async function Ae(){await K.get("/api/settings/custom-table-settings/property-list").then(f=>{let i=JSON.parse(f.data.data);i=Object.entries(i).map(([Z,W])=>({name:Z,...W})).filter(Z=>Z.active).sort((Z,W)=>Z.position-W.position),T.value=i}).catch(f=>{console.log(f)})}function Ee(f){b.value=f,se()}function Ge(){w.value=!0,b.value=1,g.value="",S.value={id:"",name:""},$.value="",u.value="",C.value=[],L.value="",se()}function ge(){w.value=!0,b.value=1,C.value=[],se()}return(f,i)=>{const Z=ze("VueSpinner"),W=ze("TransitionRoot");return t(),l(_,null,[o(Qe,{breadCrumbs:j.value},{topbarButtons:c(()=>[s(P).check("items.create")||s(P).check("property.master")?(t(),l("button",{key:0,onClick:i[0]||(i[0]=D(a=>f.$refs.createItemRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nová položka ")):y("",!0)]),_:1},8,["breadCrumbs"]),e("div",fl,[e("div",yl,[e("div",bl,[e("div",xl,[e("div",hl,[e("div",kl,[je(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":i[1]||(i[1]=a=>g.value=a),onKeyup:i[2]||(i[2]=qe(a=>ge(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[Fe,g.value]])]),e("div",_l,[o(s(rt),{as:"div",class:"relative inline-block text-left w-full"},{default:c(()=>[e("div",null,[o(s(ot),{class:"inline-flex w-full justify-center items-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:c(()=>[e("div",wl,[S.value&&S.value.name?(t(),l("span",$l,x(S.value.name),1)):(t(),l("span",Cl,"Stav položky..."))]),o(s(lt),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"}),S.value&&S.value.name?(t(),l("div",zl,[e("button",{onClick:i[3]||(i[3]=D(a=>(S.value={id:"",name:""},ge()),["prevent"]))},[o(s(H),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0)]),_:1})]),o(Ke,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:c(()=>[o(s(nt),{class:"absolute z-10 right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:c(()=>[(t(!0),l(_,null,ee(ie.value,a=>(t(),l("div",{key:a.id,class:"px-1 py-1"},[o(s(it),null,{default:c(({active:k})=>[e("button",{onClick:U=>S.value=a,class:A([k?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},x(a.name),11,Vl)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),e("div",Il,[o(s(be),{modelValue:$.value,"onUpdate:modelValue":i[7]||(i[7]=a=>$.value=a)},{default:c(()=>[e("div",Ml,[e("div",Dl,[o(s(xe),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte uživatele...",displayValue:a=>a?`${a.first_name||""} ${a.middle_name?a.middle_name+" ":""}${a.last_name||""}`.trim():"",onChange:i[4]||(i[4]=a=>m.value=a.target.value)},null,8,["displayValue"]),$.value&&$.value.first_name?(t(),l("div",Sl,[e("button",{onClick:i[5]||(i[5]=D(a=>($.value=null,ge()),["prevent"]))},[o(s(H),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0),o(s(he),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:c(()=>[o(s(Me),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),o(W,{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:i[6]||(i[6]=a=>m.value="")},{default:c(()=>[o(s(ke),{class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:c(()=>[p.value?y("",!0):(t(),l("div",Ul,[q.value.length===0&&m.value!==""?(t(),l("div",jl," Žádný uživatel nenalezen. ")):y("",!0)])),p.value?(t(),l("div",Rl,[o(Z,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(t(),l("div",Nl,[(t(!0),l(_,null,ee(q.value,a=>(t(),E(s(_e),{as:"template",key:a.id,value:a},{default:c(({active:k})=>{var U,ae,B;return[e("li",{class:A(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":k,"text-gray-900":!k}])},[e("span",{class:A(["block truncate",{"font-medium":((U=$.value)==null?void 0:U.id)==a.id,"font-normal":((ae=$.value)==null?void 0:ae.id)!=a.id}])},x((a==null?void 0:a.first_name)+" "+(a!=null&&a.middle_name?(a==null?void 0:a.middle_name)+" ":"")+(a==null?void 0:a.last_name)),3),((B=$.value)==null?void 0:B.id)==a.id?(t(),l("span",{key:0,class:A(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":k,"text-main-color-600":!k}])},[o(s(ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):y("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])]),e("div",Ol,[o(s(be),{modelValue:u.value,"onUpdate:modelValue":i[11]||(i[11]=a=>u.value=a)},{default:c(()=>[e("div",Tl,[e("div",Pl,[o(s(xe),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte místnost...",displayValue:a=>a?`${a.name}`.trim():"",onChange:i[8]||(i[8]=a=>I.value=a.target.value)},null,8,["displayValue"]),u.value&&u.value.name?(t(),l("div",Yl,[e("button",{onClick:i[9]||(i[9]=D(a=>(u.value=null,ge()),["prevent"]))},[o(s(H),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0),o(s(he),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:c(()=>[o(s(Me),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),o(W,{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:i[10]||(i[10]=a=>I.value="")},{default:c(()=>[o(s(ke),{class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:c(()=>[R.value?y("",!0):(t(),l("div",Ll,[ve.value.length===0&&I.value!==""?(t(),l("div",Zl," Žádná místnost nenalezena. ")):y("",!0)])),R.value?(t(),l("div",Al,[o(Z,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(t(),l("div",El,[(t(!0),l(_,null,ee(ve.value,a=>(t(),E(s(_e),{as:"template",key:a.id,value:a},{default:c(({active:k})=>{var U,ae,B;return[e("li",{class:A(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":k,"text-gray-900":!k}])},[e("span",{class:A(["block truncate",{"font-medium":((U=u.value)==null?void 0:U.id)==a.id,"font-normal":((ae=u.value)==null?void 0:ae.id)!=a.id}])},x(a==null?void 0:a.name),3),((B=u.value)==null?void 0:B.id)==a.id?(t(),l("span",{key:0,class:A(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":k,"text-main-color-600":!k}])},[o(s(ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):y("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])]),e("div",Gl,[o(s(be),{modelValue:L.value,"onUpdate:modelValue":i[15]||(i[15]=a=>L.value=a)},{default:c(()=>[e("div",Bl,[e("div",Fl,[o(s(xe),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte druh majetku...",displayValue:a=>a?`${a.name}`.trim():"",onChange:i[12]||(i[12]=a=>J.value=a.target.value)},null,8,["displayValue"]),L.value&&L.value.name?(t(),l("div",ql,[e("button",{onClick:i[13]||(i[13]=D(a=>L.value=null,["prevent"])),type:"button"},[o(s(H),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0),o(s(he),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:c(()=>[o(s(Me),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),o(W,{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:i[14]||(i[14]=a=>J.value="")},{default:c(()=>[o(s(ke),{class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:c(()=>[de.value?y("",!0):(t(),l("div",Kl,[J.value.length===0&&J.value!==""?(t(),l("div",Hl," Žádný druh majetku nenalezen. ")):y("",!0)])),de.value?(t(),l("div",Ql,[o(Z,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(t(),l("div",Jl,[(t(!0),l(_,null,ee(ce.value,a=>(t(),E(s(_e),{as:"template",key:a.id,value:a},{default:c(({active:k})=>{var U,ae,B;return[e("li",{class:A(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":k,"text-gray-900":!k}])},[e("span",{class:A(["block truncate",{"font-medium":((U=L.value)==null?void 0:U.id)==a.id,"font-normal":((ae=L.value)==null?void 0:ae.id)!=a.id}])},x(a==null?void 0:a.name),3),((B=L.value)==null?void 0:B.id)==a.id?(t(),l("span",{key:0,class:A(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":k,"text-main-color-600":!k}])},[o(s(ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):y("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])]),e("div",Xl,[e("button",{onClick:i[16]||(i[16]=a=>Ge()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},i[28]||(i[28]=[e("span",null,"Resetovat",-1)])),e("button",{onClick:i[17]||(i[17]=a=>ge()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",Wl,[e("div",es,[e("div",ts,[w.value==!1&&n.value==!1?(t(),l("table",as,[e("thead",null,[e("tr",null,[(t(!0),l(_,null,ee(T.value,a=>(t(),l("th",{key:a,class:"py-4 px-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},[a.name==="barcodes"?(t(),l("div",ls,[e("input",{checked:C.value.length==M.value.length,onClick:Ze,"aria-describedby":"comments-description",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-main-color-600 ring-transparent focus:ring-transparent cursor-pointer"},null,8,ss)])):a.name==="evidence_number"?(t(),l(_,{key:1},[N(" Ev. číslo ")],64)):a.name==="invoice_number"?(t(),l(_,{key:2},[N("Číslo faktury")],64)):a.name==="name"?(t(),l(_,{key:3},[N("Název položky")],64)):a.name==="price"?(t(),l(_,{key:4},[N("Cena za kus")],64)):a.name==="buyed_at"?(t(),l(_,{key:5},[N("Datum zakoupení")],64)):a.name==="room"?(t(),l(_,{key:6},[N("Přiřazená třída")],64)):a.name==="user"?(t(),l(_,{key:7},[N("Přiřazený uživatel")],64)):a.name==="state"?(t(),l(_,{key:8},[N("Stav")],64)):a.name==="actions"?(t(),l("div",os)):(t(),l(_,{key:10},[N(x(a.custom_name||a.name),1)],64))]))),128))])]),M.value&&M.value.length?(t(),l("tbody",ns,[(t(!0),l(_,null,ee(M.value,a=>(t(),l("tr",{key:a.id},[(t(!0),l(_,null,ee(T.value,k=>{var U,ae;return t(),l("td",{key:k,class:"py-4 px-3 text-sm text-gray-600"},[k.name==="barcodes"?(t(),l("div",is,[je(e("input",{value:a.id,"onUpdate:modelValue":i[18]||(i[18]=B=>C.value=B),id:"comments","aria-describedby":"comments-description",name:"comments",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,rs),[[He,C.value]]),e("button",{onClick:D(B=>(C.value=[a.id],Ie("download",a.evidence_number)),["prevent"]),class:"w-8 h-8"},i[29]||(i[29]=[e("svg",{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-1s50 rounded-lg p-1.5",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("g",null,[e("path",{d:"M0 0h24v24H0z",fill:"none"}),e("path",{class:"fill-main-color-600",d:"M2 4h2v16H2V4zm4 0h1v16H6V4zm2 0h2v16H8V4zm3 0h2v16h-2V4zm3 0h2v16h-2V4zm3 0h1v16h-1V4zm2 0h3v16h-3V4z"})])],-1)]),8,ds)])):k.name==="evidence_number"?(t(),l(_,{key:1},[N(x(a.evidence_number||"-"),1)],64)):k.name==="invoice_number"?(t(),l(_,{key:2},[N(x(a.invoice_number||"-"),1)],64)):k.name==="name"?(t(),l(_,{key:3},[N(x(a.name||"-"),1)],64)):k.name==="price"?(t(),l(_,{key:4},[N(x(a.price)+" Kč ",1)],64)):k.name==="buyed_at"?(t(),l(_,{key:5},[a.buyed_at?(t(),l("span",us,x(s(we)(a.buyed_at).format("DD. MM. YYYY")),1)):y("",!0)],64)):k.name==="room"?(t(),l(_,{key:6},[N(x(((U=a.room)==null?void 0:U.name)||"-"),1)],64)):k.name==="user"?(t(),l(_,{key:7},[N(x(((ae=a.user)==null?void 0:ae.full_name)||"-"),1)],64)):k.name==="state"?(t(),l(_,{key:8},[a.state==="NOT_ASSIGNED"?(t(),l("span",ms,"Nepřiřazeno")):a.state==="ASSIGNED_TO_USER"?(t(),l("span",cs,"Přiřazeno uživateli")):a.state==="ASSIGNED_TO_ROOM"?(t(),l("span",vs,"Přiřazeno místnosti")):a.state==="LOCKED"?(t(),l("span",ps,"Uzamčeno")):a.state==="DISCARDED"?(t(),l("span",gs,"Vyřazeno")):y("",!0)],64)):k.name==="actions"?(t(),l("div",fs,[s(P).check("items.read")||s(P).check("property.master")?(t(),l("button",{key:0,onClick:D(B=>(Ce(a),f.$refs.detailItemRef.openModal(a)),["prevent"]),class:"mr-2"},[o(s(et),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,ys)):y("",!0),s(P).check("items.edit")||s(P).check("property.master")?(t(),l("button",{key:1,onClick:D(B=>(Ce(a),f.$refs.editItemRef.openModal(a)),["prevent"]),class:"mr-2"},[o(s(tt),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,bs)):y("",!0),s(P).check("items.discard")||s(P).check("property.master")?(t(),l("button",{key:2,onClick:D(B=>(Ce(a),f.$refs.discardItemRef.openModal()),["prevent"]),class:"mr-2"},[o(s(Pe),{class:"h-8 w-8 text-orange-600 bg-orange-200/75 hover:bg-orange-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,xs)):y("",!0),s(P).check("items.delete")||s(P).check("property.master")?(t(),l("button",{key:3,onClick:D(B=>(Ce(a),f.$refs.deleteItemRef.openModal()),["prevent"])},[o(s(H),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,hs)):y("",!0)])):(t(),l(_,{key:10},[k.name in a?(t(),l("div",ks,x(a[k.name]),1)):y("",!0)],64))])}),128))]))),128))])):(t(),l("tbody",_s,i[30]||(i[30]=[e("tr",null,[e("td",{colspan:"10",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné položky.")],-1)]))),C.value&&C.value.length?(t(),l("tfoot",ws,[e("tr",null,[e("td",$s,[e("div",Cs,[e("button",{onClick:i[19]||(i[19]=D(a=>Ie("open"),["prevent"])),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700 flex gap-1 justify-center items-center"},i[31]||(i[31]=[e("img",{class:"h-5 w-6",src:dt,"aria-hidden":"true",alt:"Generovat carovy kod"},null,-1),e("span",null,"Tisk štítků",-1)])),e("button",{onClick:i[20]||(i[20]=D(a=>Ie("download"),["prevent"])),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700 flex gap-1 justify-center items-center"},[o(s(at),{class:"h-5 w-5 text-white","aria-hidden":"true"}),i[32]||(i[32]=e("span",null,"Stažení štítků",-1))]),e("button",{onClick:i[21]||(i[21]=D(a=>C.value=[],["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},i[33]||(i[33]=[e("span",null,"Zrušit výběr",-1)]))])])])])):y("",!0)])):(t(),E(Z,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),Y.value!==null?(t(),E(st,{key:0,meta:Y.value,onSetPage:Ee,modelValue:b.value,"onUpdate:modelValue":i[22]||(i[22]=a=>b.value=a)},null,8,["meta","modelValue"])):y("",!0)])]),o(Pt,{ref_key:"createItemRef",ref:te,onReloadItems:i[23]||(i[23]=a=>se())},null,512),o(Gt,{ref_key:"deleteItemRef",ref:Q,selectedItem:v.value,onReloadItems:i[24]||(i[24]=a=>se())},null,8,["selectedItem"]),o(Zt,{ref_key:"discardItemRef",ref:le,selectedItem:v.value,onReloadItems:i[25]||(i[25]=a=>se())},null,8,["selectedItem"]),o(ba,{ref_key:"editItemRef",ref:G,selectedItem:v.value,onReloadItems:i[26]||(i[26]=a=>se())},null,8,["selectedItem"]),o(gl,{ref_key:"detailItemRef",ref:O,selectedItem:v.value,onReloadItems:i[27]||(i[27]=a=>se())},null,8,["selectedItem"])],64)}}};export{Fs as default};

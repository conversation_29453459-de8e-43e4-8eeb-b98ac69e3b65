import{a as m,I as L,k as h,o,b as a,e as n,w as k,d as e,c as d,u as t,v,F,q as V,s as u,t as y,E as p,x as z,a3 as q,h as D}from"./index-0d8d3833.js";import{g as O,m as T,L as M,E as C,a as E}from"./index-5b2a5028.js";import{A as P}from"./auth.service-44344015.js";import{_ as H}from"./AppTopbar-2155fa89.js";import{c}from"./checkPermission.service-455e2b2e.js";import"./index-cf95b4f0.js";const J={class:"space-y-12"},$={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},G={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},K={class:"flex justify-between items-center"},Q={class:"flex items-center"},R={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},W={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},X={class:"sm:col-span-3"},Y={class:"mt-2"},ee={key:1,class:"text-gray-900"},se={key:0},te={key:1},oe={class:"sm:col-span-3"},le={class:"mt-2"},ae={key:1,class:"text-gray-900"},ne={key:0},re={key:1},ie={class:"sm:col-span-3"},de={class:"mt-2"},me={key:1,class:"text-gray-900"},ue={key:0},ce={key:1},ge={class:"sm:col-span-3"},pe={class:"mt-2"},fe={key:1,class:"text-gray-900"},ve={key:0},ye={key:1},xe={key:1,class:"h-40 flex items-center"},be={class:"bg-white border border-zinc-200/70 rounded-md"},_e={key:0,class:"flex justify-between items-center p-5 pb-3"},we={class:"flex items-center"},he={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},ke={class:"p-5 border-y border-zinc-200/70 flex items-center"},Ve={class:"font-medium text-sm text-green-500"},je={key:1,class:"grid grid-cols-1 gap-y-6 p-5"},Ue={class:"sm:col-span-2"},Ze={class:"mt-2"},ze={class:"sm:col-span-2"},qe={class:"mt-2"},Ce={class:"sm:col-span-2"},Ee={class:"mt-2"},Pe={key:1,class:"h-40 flex items-center"},Se={key:0},Ne={class:"bg-white border border-zinc-200/70 rounded-md"},Ie={class:"flex justify-between items-center p-5 pb-3"},Ae={class:"flex items-center"},Be={class:"mt-8 grid grid-cols-1 gap-y-6 p-5 pt-0"},Le={class:"sm:col-span-2"},Fe={class:"mt-2 relative"},De={class:"sm:col-span-2"},Oe={class:"mt-2 relative"},Te={class:"sm:col-span-2"},Me={class:"mt-2 relative"},He={key:1,class:"h-40 flex items-center"},Xe={__name:"Profile",setup(Je){const l=m({}),x=m(""),j=m(""),b=m(""),_=m(""),U=m(""),w=m(""),S=m(["profile"]),f=m(!0),g=m(!1),Z=()=>{g.value=!g.value};L(()=>{f.value=!0,N(),f.value=!1});function N(){h.get("/api/users/profil").then(i=>{l.value=i.data}).catch(i=>{console.log(i)})}function I(){h.post("/api/users/profil",{first_name:l.value.first_name,middle_name:l.value.middle_name,last_name:l.value.last_name,degree_before:l.value.degree_before,degree_after:l.value.degree_after}).then(i=>{l.value=i.data.data.user,V.success(i.data.message)}).catch(i=>{V.error("Změny se nepovedlo uložit")})}function A(){h.post("/api/users/email-update",{new_email:x.value,new_email_confirmation:j.value,password:b.value}).then(i=>{P.logout(),V.success(i.data.message)}).catch(i=>{b.value=""})}function B(){h.post("/api/users/password-update",{new_password:_.value,new_password_confirmation:U.value,password:w.value}).then(i=>{P.logout(),V.success(i.data.message)}).catch(i=>{w.value=""})}return(i,s)=>(o(),a(F,null,[n(H,{breadCrumbs:S.value},{topbarButtons:k(()=>s[10]||(s[10]=[])),_:1},8,["breadCrumbs"]),e("div",J,[e("div",$,[e("div",null,[e("div",G,[f.value?(o(),a("div",xe,[n(t(q),{class:"mx-auto text-spinner-color",size:"40"})])):(o(),d(t(z),{key:0,onSubmit:I,class:"p-5 pb-8"},{default:k(()=>[e("div",K,[e("div",Q,[n(t(O),{class:"w-7"}),s[11]||(s[11]=e("p",{class:"ml-4 text-lg text-gray-900"},"Základní informace",-1))]),e("div",null,[t(c).check("profil.edit")?(o(),a("button",R,"Uložit")):v("",!0)])]),e("div",W,[e("div",X,[s[12]||(s[12]=e("label",{for:"degree-before",class:"block text-sm font-normal leading-6 text-gray-900"},"Titul před jménem:",-1)),e("div",Y,[t(c).check("profil.edit")?(o(),d(t(u),{key:0,modelValue:l.value.degree_before,"onUpdate:modelValue":s[0]||(s[0]=r=>l.value.degree_before=r),id:"degree-before",name:"degree-before",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte titul před jménem..."},null,8,["modelValue"])):(o(),a("div",ee,[l.value.degree_before&&l.value.degree_before.length?(o(),a("p",se,y(l.value.degree_before),1)):(o(),a("p",te,"-"))]))])]),e("div",oe,[s[13]||(s[13]=e("label",{for:"degree-after",class:"block text-sm font-normal leading-6 text-gray-900"},"Titul za jménem:",-1)),e("div",le,[t(c).check("profil.edit")?(o(),d(t(u),{key:0,modelValue:l.value.degree_after,"onUpdate:modelValue":s[1]||(s[1]=r=>l.value.degree_after=r),id:"degree-after",name:"degree-after",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte titul za jménem..."},null,8,["modelValue"])):(o(),a("div",ae,[l.value.degree_after&&l.value.degree_after.length?(o(),a("p",ne,y(l.value.degree_after),1)):(o(),a("p",re,"-"))]))])]),e("div",ie,[s[14]||(s[14]=e("label",{for:"first-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Jméno:",-1)),e("div",de,[t(c).check("profil.edit")?(o(),d(t(u),{key:0,rules:"required|minLength:2|maxLength:50",modelValue:l.value.first_name,"onUpdate:modelValue":s[2]||(s[2]=r=>l.value.first_name=r),type:"text",name:"first-name",id:"first-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte jméno.."},null,8,["modelValue"])):(o(),a("div",me,[l.value.first_name&&l.value.first_name.length?(o(),a("p",ue,y(l.value.first_name),1)):(o(),a("p",ce,"-"))])),n(t(p),{name:"first-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",ge,[s[15]||(s[15]=e("label",{for:"last-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Příjmení:",-1)),e("div",pe,[t(c).check("profil.edit")?(o(),d(t(u),{key:0,rules:"required|minLength:2|maxLength:50",modelValue:l.value.last_name,"onUpdate:modelValue":s[3]||(s[3]=r=>l.value.last_name=r),type:"text",name:"last-name",id:"last-name",autocomplete:"family-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte Příjmení.."},null,8,["modelValue"])):(o(),a("div",fe,[l.value.last_name&&l.value.last_name.length?(o(),a("p",ve,y(l.value.last_name),1)):(o(),a("p",ye,"-"))])),n(t(p),{name:"last-name",class:"text-rose-400 text-sm block pt-1"})])])])]),_:1}))]),e("div",be,[f.value?(o(),a("div",Pe,[n(t(q),{class:"mx-auto text-spinner-color",size:"40"})])):(o(),d(t(z),{key:0,onSubmit:A},{default:k(()=>[t(c).check("profil.change_email")?(o(),a("div",_e,[e("div",we,[n(t(T),{class:"w-7"}),s[16]||(s[16]=e("p",{class:"ml-4 text-lg text-gray-900"},"Změna emailové adresy",-1))]),e("div",null,[t(c).check("profil.change_email")?(o(),a("button",he,"Uložit")):v("",!0)])])):v("",!0),e("div",ke,[s[17]||(s[17]=e("p",{class:"mr-4 font-medium text-sm"},"Aktuální email:",-1)),e("p",Ve,y(l.value.email),1)]),t(c).check("profil.change_email")?(o(),a("div",je,[e("div",Ue,[s[18]||(s[18]=e("label",{for:"password",class:"block text-sm font-normal leading-6 text-gray-900"},"Aktuální heslo:",-1)),e("div",Ze,[n(t(u),{rules:"required",modelValue:b.value,"onUpdate:modelValue":s[4]||(s[4]=r=>b.value=r),id:"password",name:"password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte aktuální heslo..."},null,8,["modelValue"]),n(t(p),{name:"password",class:"text-rose-400 text-sm block pt-1"})])]),e("div",ze,[s[19]||(s[19]=e("label",{for:"new-email",class:"block text-sm font-normal leading-6 text-gray-900"},"Zadejte novou emailovou adresu:",-1)),e("div",qe,[n(t(u),{rules:"required|email",modelValue:x.value,"onUpdate:modelValue":s[5]||(s[5]=r=>x.value=r),id:"new-email",name:"new-email",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte novou emailovou adresu..."},null,8,["modelValue"]),n(t(p),{name:"new-email",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Ce,[s[20]||(s[20]=e("label",{for:"new-email-confirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakujte novou emailovou adresu:",-1)),e("div",Ee,[n(t(u),{rules:"required|email|isEqual:"+x.value,modelValue:j.value,"onUpdate:modelValue":s[6]||(s[6]=r=>j.value=r),id:"new-email-confirmation",name:"new-email-confirmation",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte novou emailovou adresu..."},null,8,["rules","modelValue"]),n(t(p),{name:"new-email-confirmation",class:"text-rose-400 text-sm block pt-1"})])])])):v("",!0)]),_:1}))])]),t(c).check("profil.change_password")?(o(),a("div",Se,[e("div",Ne,[f.value?(o(),a("div",He,[n(t(q),{class:"mx-auto text-spinner-color",size:"40"})])):(o(),d(t(z),{key:0,onSubmit:B},{default:k(()=>[e("div",Ie,[e("div",Ae,[n(t(M),{class:"w-7"}),s[21]||(s[21]=e("p",{class:"ml-4 text-lg text-gray-900"},"Změna hesla",-1))]),s[22]||(s[22]=e("div",null,[e("button",{type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},"Uložit")],-1))]),e("div",Be,[e("div",Le,[s[23]||(s[23]=e("label",{for:"old-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Aktuální heslo",-1)),e("div",Fe,[n(t(u),{rules:"required",modelValue:w.value,"onUpdate:modelValue":s[7]||(s[7]=r=>w.value=r),id:"old-password",name:"old-password",type:g.value?"text":"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte aktuální heslo..."},null,8,["modelValue","type"]),n(t(p),{name:"old-password",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:Z},[g.value?(o(),d(t(C),{key:0,name:"eye-off",class:"h-5 w-5"})):(o(),d(t(E),{key:1,name:"eye",class:"h-5 w-5"}))])])]),e("div",De,[s[24]||(s[24]=e("label",{for:"new-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Nové heslo",-1)),e("div",Oe,[n(t(u),{rules:"required|password",modelValue:_.value,"onUpdate:modelValue":s[8]||(s[8]=r=>_.value=r),id:"new-password",name:"new-password",type:g.value?"text":"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte nové heslo..."},null,8,["modelValue","type"]),n(t(p),{name:"new-password",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:Z},[g.value?(o(),d(t(C),{key:0,name:"eye-off",class:"h-5 w-5"})):(o(),d(t(E),{key:1,name:"eye",class:"h-5 w-5"}))])])]),e("div",Te,[s[25]||(s[25]=e("label",{for:"new-password-confirm",class:"block text-sm font-normal leading-6 text-gray-900"},"Nové heslo (znovu)",-1)),e("div",Me,[n(t(u),{rules:"required|password|isEqual:"+_.value,modelValue:U.value,"onUpdate:modelValue":s[9]||(s[9]=r=>U.value=r),id:"new-password-confirm",name:"new-password-confirm",type:g.value?"text":"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte nové heslo..."},null,8,["rules","modelValue","type"]),n(t(p),{name:"new-password-confirm",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:Z},[g.value?(o(),d(t(C),{key:0,name:"eye-off",class:"h-5 w-5"})):(o(),d(t(E),{key:1,name:"eye",class:"h-5 w-5"}))])])])]),s[26]||(s[26]=e("div",{class:"p-5 border-t border-zinc-200/70"},[e("p",{class:"text-sm font-semibold"},"Požadavky na nové heslo:"),e("ul",{class:"list-disc text-sm pl-6 pt-4"},[e("li",{class:"pb-1"},[D("Heslo musí mít alespoň "),e("span",{class:"font-semibold"},"8 znaků")]),e("li",{class:"pb-1"},"Doporučujeme využít kombinaci malých a velkých písmen"),e("li",null,"Doporučujeme využít v hesle i číslici")])],-1))]),_:1}))])])):v("",!0)])])],64))}};export{Xe as default};

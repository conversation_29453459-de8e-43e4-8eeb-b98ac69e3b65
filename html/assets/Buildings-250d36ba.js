import{a as i,j as S,o as d,c as M,w as c,e as a,h as I,d as t,u as l,x as E,s as z,E as j,C as B,k as R,q as Z,B as U,b as m,v as V,L,I as T,r as A,z as H,a2 as J,X as Q,F as P,f as W,t as G}from"./index-68b8ac03.js";import{_ as Y}from"./AppTopbar-8e1085f0.js";import{o as ee}from"./index-67bf9347.js";import{X as N}from"./index-034b8efa.js";import{_ as te}from"./pagination-e4e83c1d.js";import{c as x}from"./checkPermission.service-2282af58.js";import{_ as q}from"./basicModal-86318d6e.js";import{S as F}from"./transition-332c98cd.js";import"./listbox-b79c8b7a.js";import"./hidden-de3fbc3d.js";import"./use-tracked-pointer-c58e5566.js";import"./use-resolve-button-type-ab764b96.js";import"./use-controllable-d8e20660.js";import"./dialog-ef3853bc.js";const oe={class:"p-6 gap-4 space-y-4"},se={class:"mt-2"},le={class:"mt-2"},ne={class:"border-t p-5"},ae={class:"text-right space-x-3"},de={__name:"createBuildingModal",emits:["reloadItems"],setup(C,{expose:w,emit:$}){const _=$,y=i(!0);S("debugModeGlobalVar");const p=i(""),o=i(""),h=i(!1);function r(){y.value=!0,p.value="",o.value=""}function b(){y.value=!1}async function k(){x.check("buildings.create")||x.check("property.master")?await R.post("api/buildings",{name:p.value,code:o.value}).then(n=>{Z.success(n.data.message)}).catch(n=>{console.log(n)}):h.value=!1,b(),_("reloadItems",!0)}return w({openModal:r}),(n,e)=>(d(),M(l(F),{appear:"",show:y.value,as:"template",onClose:e[5]||(e[5]=g=>b())},{default:c(()=>[a(q,{size:"xs"},{"modal-title":c(()=>e[6]||(e[6]=[I("Založení nové budovy")])),"modal-close-button":c(()=>[t("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:e[0]||(e[0]=g=>b())},[a(l(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[a(l(E),{onSubmit:e[4]||(e[4]=g=>k())},{default:c(({values:g})=>[t("div",oe,[t("div",null,[e[7]||(e[7]=t("label",{for:"building-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název budovy:",-1)),t("div",se,[a(l(z),{rules:"required",modelValue:p.value,"onUpdate:modelValue":e[1]||(e[1]=f=>p.value=f),type:"text",name:"building-name",id:"building-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název budovy..."},null,8,["modelValue"]),a(l(j),{name:"building-name",class:"text-rose-400 text-sm block pt-1"})])]),t("div",null,[e[8]||(e[8]=t("label",{for:"item-code",class:"block text-sm font-normal leading-6 text-gray-900"},"Kód budovy (nepovinné):",-1)),t("div",le,[a(l(z),{modelValue:o.value,"onUpdate:modelValue":e[2]||(e[2]=f=>o.value=f),type:"text",name:"item-code",id:"item-code",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte kód budovy..."},null,8,["modelValue"]),a(l(j),{name:"item-code",class:"text-rose-400 text-sm block pt-1"})])])]),t("div",ne,[t("div",ae,[t("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:e[3]||(e[3]=B(f=>b(),["prevent"]))}," Zavřít "),e[9]||(e[9]=t("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"}," Vytvořit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},ie={key:0,class:"p-6 space-y-4"},re={class:"mt-2"},ue={class:"mt-2"},me={class:"border-t p-5"},ce={class:"text-right space-x-3"},pe={__name:"editBuildingModal",props:{selectedBuilding:{type:Object,required:!0}},emits:["reloadBuildings"],setup(C,{expose:w,emit:$}){const _=$,y=C,p=i(!1);S("debugModeGlobalVar");const o=i(""),h=i(!1);U(()=>y.selectedBuilding,(n,e)=>{o.value={},o.value=n});function r(){p.value=!1}async function b(){p.value=!0}async function k(){x.check("buildings.edit")||x.check("property.master")?(console.log(y.selectedBuilding),await R.post("api/buildings/"+y.selectedBuilding.id+"/update",{name:o.value.name,code:o.value.code}).then(n=>{Z.success(n.data.message)}).catch(n=>{console.log(n)})):h.value=!1,r(),_("reloadBuildings",!0)}return w({openModal:b}),(n,e)=>(d(),M(l(F),{appear:"",show:p.value,as:"template",onClose:e[5]||(e[5]=g=>r())},{default:c(()=>[a(q,{size:"sm"},{"modal-title":c(()=>e[6]||(e[6]=[I("Úprava budovy")])),"modal-close-button":c(()=>[t("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:e[0]||(e[0]=g=>r())},[a(l(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[a(l(E),{onSubmit:e[4]||(e[4]=g=>k())},{default:c(({values:g})=>[o.value?(d(),m("div",ie,[t("div",null,[e[7]||(e[7]=t("label",{for:"building-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název budovy:",-1)),t("div",re,[a(l(z),{rules:"required",modelValue:o.value.name,"onUpdate:modelValue":e[1]||(e[1]=f=>o.value.name=f),type:"text",name:"building-name",id:"building-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název budovy..."},null,8,["modelValue"]),a(l(j),{name:"building-name",class:"text-rose-400 text-sm block pt-1"})])]),t("div",null,[e[8]||(e[8]=t("label",{for:"building-code",class:"block text-sm font-normal leading-6 text-gray-900"},"Kód budovy (nepovinné):",-1)),t("div",ue,[a(l(z),{modelValue:o.value.code,"onUpdate:modelValue":e[2]||(e[2]=f=>o.value.code=f),type:"text",name:"building-code",id:"building-code",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte kód budovy..."},null,8,["modelValue"]),a(l(j),{name:"building-code",class:"text-rose-400 text-sm block pt-1"})])])])):V("",!0),t("div",me,[t("div",ce,[t("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:e[3]||(e[3]=B(f=>r(),["prevent"]))}," Zavřít "),e[9]||(e[9]=t("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Upravit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},ge={key:0,class:"p-6"},ve={class:"border-t p-5"},be={class:"text-right space-x-3"},xe={__name:"deleteBuildingModal",props:{selectedBuilding:{type:Object,required:!0}},emits:["reloadBuildings"],setup(C,{expose:w,emit:$}){const _=$,y=C,p=i(!1);S("debugModeGlobalVar");const o=i(""),h=i(!1);U(()=>y.selectedBuilding,(n,e)=>{o.value=n});function r(){p.value=!1}async function b(){p.value=!0}async function k(){x.check("buildings.delete")||x.check("property.master")?await R.post("api/buildings/"+o.value.id+"/delete").then(n=>{Z.success(n.data.message)}).catch(n=>{console.log(n)}):h.value=!1,r(),_("reloadBuildings",!0)}return w({openModal:b}),(n,e)=>(d(),M(l(F),{appear:"",show:p.value,as:"template",onClose:e[3]||(e[3]=g=>r())},{default:c(()=>[a(q,{size:"sm"},{"modal-title":c(()=>e[4]||(e[4]=[I("Smazat budovu")])),"modal-close-button":c(()=>[t("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:e[0]||(e[0]=g=>r())},[a(l(N),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[o.value?(d(),m("div",ge,e[5]||(e[5]=[t("span",null,"Opravdu si přejete budovu smazat?",-1)]))):V("",!0),t("div",ve,[t("div",be,[t("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:e[1]||(e[1]=B(g=>r(),["prevent"]))}," Zavřít "),t("button",{onClick:e[2]||(e[2]=B(g=>k(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}},ye={class:"space-y-6"},fe={class:"px-0"},he={class:"bg-white border border-zinc-200/70 rounded-md p-5"},ke={class:"sm:flex justify-between items-center gap-4"},we={class:"w-80"},$e={class:"flex items-center gap-4"},_e={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},Be={class:"sm:-mx-6 lg:-mx-8"},Ve={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},Ce={key:0,class:"min-w-full divide-y divide-gray-200"},Me={key:0,class:"divide-y divide-gray-200"},ze={class:"whitespace-nowrap py-4 pl-5 pr-3 text-sm text-gray-600"},je={key:0},Se={key:1},Re={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},Ne={key:0},Ie={key:1},Ze={class:"text-end pr-5 w-40"},Ue=["onClick"],qe=["onClick"],Fe={key:1},Ye={__name:"Buildings",setup(C){const w=i(""),$=i(""),_=i(""),y=L();S("debugModeGlobalVar");const p=i(["buildings"]),o=i(!1),h=i(""),r=i(1),b=i({}),k=i({}),n=i({});T(()=>{e()}),U(()=>y.perPage,(v,s)=>{o.value=!0,r.value=1,e()});async function e(){o.value=!0,await R.get("/api/buildings?page="+r.value+"&search="+h.value).then(v=>{k.value=v.data.data,b.value=v.data.meta}).catch(v=>{console.log(v)}),o.value=!1}function g(v){n.value=v}function f(v){r.value=v,e()}function O(){o.value=!0,r.value=1,h.value="",e()}function K(){o.value=!0,r.value=1,e()}return(v,s)=>{const X=A("VueSpinner");return d(),m(P,null,[a(Y,{breadCrumbs:p.value},{topbarButtons:c(()=>[l(x).check("buildings.create")||l(x).check("property.master")?(d(),m("button",{key:0,onClick:s[0]||(s[0]=B(u=>v.$refs.createBuildingRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nová budova ")):V("",!0)]),_:1},8,["breadCrumbs"]),t("div",ye,[t("div",fe,[t("div",he,[t("div",ke,[t("div",we,[H(t("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":s[1]||(s[1]=u=>h.value=u),onKeyup:s[2]||(s[2]=Q(u=>K(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[J,h.value]])]),t("div",$e,[t("button",{onClick:s[3]||(s[3]=u=>O()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},s[9]||(s[9]=[t("span",null,"Resetovat",-1)])),t("button",{onClick:s[4]||(s[4]=u=>K()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),t("div",null,[t("div",_e,[t("div",Be,[t("div",Ve,[o.value==!1?(d(),m("table",Ce,[s[11]||(s[11]=t("thead",null,[t("tr",null,[t("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Kód budovy "),t("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Název budovy "),t("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"})])],-1)),k.value&&k.value.length?(d(),m("tbody",Me,[(d(!0),m(P,null,W(k.value,u=>(d(),m("tr",{key:u.id},[t("td",ze,[u.code?(d(),m("span",je,G(u.code),1)):(d(),m("span",Se,"-"))]),t("td",Re,[u.name?(d(),m("span",Ne,G(u.name),1)):(d(),m("span",Ie,"-"))]),t("td",Ze,[l(x).check("buildings.edit")||l(x).check("property.master")?(d(),m("button",{key:0,onClick:B(D=>(g(u),v.$refs.editBuildingRef.openModal()),["prevent"]),class:"mr-2"},[a(l(ee),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,Ue)):V("",!0),l(x).check("buildings.delete")||l(x).check("property.master")?(d(),m("button",{key:1,onClick:B(D=>(g(u),v.$refs.deleteBuildingRef.openModal()),["prevent"])},[a(l(N),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,qe)):V("",!0)])]))),128))])):(d(),m("tbody",Fe,s[10]||(s[10]=[t("tr",null,[t("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné budovy.")],-1)])))])):(d(),M(X,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),b.value!==null?(d(),M(te,{key:0,meta:b.value,onSetPage:f,modelValue:r.value,"onUpdate:modelValue":s[5]||(s[5]=u=>r.value=u)},null,8,["meta","modelValue"])):V("",!0)])]),a(de,{ref_key:"createBuildingRef",ref:w,size:"sm",onReloadItems:s[6]||(s[6]=u=>e())},null,512),a(pe,{ref_key:"editBuildingRef",ref:$,size:"sm",selectedBuilding:n.value,onReloadBuildings:s[7]||(s[7]=u=>e())},null,8,["selectedBuilding"]),a(xe,{ref_key:"deleteBuildingRef",ref:_,size:"sm",selectedBuilding:n.value,onReloadBuildings:s[8]||(s[8]=u=>e())},null,8,["selectedBuilding"])],64)}}};export{Ye as default};

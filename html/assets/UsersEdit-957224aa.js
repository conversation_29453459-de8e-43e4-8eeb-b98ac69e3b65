import{A as oe,a as u,o as n,c as f,w as c,d as e,e as l,u as a,x as g,E as h,h as w,z as ne,l as V,v as H,j as fe,D as _e,r as be,b as d,t as m,y as p,F as O,a3 as R,T as X,f as J,n as P}from"./index-04cb9130.js";import{_ as he}from"./AppTopbar-73f760e5.js";import{d as K,C as T,X as ke}from"./index-b2d17f43.js";import{L as re,E as Q,a as W,_ as we,g as Ve,B as ee,b as te,R as Ce,G as Ue}from"./index-4485c523.js";import{S as je}from"./vue-tailwind-datepicker-08bb3bad.js";import{s as ze}from"./default.css_vue_type_style_index_1_src_true_lang-d0de2321.js";import{c as q}from"./checkPermission.service-6ab2ad75.js";import{E as se,A as ae,F as le,B as Y}from"./listbox-b603cc4e.js";import"./hidden-0aacd91d.js";import"./use-tracked-pointer-3bd62e78.js";import"./use-resolve-button-type-0913c19f.js";import"./use-controllable-f4b23304.js";const Pe={class:"space-y-4 bg-white border border-zinc-200/70 rounded-md p-5"},Ee={class:"flex justify-between items-center"},Se={class:"flex items-center"},Ze={class:"mt-2 relative"},$e={class:"grid grid-cols-12 items-end gap-4 border-b pb-8 mb-6"},De={class:"col-span-12 sm:col-span-8"},Ie={class:"mt-2 relative"},Me={class:"col-span-12 sm:col-span-4 mb-2"},Le={class:"flex h-6 md:justify-end md:items-center"},Ae=["checked"],Be={class:"col-span-12 sm:col-span-12 mb-2"},qe={class:"flex h-6 justify-start items-center"},Ge={__name:"usersEditChangeAdPassword",setup(ie){const G=oe();u({});const C=u(null),E=u(null),_=u(0),s=u(0),v=u(!1),I=()=>{v.value=!v.value};function M(){_.value=!_.value}function L(){V.post("/api/users/"+G.params.id+"/ad-password-update",{password:C.value,password_confirmation:E.value,must_change_password:s.value,send_sms:_.value,show_password:1}).then(b=>{H.success(b.data.message),getTableData(),closeCreateUserModal()}).catch(b=>{console.log(b)})}const A=u([{id:1,name:"A"},{id:2,name:"B"},{id:3,name:"C"},{id:4,name:"D"},{id:5,name:"E"}]);return u(A.value[0]),(b,i)=>(n(),f(a(ne),{onSubmit:L,class:"col-span-8 md:col-span-4 order-2 2xl:order-3 2xl:col-span-3"},{default:c(({values:F})=>[e("div",Pe,[e("div",Ee,[e("div",Se,[l(a(re),{class:"w-7"}),i[4]||(i[4]=e("p",{class:"ml-4 text-lg text-gray-900"},"Změna hesla",-1))]),i[5]||(i[5]=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Uložit ",-1))]),e("div",null,[i[6]||(i[6]=e("label",{for:"userPassword",class:"block text-sm font-normal leading-6 text-gray-900"},"Heslo:",-1)),e("div",Ze,[l(a(g),{rules:"required|password",modelValue:C.value,"onUpdate:modelValue":i[0]||(i[0]=k=>C.value=k),id:"userPassword",name:"userPassword",type:v.value?"text":"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte heslo..."},null,8,["modelValue","type"]),l(a(h),{name:"userPassword",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:I},[v.value?(n(),f(a(Q),{key:0,name:"eye-off",class:"h-5 w-5"})):(n(),f(a(W),{key:1,name:"eye",class:"h-5 w-5"}))])])]),e("div",$e,[e("div",De,[i[7]||(i[7]=e("label",{for:"userPasswordConfirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakovat heslo:",-1)),e("div",Ie,[l(a(g),{rules:"required|password|isEqual:"+C.value,modelValue:E.value,"onUpdate:modelValue":i[1]||(i[1]=k=>E.value=k),id:"userPasswordConfirmation",name:"userPasswordConfirmation",type:v.value?"text":"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte heslo..."},null,8,["rules","modelValue","type"]),l(a(h),{name:"userPasswordConfirmation",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:I},[v.value?(n(),f(a(Q),{key:0,name:"eye-off",class:"h-5 w-5"})):(n(),f(a(W),{key:1,name:"eye",class:"h-5 w-5"}))])])]),e("div",Me,[e("div",Le,[e("input",{id:"sendSms","aria-describedby":"sendSms",name:"sendSms",onClick:i[2]||(i[2]=k=>M()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",checked:_.value},null,8,Ae),i[8]||(i[8]=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"sendSms",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Zaslat do SMS")],-1))])]),e("div",Be,[e("div",qe,[l(a(g),{id:"mustChangePassword","aria-describedby":"mustChangePassword",name:"mustChangePassword",modelValue:s.value,"onUpdate:modelValue":i[3]||(i[3]=k=>s.value=k),value:!s.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),i[9]||(i[9]=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"mustChangePassword",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Vyžádat změnu hesla po přihlášení")],-1))])])]),i[10]||(i[10]=e("div",null,[e("span",{class:"text-sm font-semibold inline-block pb-4"},"Požadavky na nové heslo:"),e("ul",{class:"list-disc ml-6 text-sm space-y-1"},[e("li",null,[w("Heslo musí mít alespoň "),e("span",null,"8 znaků")]),e("li",null,"Doporučujeme využít kombinaci malých a velkých písmen"),e("li",null,"Doporučujeme využít v hesle i číslici")])],-1))])]),_:1}))}};const Oe={key:0,type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},Te={class:"md:flex justify-between items-center"},Ye={class:"pb-6 pl-2 flex items-center gap-6"},He={class:"text-xl"},Fe={key:0},Ne={key:1},Re={key:2},Xe={key:0,class:"flex gap-2 items-center"},Je={class:"bg-main-color-100 rounded-full p-1"},Ke={class:"flex"},Qe={class:"mb-6 sm:border sm:border-zinc-200/70 rounded-md sm:flex gap-x-0.5 sm:bg-zinc-200/70 space-y-2 sm:space-y-0"},We={class:"flex items-center"},et={class:"grid grid-cols-8 gap-6"},tt={class:"col-span-8 md:col-span-4 order-1 2xl:order-1 2xl:col-span-3"},st={class:"space-y-4 bg-white border border-zinc-200/70 rounded-md p-5"},at={class:"flex items-center"},lt={class:"grid grid-cols-1 sm:grid-cols-2 gap-6 gap-y-4"},ot={class:"mt-2"},nt={class:"mt-2"},rt={class:"mt-2"},it={class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 bg-gray-100 placeholder:text-gray-400 text-sm sm:leading-6 outline-none"},dt={class:"mt-2"},ut={class:"mt-2"},mt={class:"grid grid-cols-12 items-end gap-4"},ct={class:"col-span-12 sm:col-span-8"},pt={class:"mt-2"},vt={class:"col-span-12 sm:col-span-4 mb-2"},gt={class:"flex h-6 md:justify-end md:items-center"},yt={key:0,class:"col-span-8 md:col-span-4 order-3 2xl:order-2 2xl:col-span-2"},xt={class:"bg-white border border-zinc-200/70 rounded-md p-5"},ft={key:0,class:"border-b pb-6"},_t={class:"flex items-center my-4"},bt={class:"relative mt-1"},ht={key:0,class:"block truncate"},kt={key:0,class:"bg-main-color-200 rounded-lg inline-block py-1 px-2 ml-2"},wt={key:1,class:"block truncate text-gray-400"},Vt={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Ct={key:0,class:"bg-main-color-200 rounded-lg inline-block py-1 px-2 ml-2"},Ut={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},jt={key:1},zt={class:"flex items-center my-4"},Pt={key:2,class:"space-y-4 border-t pt-6 mt-6"},Et={class:"flex items-center"},St={class:"flex h-6 justify-start items-center"},Zt={class:"rounded-l-full w-full"},$t={type:"button",class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},Dt={class:"flex items-center w-full"},It={class:"flex-1 text-left"},Mt={key:0,class:"text-gray-900"},Lt={key:1,class:"text-gray-400 text-sm"},At={class:"w-6 h-6"},Bt={class:"flex gap-2"},qt={key:0,class:"bg-white border border-zinc-200/70 rounded-md p-5 mt-6"},Gt={class:"flex items-center my-4"},Ot={class:"grid grid-cols-3 gap-4"},Tt={class:"col-span-3 lg:col-span-1"},Yt={class:"mt-2"},Ht={class:"col-span-3 lg:col-span-1"},Ft={class:"mt-2"},Nt={class:"col-span-3 lg:col-span-1 flex items-end gap-4"},Rt={class:"grow"},Xt={class:"mt-2"},Jt={class:"flex items-center"},Kt={class:"relative mt-1"},Qt={key:0,class:"block truncate"},Wt={key:1,class:"block truncate text-gray-400"},es={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},ts={key:0,class:"absolute inset-y-0 right-0 flex items-center pl-3 text-main-color-600"},ss={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},as={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block mt-6"},ys={__name:"UsersEdit",setup(ie){const G=fe("debugModeGlobalVar"),C=u(["skolasys-root","users-edit"]),E=u(),_=oe(),s=u({}),v=u(),I=u({}),M=u({}),L=u({}),A=u(I.value.id),b=u("email"),i=u([]),F=u([]),k=u(null),de=u(null),ue=u(0);_e(()=>{me(),ce(),ve(),pe()});function me(){V.get("/api/users/"+_.params.id).then(r=>{s.value=r.data.data,s.value.email_confirmation=r.data.data.email,s.value.email_verified_at?v.value=!0:v.value=!1,s.value.active_directory==1?(xe(),b.value="email"):b.value="required|email"}).catch(r=>{console.log(r)})}function ce(){V.get("/api/organization-units?perpage=9999").then(r=>{M.value=r.data.data,A.value=r.data.data[0]}).catch(r=>{console.log(r)})}function pe(){V.get("/api/account-control-codes?listing=1").then(r=>{L.value=r.data.data}).catch(r=>{console.log(r)})}function ve(){V.get("/api/groups?perpage=9999").then(r=>{i.value=r.data.data}).catch(r=>{console.log(r)})}function ge(){var r,t,j,B,o,y,x,S,Z,$;if(s.value.active_directory==1){let z={first_name:s.value.first_name,last_name:s.value.last_name,email:s.value.email,email_verified:v.value,phone:s.value.phone,organization_unit:((j=(t=(r=s.value)==null?void 0:r.active_directory_data)==null?void 0:t.organization_unit)==null?void 0:j.id)||null,visitor:s.value.active_directory_data.visitors,groups:s.value.active_directory_data.groups.map(D=>D.id),visitor:s.value.active_directory_data.visitor,profile_path:((o=(B=s.value)==null?void 0:B.active_directory_data)==null?void 0:o.profile_path)||null,script_path:((x=(y=s.value)==null?void 0:y.active_directory_data)==null?void 0:x.script_path)||null,home_directory:((Z=(S=s.value)==null?void 0:S.active_directory_data)==null?void 0:Z.home_directory)||null,home_drive:(($=U.value)==null?void 0:$.name)||null};s.value.active_directory_data.expire_date&&s.value.active_directory_data.expire_date[0]&&s.value.active_directory_data.expire_date[0].length&&(z.expire=R(s.value.active_directory_data.expire_date[0]).format("YYYY-MM-DD HH:MM")),V.post("/api/users/"+_.params.id+"/ad-update",z).then(D=>{H.success(D.data.message)}).catch(D=>{console.log(D)})}else V.post("/api/users/"+_.params.id+"/update",{first_name:s.value.first_name,last_name:s.value.last_name,email:s.value.email,email_verified:v.value,phone:s.value.phone}).then(z=>{H.success(z.data.message)}).catch(z=>{console.log(z)})}function ye(){s.value.active_directory_data.visitor=!s.value.active_directory_data.visitor}const N=u(Array.from({length:26},(r,t)=>({id:t+1,name:String.fromCharCode(65+t)+":"}))),U=u(null);function xe(){s.value.active_directory_data.home_drive&&(U.value=N.value.find(r=>r.name==s.value.active_directory_data.home_drive))}return(r,t)=>{const j=be("router-link");return n(),d(O,null,[l(a(ne),{onSubmit:t[16]||(t[16]=B=>ge())},{default:c(({values:B})=>[l(he,{breadCrumbs:C.value},{topbarButtons:c(()=>[l(j,{to:{name:"users"},class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200 flex items-center"},{default:c(()=>t[17]||(t[17]=[w(" Zpět ")])),_:1}),a(q).check("users.edit")?(n(),d("button",Oe,"Uložit ")):p("",!0)]),_:1},8,["breadCrumbs"]),e("div",Te,[e("div",Ye,[e("h2",He,[s.value.first_name?(n(),d("span",Fe,m(s.value.first_name+" "),1)):p("",!0),s.value.midle_name?(n(),d("span",Ne,m(s.value.midle_name+" "),1)):p("",!0),s.value.last_name?(n(),d("span",Re,m(s.value.last_name),1)):p("",!0)]),s.value.active_directory==0?(n(),d("div",Xe,[e("span",Je,[l(a(we),{class:"h-4 w-4 text-main-color-600","aria-hidden":"true"})]),t[18]||(t[18]=e("span",{class:"text-xs text-main-color-600"},"Lokální účet",-1))])):p("",!0)]),e("div",Ke,[e("div",Qe,[l(j,{class:"flex rounded-md sm:rounded-r-none sm:rounded-t-none sm:rounded-b-none sm:rounded-l-md px-8 h-10 text-center text-sm font-medium text-gray-900 bg-white border-b-2 border-zinc-200 sm:border-white hover:bg-gray-50 hover:border-gray-50","exact-active-class":"!border-main-color-600",to:{name:"users-edit",params:{id:a(_).params.id}}},{default:c(()=>t[19]||(t[19]=[e("span",{class:"flex items-center"},"Základní informace ",-1)])),_:1},8,["to"]),a(q).check("users.read_roles_permissions")&&a(q).check("roles.read")&&a(q).check("permissions.read")?(n(),f(j,{key:0,class:"flex rounded-md sm:rounded-l-none sm:rounded-t-none sm:rounded-b-none sm:rounded-r-md px-8 h-10 text-center text-sm font-medium text-gray-900 bg-white border-b-2 border-zinc-200 sm:border-white hover:bg-gray-50 hover:border-gray-50","exact-active-class":"!border-main-color-600",to:{name:"users-roles-permissions",params:{id:a(_).params.id}}},{default:c(()=>[e("span",We,[l(a(re),{class:"h-4"}),t[20]||(t[20]=e("p",{class:"ml-2"},"Role a oprávnění",-1))])]),_:1},8,["to"])):p("",!0)])])]),e("div",et,[e("div",tt,[e("div",st,[e("div",at,[l(a(Ve),{class:"w-7"}),t[21]||(t[21]=e("p",{class:"ml-4 text-lg text-gray-900"},"Základní informace",-1))]),e("div",lt,[e("div",null,[t[22]||(t[22]=e("label",{for:"first-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Jméno:",-1)),e("div",ot,[l(a(g),{rules:"required|minLength:2|maxLength:50",modelValue:s.value.first_name,"onUpdate:modelValue":t[0]||(t[0]=o=>s.value.first_name=o),type:"text",name:"first-name",id:"first-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte jméno..."},null,8,["modelValue"]),l(a(h),{name:"first-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[t[23]||(t[23]=e("label",{for:"last-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Příjmení:",-1)),e("div",nt,[l(a(g),{rules:"required|minLength:2|maxLength:50",modelValue:s.value.last_name,"onUpdate:modelValue":t[1]||(t[1]=o=>s.value.last_name=o),type:"text",name:"last-name",id:"last-name",autocomplete:"family-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte Příjmení..."},null,8,["modelValue"]),l(a(h),{name:"last-name",class:"text-rose-400 text-sm block pt-1"})])])]),e("div",null,[t[24]||(t[24]=e("label",{for:"",class:"block text-sm font-normal leading-6 text-gray-900"},"Login:",-1)),e("div",rt,[e("p",it,m(s.value.account_name),1)])]),e("div",null,[t[25]||(t[25]=e("label",{for:"last-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Telefonní číslo:",-1)),e("div",dt,[l(a(g),{rules:"phone",modelValue:s.value.phone,"onUpdate:modelValue":t[2]||(t[2]=o=>s.value.phone=o),type:"tel",name:"phone",id:"phone",autocomplete:"phone",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte tel. číslo..."},null,8,["modelValue"]),l(a(h),{name:"phone",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[t[26]||(t[26]=e("label",{for:"new-email",class:"block text-sm font-normal leading-6 text-gray-900"},"Emailová adresa:",-1)),e("div",ut,[l(a(g),{rules:b.value,modelValue:s.value.email,"onUpdate:modelValue":t[3]||(t[3]=o=>s.value.email=o),id:"new-email",name:"new-email",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte emailovou adresu..."},null,8,["rules","modelValue"]),l(a(h),{name:"new-email",class:"text-rose-400 text-sm block pt-1"})])]),e("div",mt,[e("div",ct,[t[27]||(t[27]=e("label",{for:"new-email-confirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakovat email:",-1)),e("div",pt,[l(a(g),{rules:b.value+"|isEqual:"+s.value.email,modelValue:s.value.email_confirmation,"onUpdate:modelValue":t[4]||(t[4]=o=>s.value.email_confirmation=o),id:"new-email-confirmation",name:"new-email-confirmation",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte emailovou adresu..."},null,8,["rules","modelValue"]),l(a(h),{name:"new-email-confirmation",class:"text-rose-400 text-sm block pt-1"})])]),e("div",vt,[e("div",gt,[l(a(g),{id:"verifiedEmail","aria-describedby":"verifiedEmail",name:"verifiedEmail",modelValue:v.value,"onUpdate:modelValue":t[5]||(t[5]=o=>v.value=o),value:!v.value,type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer"},null,8,["modelValue","value"]),t[28]||(t[28]=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"verifiedEmail",class:"text-gray-900 cursor-pointer text-sm font-semibold"},"Ověřený email")],-1))])])])])]),s.value.active_directory==1?(n(),d("div",yt,[e("div",xt,[s.value.active_directory==1?(n(),d("div",ft,[e("div",_t,[l(a(ee),{class:"w-7"}),t[29]||(t[29]=e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do OU",-1))]),l(a(g),{rules:"required",modelValue:s.value.active_directory_data.organization_unit,"onUpdate:modelValue":t[6]||(t[6]=o=>s.value.active_directory_data.organization_unit=o),type:"text",name:"selected_ou",id:"selected_ou",class:"hidden"},null,8,["modelValue"]),l(a(se),{modelValue:s.value.active_directory_data.organization_unit,"onUpdate:modelValue":t[7]||(t[7]=o=>s.value.active_directory_data.organization_unit=o)},{default:c(()=>[e("div",bt,[l(a(ae),{class:"relative w-full text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:c(()=>{var o,y,x,S,Z,$;return[(y=(o=s.value.active_directory_data)==null?void 0:o.organization_unit)!=null&&y.name?(n(),d("span",ht,[e("div",null,[w(m((S=(x=s.value.active_directory_data)==null?void 0:x.organization_unit)==null?void 0:S.name)+" ",1),($=(Z=s.value.active_directory_data)==null?void 0:Z.organization_unit)!=null&&$.is_class?(n(),d("span",kt,t[30]||(t[30]=[e("span",{class:"flex items-center"},[e("span",{class:"text-xs text-main-color-700 font-semibold"},"Třída")],-1)]))):p("",!0)])])):(n(),d("span",wt,"Vyberte organizační jednotku...")),e("span",Vt,[l(a(K),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]}),_:1}),l(X,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:c(()=>[l(a(le),{class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:c(()=>[(n(!0),d(O,null,J(M.value,o=>(n(),f(a(Y),{key:o.id,value:o,as:"template"},{default:c(({active:y,selected:x})=>[e("li",{class:P([y?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[e("span",{class:P([x?"font-medium":"font-normal","block truncate"])},[e("div",null,[w(m(o.name)+" ",1),o.is_class?(n(),d("span",Ct,t[31]||(t[31]=[e("span",{class:"flex items-center"},[e("span",{class:"text-xs text-main-color-700 font-semibold"},"Třída")],-1)]))):p("",!0)])],2),x?(n(),d("span",Ut,[l(a(T),{class:"h-5 w-5","aria-hidden":"true"})])):p("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"]),l(a(h),{name:"selected_ou",class:"text-rose-400 text-sm block pt-1"})])):p("",!0),s.value.active_directory==1?(n(),d("div",jt,[e("div",zt,[l(a(te),{class:"w-7"}),t[32]||(t[32]=e("p",{class:"ml-4 text-lg text-gray-900"},"Zařazení do skupin",-1))]),l(a(g),{name:"selectedGroups",value:s.value.active_directory_data.groups},{default:c(({handleChange:o,value:y})=>[l(a(ze),{name:"selectedGroups",modelValue:s.value.active_directory_data.groups,"onUpdate:modelValue":[t[8]||(t[8]=x=>s.value.active_directory_data.groups=x),o],mode:"tags",label:"name","value-prop":"id",options:i.value,object:!0,placeholder:"Zvolte si skupiny",class:"!rounded-lg"},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1},8,["value"]),l(a(h),{name:"selectedGroups",class:"text-rose-400 text-sm block pt-1"})])):p("",!0),s.value.active_directory==1?(n(),d("div",Pt,[e("div",Et,[l(a(te),{class:"w-7"}),t[33]||(t[33]=e("p",{class:"ml-4 text-lg text-gray-900"},"Host / expirace účtu",-1))]),e("div",St,[l(a(g),{id:"visitor","aria-describedby":"visitor",name:"visitor",onClick:t[9]||(t[9]=o=>ye()),type:"checkbox",class:"h-5 w-5 rounded border-gray-300 text-main-color-600 focus:ring-transparent cursor-pointer",modelValue:s.value.active_directory_data.visitor,"onUpdate:modelValue":t[10]||(t[10]=o=>s.value.active_directory_data.visitor=o),value:!s.value.active_directory_data.visitor},null,8,["modelValue","value"]),t[34]||(t[34]=e("div",{class:"ml-3 text-md leading-6"},[e("label",{for:"visitor",class:"text-gray-900 cursor-pointer text-sm"},[w("Uživatelský účet je "),e("strong",null,"účet hosta")])],-1))]),e("div",null,[l(a(je),{i18n:"cs","as-single":"",shortcuts:!1,modelValue:s.value.active_directory_data.expire_date,"onUpdate:modelValue":t[11]||(t[11]=o=>s.value.active_directory_data.expire_date=o),placeholder:"Zvolte datum, do kdy je účet aktivní"},{default:c(({clear:o})=>[e("div",null,[e("div",Zt,[e("button",$t,[e("div",Dt,[e("div",It,[s.value.active_directory_data.expire_date&&s.value.active_directory_data.expire_date.length?(n(),d("span",Mt,[e("span",null,m(a(R)(s.value.active_directory_data.expire_date[0]).format("DD.MM.YYYY")),1)])):(n(),d("span",Lt,"Zvolte datum, do kdy je účet aktivní"))]),e("div",At,[s.value.active_directory_data.expire_date&&s.value.active_directory_data.expire_date.length?(n(),f(a(ke),{key:0,onClick:o,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(n(),f(a(Ce),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))])])])])])]),_:1},8,["modelValue"])]),e("div",Bt,[e("div",null,[l(a(Ue),{class:"h-5 w-5 text-main-color-600","aria-hidden":"true"})]),t[35]||(t[35]=e("span",{class:"font-light text-sm"},"V případě nenastavení data, bude účet neomezeně platný.",-1))])])):p("",!0)])])):p("",!0),s.value.active_directory==1?(n(),f(Ge,{key:1})):p("",!0)]),s.value.active_directory==1?(n(),d("div",qt,[e("div",Gt,[l(a(ee),{class:"w-7"}),t[36]||(t[36]=e("p",{class:"ml-4 text-lg text-gray-900"},"Doplňující informace",-1))]),e("div",Ot,[e("div",Tt,[t[37]||(t[37]=e("label",{for:"profile_path",class:"block text-sm font-normal leading-6 text-gray-900"},"Cesta k profilu:",-1)),e("div",Yt,[l(a(g),{modelValue:s.value.active_directory_data.profile_path,"onUpdate:modelValue":t[12]||(t[12]=o=>s.value.active_directory_data.profile_path=o),type:"text",name:"profile_path",id:"profile_path",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cestu k profilu..."},null,8,["modelValue"])])]),e("div",Ht,[t[38]||(t[38]=e("label",{for:"script_path",class:"block text-sm font-normal leading-6 text-gray-900"},"Logon script:",-1)),e("div",Ft,[l(a(g),{modelValue:s.value.active_directory_data.script_path,"onUpdate:modelValue":t[13]||(t[13]=o=>s.value.active_directory_data.script_path=o),type:"text",name:"script_path",id:"script_path",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte logon script..."},null,8,["modelValue"])])]),e("div",Nt,[e("div",Rt,[t[39]||(t[39]=e("label",{for:"home_directory",class:"block text-sm font-normal leading-6 text-gray-900"},"Domovská složka:",-1)),e("div",Xt,[l(a(g),{modelValue:s.value.active_directory_data.home_directory,"onUpdate:modelValue":t[14]||(t[14]=o=>s.value.active_directory_data.home_directory=o),type:"text",name:"home_directory",id:"home_directory",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cestu domovské s..."},null,8,["modelValue"])])]),e("div",Jt,[l(a(se),{modelValue:U.value,"onUpdate:modelValue":t[15]||(t[15]=o=>U.value=o)},{default:c(()=>[e("div",Kt,[l(a(ae),{class:"relative w-16 text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:c(()=>[U.value?(n(),d("span",Qt,m(U.value.name),1)):(n(),d("span",Wt,"Zvolte disk...")),e("span",es,[l(a(K),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),l(X,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:c(()=>[l(a(le),{class:"absolute right-0 mt-1 max-h-60 w-46 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:c(()=>[(n(),f(a(Y),{key:"uncheck",value:null,as:"template"},{default:c(({active:o,selected:y})=>[e("li",{class:P([o?"bg-main-color-100 text-main-color-900":"text-gray-900","relative select-none py-2 pl-4 pr-10 cursor-pointer"])},[e("span",{class:P([y?"font-medium":"font-normal","block truncate"])},"Žádný",2),y?(n(),d("span",ts,[l(a(T),{class:"h-5 w-5","aria-hidden":"true"})])):p("",!0)],2)]),_:1})),(n(!0),d(O,null,J(N.value,o=>(n(),f(a(Y),{key:o.name,value:o,as:"template"},{default:c(({active:y,selected:x})=>[e("li",{class:P([y?"bg-main-color-100 text-main-color-900":"text-gray-900","relative select-none py-2 pr-10 pl-4 cursor-pointer"])},[e("span",{class:P([x?"font-medium":"font-normal","block truncate"])},m(o.name),3),x?(n(),d("span",ss,[l(a(T),{class:"h-5 w-5","aria-hidden":"true"})])):p("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])])])])):p("",!0)]),_:1}),a(G)?(n(),d("div",as,[t[42]||(t[42]=e("span",null,"New password styles",-1)),t[43]||(t[43]=e("br",null,null,-1)),e("span",null,m(k.value),1),t[44]||(t[44]=e("br",null,null,-1)),e("span",null,m(de.value),1),t[45]||(t[45]=e("br",null,null,-1)),e("span",null,m(ue.value),1),t[46]||(t[46]=e("br",null,null,-1)),t[47]||(t[47]=e("br",null,null,-1)),e("span",null,[t[40]||(t[40]=w("user: ")),e("pre",null,m(s.value),1)]),t[48]||(t[48]=e("br",null,null,-1)),e("span",null,"selected account type: "+m(r.selectedAccountType),1),t[49]||(t[49]=e("br",null,null,-1)),e("span",null,"verified email: "+m(v.value),1),t[50]||(t[50]=e("br",null,null,-1)),e("span",null,"send sms: "+m(r.sendSms),1),t[51]||(t[51]=e("br",null,null,-1)),e("span",null,"selected ou: "+m(A.value),1),t[52]||(t[52]=e("br",null,null,-1)),e("span",null,[t[41]||(t[41]=w("groups: ")),e("pre",null,m(F.value),1)]),t[53]||(t[53]=e("br",null,null,-1)),e("span",null,"account control codes: "+m(L.value),1),t[54]||(t[54]=e("br",null,null,-1)),e("span",null,"selected account control code: "+m(E.value),1)])):p("",!0)],64)}}};export{ys as default};

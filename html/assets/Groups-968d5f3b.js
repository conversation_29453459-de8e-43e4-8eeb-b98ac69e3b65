import{a as m,j as oe,o as i,c as C,w as n,e as l,h as P,d as t,u as a,z as W,b as c,t as k,l as B,y as h,E as F,T as I,f as R,n as O,F as U,H as A,m as M,x as J,M as se,B as Y,a9 as le,P as ae,D as ne,G as re,r as ie,a5 as ue,$ as de}from"./index-9d9f1067.js";import{_ as pe}from"./AppTopbar-f7fb50ba.js";import{i as Q,g as me,y as ce,o as ve}from"./index-0c6a7c95.js";import{_ as ge}from"./pagination-ccc83ede.js";import{_ as K}from"./basicModal-f66ed136.js";import{X,d as E,C as fe}from"./index-adce43bb.js";import{E as N,A as T,F as q,B as L}from"./listbox-9038424e.js";import{S as H}from"./transition-a42de4b5.js";import"./dialog-71363bda.js";import"./hidden-b1ebec83.js";import"./use-tracked-pointer-f803765e.js";import"./use-resolve-button-type-13e1cf97.js";import"./use-controllable-3025fab5.js";const xe={class:"p-6 space-y-3"},be={key:0,class:"bg-pink-200 py-2 px-3 rounded-lg inline-block mb-6"},ye={class:"mt-2"},we={class:"relative w-full"},ke={key:0,class:"block truncate"},he={key:1,class:"block truncate text-gray-400"},Ge={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Ve={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},$e={class:"relative w-full"},_e={key:0,class:"flex flex-wrap gap-2 w-full box-border"},ze={class:"text-main-color-600 bg-main-color-200 py-1 px-2 rounded-md text-sm"},Ce={key:1,class:"block truncate text-gray-400"},Ue={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Me={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},je={class:"pb-2"},Oe={class:"border-t pt-4"},Re={class:"flex items-center gap-3"},Ze={class:"flex items-center gap-3"},De={class:"border-t p-5"},Se={class:"text-right space-x-3"},Pe={__name:"createGroupModal",emits:["reloadGroups"],setup(Z,{expose:_,emit:V}){const z=V,b=m(!1),w=oe("debugModeGlobalVar"),g=m("");m([{id:1,name:"Bez nastavení"},{id:2,name:"Skupina pro hosty"},{id:3,name:"Skupina pro blokaci internetu"}]);const y=m(),s=m({}),f=m([]);function u(){b.value=!1}function p(){G(),$(),s.value={newUserGroup:!1,newVisitors:!1,selectedGroups:[]},y.value=null,b.value=!0}function G(){M.get("/api/organization-units?perpage=9999").then(r=>{g.value=r.data.data}).catch(r=>{console.log(r)})}function $(){M.get("/api/groups?perpage=99999&page=1&search=").then(r=>{f.value=r.data.data}).catch(r=>{console.log(r)})}function D(){M.post("/api/groups",{organization_unit_id:s.value.selectedOu.id,name:s.value.name,description:s.value.description,default:s.value.newUserGroup===!0?1:0,visitors:s.value.newVisitors===!0?1:0,groups:s.value.selectedGroups.length>0?s.value.selectedGroups.map(r=>r.id):null}).then(r=>{J.success(r.data.message),z("reloadGroups",!0),u()}).catch(r=>{console.log(r)})}return _({openModal:p}),(r,o)=>(i(),C(a(H),{appear:"",show:b.value,as:"template",onClose:o[11]||(o[11]=v=>u())},{default:n(()=>[l(K,{size:"sm"},{"modal-title":n(()=>o[12]||(o[12]=[P("Vytvoření nové skupiny v AD")])),"modal-close-button":n(()=>[t("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:o[0]||(o[0]=v=>u())},[l(a(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":n(()=>[l(a(W),{onSubmit:o[10]||(o[10]=v=>D())},{default:n(({values:v})=>[t("div",xe,[a(w)?(i(),c("div",be,[t("span",null,"new group: "+k(s.value),1),o[13]||(o[13]=t("br",null,null,-1)),t("span",null,"selected group type: "+k(y.value),1)])):B("",!0),t("div",null,[o[14]||(o[14]=t("label",{for:"group-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název skupiny:",-1)),t("div",ye,[l(a(h),{rules:"required",modelValue:s.value.name,"onUpdate:modelValue":o[1]||(o[1]=e=>s.value.name=e),type:"text",name:"group-name",id:"group-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název skupiny..."},null,8,["modelValue"]),l(a(F),{name:"group-name",class:"text-rose-400 text-sm block pt-1"})])]),t("div",null,[o[15]||(o[15]=t("label",{class:"block text-sm font-normal leading-6 text-gray-900"},"Organizační jednotka:",-1)),l(a(h),{rules:"required",modelValue:s.value.selectedOu,"onUpdate:modelValue":o[2]||(o[2]=e=>s.value.selectedOu=e),type:"text",name:"selectedOu",id:"selectedOu",class:"hidden"},null,8,["modelValue"]),l(a(N),{modelValue:s.value.selectedOu,"onUpdate:modelValue":o[3]||(o[3]=e=>s.value.selectedOu=e)},{default:n(()=>[t("div",we,[l(a(T),{class:"mt-2 relative w-full text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:n(()=>[s.value.selectedOu&&s.value.selectedOu.name?(i(),c("span",ke,k(s.value.selectedOu.name),1)):(i(),c("span",he,"Zvolte organizační jednotku...")),t("span",Ge,[l(a(E),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),l(I,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:n(()=>[l(a(q),{class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:n(()=>[(i(!0),c(U,null,R(g.value,e=>(i(),C(a(L),{key:e.name,value:e,as:"template"},{default:n(({active:x,selected:d})=>[t("li",{class:O([x?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[t("span",{class:O([d?"font-medium":"font-normal","block truncate"])},k(e.name),3),d?(i(),c("span",Ve,[l(a(Q),{class:"h-5 w-5","aria-hidden":"true"})])):B("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"]),l(a(F),{name:"selectedOu",class:"text-rose-400 text-sm block pt-1"})]),t("div",null,[o[16]||(o[16]=t("label",{class:"block text-sm font-normal leading-6 text-gray-900"},"Je členem skupiny:",-1)),l(a(h),{modelValue:s.value.selectedGroups,"onUpdate:modelValue":o[4]||(o[4]=e=>s.value.selectedGroups=e),type:"text",name:"selectedGroups",id:"selectedGroups",class:"hidden"},null,8,["modelValue"]),l(a(N),{modelValue:s.value.selectedGroups,"onUpdate:modelValue":o[5]||(o[5]=e=>s.value.selectedGroups=e),multiple:""},{default:n(()=>[t("div",$e,[l(a(T),{class:"mt-2 relative w-full text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:n(()=>[s.value.selectedGroups&&s.value.selectedGroups.length?(i(),c("span",_e,[(i(!0),c(U,null,R(s.value.selectedGroups,(e,x)=>(i(),c("span",ze,[(i(),c("span",{key:x},k(e.name),1))]))),256))])):(i(),c("span",Ce,"Zvolte skupiny...")),t("span",Ue,[l(a(E),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),l(I,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:n(()=>[l(a(q),{class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:n(()=>[(i(!0),c(U,null,R(f.value,e=>(i(),C(a(L),{key:e.name,value:e,as:"template"},{default:n(({active:x,selected:d})=>[t("li",{class:O([x?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[t("span",{class:O([d?"font-medium":"font-normal","block truncate"])},k(e.name),3),d?(i(),c("span",Me,[l(a(Q),{class:"h-5 w-5","aria-hidden":"true"})])):B("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"]),l(a(F),{name:"selectedGroups",class:"text-rose-400 text-sm block pt-1"})]),t("div",je,[o[17]||(o[17]=t("label",{class:"block text-sm font-normal leading-6 text-gray-900"},"Popis skupiny:",-1)),l(a(h),{as:"textarea",placeholder:"Zadejte popis skupiny...",modelValue:s.value.description,"onUpdate:modelValue":o[6]||(o[6]=e=>s.value.description=e),name:"description",id:"description",cols:"30",rows:"5",class:"mt-2 resize-none block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none"},null,8,["modelValue"])]),t("div",Oe,[o[20]||(o[20]=t("p",{class:"text-sm mb-3"},'Změnu výchozích skupin využijte v případě, že v Active Directory máte nastaveny jiné výchozí skupiny než "Domain Users" a "Domain Guests" - tyto možnosti nezmění nastavení skupin v Active Directory.',-1)),t("div",Re,[l(a(h),{name:"newUserGroup",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-main-color-600 cursor-pointer",modelValue:s.value.newUserGroup,"onUpdate:modelValue":o[7]||(o[7]=e=>s.value.newUserGroup=e),value:!s.value.newUserGroup},null,8,["modelValue","value"]),o[18]||(o[18]=t("label",{for:"newUserGroup",class:"block text-sm font-semibold leading-6 text-gray-900"},"Výchozí skupina nově založených uživatelů",-1))]),t("div",Ze,[l(a(h),{name:"newVisitors",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-main-color-600 cursor-pointer",modelValue:s.value.newVisitors,"onUpdate:modelValue":o[8]||(o[8]=e=>s.value.newVisitors=e),value:!s.value.newVisitors},null,8,["modelValue","value"]),o[19]||(o[19]=t("label",{for:"newVisitors",class:"block text-sm font-semibold leading-6 text-gray-900"},"Výchozí skupina nově založených návštěv",-1))])])]),t("div",De,[t("div",Se,[t("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:o[9]||(o[9]=A(e=>u(),["prevent"]))}," Zavřít "),o[21]||(o[21]=t("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Přidat ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},Be={class:"p-6 space-y-3"},Fe={class:"mt-2"},Ae={class:"pb-2"},Ie={class:"relative w-full"},Ee={key:0,class:"flex flex-wrap gap-2 w-full box-border"},Ne={class:"text-main-color-600 bg-main-color-200 py-1 px-2 rounded-md text-sm"},Te={key:1,class:"block truncate text-gray-400"},qe={class:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"},Le={key:0,class:"absolute inset-y-0 left-0 flex items-center pl-3 text-main-color-600"},Je={class:"border-t pt-4"},Ke={class:"flex items-center gap-3"},Xe=["value"],He={class:"flex items-center gap-3"},Qe={class:"border-t p-5"},We={class:"text-right space-x-3"},Ye={__name:"editGroupModal",props:{selectedGroup:Object},emits:["reloadGroups"],setup(Z,{expose:_,emit:V}){const z=V,b=m(""),w=m(""),g=m(!1),y=m(!1),s=m([]),f=m([]),u=m(!1),p=Z;se(()=>{var v,e,x,d,j,S;!p.selectedGroup||f.value.length===0||(b.value=((v=p.selectedGroup)==null?void 0:v.name)||"",w.value=((e=p.selectedGroup)==null?void 0:e.description)||"",g.value=((x=p.selectedGroup)==null?void 0:x.default)===1,y.value=((d=p.selectedGroup)==null?void 0:d.visitors)===1,(S=(j=p.selectedGroup)==null?void 0:j.groups)!=null&&S.length?s.value=f.value.filter(ee=>p.selectedGroup.groups.some(te=>te.id===ee.id)):s.value=[])});const G=m(!1);function $(){G.value=!1}async function D(){await o(),G.value=!0}function r(){M.post("/api/groups/"+p.selectedGroup.id+"/update",{name:b.value,description:w.value,default:g.value===!0?1:0,visitors:y.value===!0?1:0,groups:s.value.length>0?s.value.map(v=>v.id):null}).then(v=>{J.success(v.data.message),z("reloadGroups",!0),$()}).catch(v=>{console.log(v)})}async function o(){u.value=!0;try{const v=await M.get("/api/groups?perpage=99999&page=1&search=");f.value=v.data.data}catch(v){console.error(v)}finally{u.value=!1}}return _({openModal:D}),(v,e)=>(i(),C(a(H),{appear:"",show:G.value,as:"template",onClose:e[9]||(e[9]=x=>$())},{default:n(()=>[l(K,{size:"sm"},{"modal-title":n(()=>e[10]||(e[10]=[P("Úprava skupiny")])),"modal-close-button":n(()=>[t("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:e[0]||(e[0]=x=>$())},[l(a(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":n(()=>[l(a(W),{onSubmit:e[8]||(e[8]=x=>r())},{default:n(({values:x})=>[t("div",Be,[t("div",null,[e[11]||(e[11]=t("label",{for:"group-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název skupiny:",-1)),t("div",Fe,[l(a(h),{rules:"required",modelValue:b.value,"onUpdate:modelValue":e[1]||(e[1]=d=>b.value=d),type:"text",name:"group-name",id:"group-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název skupiny..."},null,8,["modelValue"]),l(a(F),{name:"group-name",class:"text-rose-400 text-sm block pt-1"})])]),t("div",Ae,[e[12]||(e[12]=t("label",{class:"block text-sm font-normal leading-6 text-gray-900"},"Popis skupiny:",-1)),l(a(h),{as:"textarea",placeholder:"Zadejte popis skupiny...",modelValue:w.value,"onUpdate:modelValue":e[2]||(e[2]=d=>w.value=d),name:"description",id:"description",cols:"30",rows:"5",class:"mt-2 resize-none block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none"},null,8,["modelValue"])]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-normal leading-6 text-gray-900"},"Je členem skupiny:",-1)),l(a(h),{modelValue:s.value,"onUpdate:modelValue":e[3]||(e[3]=d=>s.value=d),type:"text",name:"selectedGroups",id:"selectedGroups",class:"hidden"},null,8,["modelValue"]),l(a(N),{modelValue:s.value,"onUpdate:modelValue":e[4]||(e[4]=d=>s.value=d),multiple:""},{default:n(()=>[t("div",Ie,[l(a(T),{class:"mt-2 relative w-full text-left cursor-pointer block rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6"},{default:n(()=>[s.value&&s.value.length?(i(),c("span",Ee,[(i(!0),c(U,null,R(s.value,(d,j)=>(i(),c("span",Ne,[(i(),c("span",{key:j},k(d.name),1))]))),256))])):(i(),c("span",Te,"Zvolte skupiny...")),t("span",qe,[l(a(E),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})])]),_:1}),l(I,{"leave-active-class":"transition duration-100 ease-in","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:n(()=>[l(a(q),{class:"absolute z-20 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"},{default:n(()=>[(i(!0),c(U,null,R(f.value,d=>(i(),C(a(L),{key:d.name,value:d,as:"template"},{default:n(({active:j,selected:S})=>[t("li",{class:O([j?"bg-main-color-100 text-main-color-900":"text-gray-900","relative cursor-pointer select-none py-2 pl-10 pr-4"])},[t("span",{class:O([S?"font-medium":"font-normal","block truncate"])},k(d.name),3),S?(i(),c("span",Le,[l(a(fe),{class:"h-5 w-5","aria-hidden":"true"})])):B("",!0)],2)]),_:2},1032,["value"]))),128))]),_:1})]),_:1})])]),_:1},8,["modelValue"]),l(a(F),{name:"selectedGroups",class:"text-rose-400 text-sm block pt-1"})]),t("div",Je,[e[16]||(e[16]=t("p",{class:"text-sm mb-3"},'Změnu výchozích skupin využijte v případě, že v Active Directory máte nastaveny jiné výchozí skupiny než "Domain Users" a "Domain Guests" - tyto možnosti nezmění nastavení skupin v Active Directory.',-1)),t("div",Ke,[Y(t("input",{name:"newUserGroup",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-main-color-600 cursor-pointer","onUpdate:modelValue":e[5]||(e[5]=d=>g.value=d),value:!g.value},null,8,Xe),[[le,g.value]]),e[14]||(e[14]=t("label",{for:"newUserGroup",class:"block text-sm font-semibold leading-6 text-gray-900"},"Výchozí skupina nově založených uživatelů",-1))]),t("div",He,[l(a(h),{name:"newVisitors",type:"checkbox",class:"h-4 w-4 rounded border-gray-300 text-main-color-600 focus:ring-main-color-600 cursor-pointer",modelValue:y.value,"onUpdate:modelValue":e[6]||(e[6]=d=>y.value=d),value:!y.value},null,8,["modelValue","value"]),e[15]||(e[15]=t("label",{for:"newVisitors",class:"block text-sm font-semibold leading-6 text-gray-900"},"Výchozí skupina nově založených návštěv",-1))])])]),t("div",Qe,[t("div",We,[t("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:e[7]||(e[7]=A(d=>$(),["prevent"]))}," Zavřít "),e[17]||(e[17]=t("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Upravit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"]))}},et={class:"p-6"},tt={class:"border-t p-5"},ot={class:"text-right space-x-3"},st={__name:"deleteGroupModal",props:{selectedGroup:Object},emits:["reloadGroups"],setup(Z,{expose:_,emit:V}){const z=V,b=Z,w=m(!1);function g(){w.value=!1}function y(){w.value=!0}async function s(){await M.post("/api/groups/"+b.selectedGroup.id+"/delete").then(f=>{J.success(f.data.message),g(),z("reloadGroups",!0)}).catch(f=>{console.log(f)})}return _({openModal:y}),(f,u)=>(i(),C(a(H),{appear:"",show:w.value,as:"template",onClose:u[3]||(u[3]=p=>g())},{default:n(()=>[l(K,{size:"sm"},{"modal-title":n(()=>u[4]||(u[4]=[P("Odstranění skupiny")])),"modal-close-button":n(()=>[t("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:u[0]||(u[0]=p=>g())},[l(a(X),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":n(()=>[t("div",et,[t("p",null,[u[5]||(u[5]=P("Opravdu si přejete skupinu ")),t("strong",null,k(b.selectedGroup.name),1),u[6]||(u[6]=P(" odstranit?"))])]),t("div",tt,[t("div",ot,[t("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:u[1]||(u[1]=A(p=>g(),["prevent"]))}," Zavřít "),t("button",{class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",onClick:u[2]||(u[2]=A(p=>s(),["prevent"]))}," Odstranit ")])])]),_:1})]),_:1},8,["show"]))}},lt={class:"px-0"},at={class:"bg-white border border-zinc-200/70 rounded-md p-5"},nt={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-x-6 gap-y-3"},rt={class:"grow"},it={class:"flex flex-wrap gap-x-2 gap-y-2"},ut={class:"grid grid-cols-12 gap-6 mt-6"},dt={class:"w-full flex justify-between items-center mb-2 border-b pb-3"},pt={class:"text-lg font-medium"},mt={class:"flex items-center gap-2"},ct={class:"text-xs"},vt={class:"text-gray-400 break-all whitespace-normal block"},gt={class:"text-sm mt-2 bg-slate-100 p-3 w-full h-full rounded-lg"},Ut={__name:"Groups",setup(Z){const _=m(["skolasys-root","groups"]),V=ae(),z=m(),b=m(),w=m(),g=m(),y=m({}),s=m({}),f=m(""),u=m(1);ne(()=>{p()}),re(()=>V.perPage,(r,o)=>{u.value=1,p()});function p(){M.get("/api/groups/list?perpage="+V.perPage+"&page="+u.value+"&search="+f.value).then(r=>{y.value=r.data.data,s.value=r.data.meta}).catch(r=>{console.log(r)})}function G(r){g.value=r}function $(){f.value="",p()}function D(r){u.value=r,p()}return(r,o)=>{const v=ie("router-link");return i(),c(U,null,[l(pe,{breadCrumbs:_.value},{topbarButtons:n(()=>[l(v,{to:{name:"users"},class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},{default:n(()=>o[9]||(o[9]=[t("div",{class:"flex"},[t("span",null,"Zpět")],-1)])),_:1}),t("button",{onClick:o[0]||(o[0]=e=>r.$refs.createGroupRef.openModal()),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Přidat skupinu ")]),_:1},8,["breadCrumbs"]),t("div",lt,[t("div",at,[t("div",nt,[t("div",rt,[Y(t("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":o[1]||(o[1]=e=>f.value=e),onKeyup:o[2]||(o[2]=de(e=>p(),["enter"])),class:"block w-full rounded-md border-0 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[ue,f.value]])]),t("div",it,[t("div",null,[t("button",{onClick:o[3]||(o[3]=e=>$()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},o[10]||(o[10]=[t("span",null,"Restartovat",-1)]))]),t("button",{onClick:o[4]||(o[4]=e=>p()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),t("div",ut,[(i(!0),c(U,null,R(y.value,e=>(i(),c("div",{key:e.id,class:"col-span-12 md:col-span-6 xl:col-span-4 gap-x-4 bg-white border border-zinc-200/70 rounded-md p-5 flex flex-col items-start"},[t("div",dt,[t("h2",pt,k(e.name),1),t("div",mt,[l(v,{to:{name:"group-users",params:{id:e.id}}},{default:n(()=>[l(a(me),{class:"h-9 w-9 text-main-color-500 bg-main-color-100 hover:bg-main-color-200/75 duration-150 p-2 rounded-md","aria-hidden":"true",onClick:x=>(G(e),r.$refs.deleteGroupRef.openModal())},null,8,["onClick"])]),_:2},1032,["to"]),t("button",null,[l(a(ce),{class:"h-9 w-9 text-red-500 bg-red-100 hover:bg-red-200/75 duration-150 p-2 rounded-md","aria-hidden":"true",onClick:x=>(G(e),r.$refs.deleteGroupRef.openModal())},null,8,["onClick"])]),t("button",null,[l(a(ve),{class:"h-9 w-9 text-main-color-500 bg-main-color-100 hover:bg-main-color-200/75 duration-150 p-2 rounded-md","aria-hidden":"true",onClick:x=>(G(e),r.$refs.editGroupRef.openModal())},null,8,["onClick"])])])]),t("span",ct,[t("span",vt,k(e.distinguished_name||""),1)]),t("span",gt,k(e.description||""),1)]))),128))]),s.value!==null?(i(),C(ge,{key:0,meta:s.value,onSetPage:D,modelValue:u.value,"onUpdate:modelValue":o[5]||(o[5]=e=>u.value=e)},null,8,["meta","modelValue"])):B("",!0),l(Pe,{ref_key:"createGroupRef",ref:z,onReloadGroups:o[6]||(o[6]=e=>p())},null,512),l(Ye,{ref_key:"editGroupRef",ref:b,selectedGroup:g.value,onReloadGroups:o[7]||(o[7]=e=>p())},null,8,["selectedGroup"]),l(st,{ref_key:"deleteGroupRef",ref:w,selectedGroup:g.value,onReloadGroups:o[8]||(o[8]=e=>p())},null,8,["selectedGroup"])],64)}}};export{Ut as default};

import{a as c,B as L,k as w,o as t,b as a,e as l,w as k,d as e,c as d,u as s,v as f,F,q as V,s as m,t as h,E as p,x as z,U as q,h as D}from"./index-f770c8ab.js";import{e as O,k as T,L as M,E as C,a as E}from"./index-3a3ac444.js";import{A as P}from"./auth.service-1ffe105f.js";import{_ as H}from"./AppTopbar-bf151018.js";import{c as u}from"./checkPermission.service-5e6704d7.js";import"./index-0d59a700.js";const J={class:"space-y-12"},$={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},G={class:"bg-white border border-zinc-200/70 rounded-md mb-6"},K={class:"flex justify-between items-center"},Q={class:"flex items-center"},R=e("p",{class:"ml-4 text-lg text-gray-900"},"Základní informace",-1),W={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},X={class:"mt-8 grid grid-cols-1 gap-y-8 gap-x-6 sm:grid-cols-6"},Y={class:"sm:col-span-3"},ee=e("label",{for:"degree-before",class:"block text-sm font-normal leading-6 text-gray-900"},"Titul před jménem:",-1),se={class:"mt-2"},te={key:1,class:"text-gray-900"},oe={key:0},ae={key:1},le={class:"sm:col-span-3"},ne=e("label",{for:"degree-after",class:"block text-sm font-normal leading-6 text-gray-900"},"Titul za jménem:",-1),re={class:"mt-2"},ie={key:1,class:"text-gray-900"},de={key:0},ce={key:1},me={class:"sm:col-span-3"},ue=e("label",{for:"first-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Jméno:",-1),ge={class:"mt-2"},pe={key:1,class:"text-gray-900"},_e={key:0},fe={key:1},he={class:"sm:col-span-3"},ve=e("label",{for:"last-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Příjmení:",-1),ye={class:"mt-2"},xe={key:1,class:"text-gray-900"},be={key:0},we={key:1},ke={key:1,class:"h-40 flex items-center"},Ve={class:"bg-white border border-zinc-200/70 rounded-md"},je={key:0,class:"flex justify-between items-center p-5 pb-3"},Ue={class:"flex items-center"},Ze=e("p",{class:"ml-4 text-lg text-gray-900"},"Změna emailové adresy",-1),ze={key:0,type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},qe={class:"p-5 border-y border-zinc-200/70 flex items-center"},Ce=e("p",{class:"mr-4 font-medium text-sm"},"Aktuální email:",-1),Ee={class:"font-medium text-sm text-green-500"},Pe={key:1,class:"grid grid-cols-1 gap-y-6 p-5"},Se={class:"sm:col-span-2"},Ne=e("label",{for:"password",class:"block text-sm font-normal leading-6 text-gray-900"},"Aktuální heslo:",-1),Be={class:"mt-2"},Ae={class:"sm:col-span-2"},Ie=e("label",{for:"new-email",class:"block text-sm font-normal leading-6 text-gray-900"},"Zadejte novou emailovou adresu:",-1),Le={class:"mt-2"},Fe={class:"sm:col-span-2"},De=e("label",{for:"new-email-confirmation",class:"block text-sm font-normal leading-6 text-gray-900"},"Opakujte novou emailovou adresu:",-1),Oe={class:"mt-2"},Te={key:1,class:"h-40 flex items-center"},Me={key:0},He={class:"bg-white border border-zinc-200/70 rounded-md"},Je={class:"flex justify-between items-center p-5 pb-3"},$e={class:"flex items-center"},Ge=e("p",{class:"ml-4 text-lg text-gray-900"},"Změna hesla",-1),Ke=e("div",null,[e("button",{type:"submit",class:"ml-3 rounded-md bg-green-500 px-4 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-green-600"},"Uložit")],-1),Qe={class:"mt-8 grid grid-cols-1 gap-y-6 p-5 pt-0"},Re={class:"sm:col-span-2"},We=e("label",{for:"old-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Aktuální heslo",-1),Xe={class:"mt-2 relative"},Ye={class:"sm:col-span-2"},es=e("label",{for:"new-password",class:"block text-sm font-normal leading-6 text-gray-900"},"Nové heslo",-1),ss={class:"mt-2 relative"},ts={class:"sm:col-span-2"},os=e("label",{for:"new-password-confirm",class:"block text-sm font-normal leading-6 text-gray-900"},"Nové heslo (znovu)",-1),as={class:"mt-2 relative"},ls=e("div",{class:"p-5 border-t border-zinc-200/70"},[e("p",{class:"text-sm font-semibold"},"Požadavky na nové heslo:"),e("ul",{class:"list-disc text-sm pl-6 pt-4"},[e("li",{class:"pb-1"},[D("Heslo musí mít alespoň "),e("span",{class:"font-semibold"},"8 znaků")]),e("li",{class:"pb-1"},"Doporučujeme využít kombinaci malých a velkých písmen"),e("li",null,"Doporučujeme využít v hesle i číslici")])],-1),ns={key:1,class:"h-40 flex items-center"},ps={__name:"Profile",setup(rs){const o=c({}),v=c(""),j=c(""),y=c(""),x=c(""),U=c(""),b=c(""),S=c(["profile"]),_=c(!0),g=c(!1),Z=()=>{g.value=!g.value};L(()=>{_.value=!0,N(),_.value=!1});function N(){w.get("/api/users/profil").then(i=>{o.value=i.data}).catch(i=>{console.log(i)})}function B(){w.post("/api/users/profil",{first_name:o.value.first_name,middle_name:o.value.middle_name,last_name:o.value.last_name,degree_before:o.value.degree_before,degree_after:o.value.degree_after}).then(i=>{o.value=i.data.data.user,V.success(i.data.message)}).catch(i=>{V.error("Změny se nepovedlo uložit")})}function A(){w.post("/api/users/email-update",{new_email:v.value,new_email_confirmation:j.value,password:y.value}).then(i=>{P.logout(),V.success(i.data.message)}).catch(i=>{y.value=""})}function I(){w.post("/api/users/password-update",{new_password:x.value,new_password_confirmation:U.value,password:b.value}).then(i=>{P.logout(),V.success(i.data.message)}).catch(i=>{b.value=""})}return(i,n)=>(t(),a(F,null,[l(H,{breadCrumbs:S.value},{topbarButtons:k(()=>[]),_:1},8,["breadCrumbs"]),e("div",J,[e("div",$,[e("div",null,[e("div",G,[_.value?(t(),a("div",ke,[l(s(q),{class:"mx-auto text-spinner-color",size:"40"})])):(t(),d(s(z),{key:0,onSubmit:B,class:"p-5 pb-8"},{default:k(()=>[e("div",K,[e("div",Q,[l(s(O),{class:"w-7"}),R]),e("div",null,[s(u).check("profil.edit")?(t(),a("button",W,"Uložit")):f("",!0)])]),e("div",X,[e("div",Y,[ee,e("div",se,[s(u).check("profil.edit")?(t(),d(s(m),{key:0,modelValue:o.value.degree_before,"onUpdate:modelValue":n[0]||(n[0]=r=>o.value.degree_before=r),id:"degree-before",name:"degree-before",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte titul před jménem..."},null,8,["modelValue"])):(t(),a("div",te,[o.value.degree_before&&o.value.degree_before.length?(t(),a("p",oe,h(o.value.degree_before),1)):(t(),a("p",ae,"-"))]))])]),e("div",le,[ne,e("div",re,[s(u).check("profil.edit")?(t(),d(s(m),{key:0,modelValue:o.value.degree_after,"onUpdate:modelValue":n[1]||(n[1]=r=>o.value.degree_after=r),id:"degree-after",name:"degree-after",type:"text",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte titul za jménem..."},null,8,["modelValue"])):(t(),a("div",ie,[o.value.degree_after&&o.value.degree_after.length?(t(),a("p",de,h(o.value.degree_after),1)):(t(),a("p",ce,"-"))]))])]),e("div",me,[ue,e("div",ge,[s(u).check("profil.edit")?(t(),d(s(m),{key:0,rules:"required|minLength:2|maxLength:50",modelValue:o.value.first_name,"onUpdate:modelValue":n[2]||(n[2]=r=>o.value.first_name=r),type:"text",name:"first-name",id:"first-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte jméno.."},null,8,["modelValue"])):(t(),a("div",pe,[o.value.first_name&&o.value.first_name.length?(t(),a("p",_e,h(o.value.first_name),1)):(t(),a("p",fe,"-"))])),l(s(p),{name:"first-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",he,[ve,e("div",ye,[s(u).check("profil.edit")?(t(),d(s(m),{key:0,rules:"required|minLength:2|maxLength:50",modelValue:o.value.last_name,"onUpdate:modelValue":n[3]||(n[3]=r=>o.value.last_name=r),type:"text",name:"last-name",id:"last-name",autocomplete:"family-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte Příjmení.."},null,8,["modelValue"])):(t(),a("div",xe,[o.value.last_name&&o.value.last_name.length?(t(),a("p",be,h(o.value.last_name),1)):(t(),a("p",we,"-"))])),l(s(p),{name:"last-name",class:"text-rose-400 text-sm block pt-1"})])])])]),_:1}))]),e("div",Ve,[_.value?(t(),a("div",Te,[l(s(q),{class:"mx-auto text-spinner-color",size:"40"})])):(t(),d(s(z),{key:0,onSubmit:A},{default:k(()=>[s(u).check("profil.change_email")?(t(),a("div",je,[e("div",Ue,[l(s(T),{class:"w-7"}),Ze]),e("div",null,[s(u).check("profil.change_email")?(t(),a("button",ze,"Uložit")):f("",!0)])])):f("",!0),e("div",qe,[Ce,e("p",Ee,h(o.value.email),1)]),s(u).check("profil.change_email")?(t(),a("div",Pe,[e("div",Se,[Ne,e("div",Be,[l(s(m),{rules:"required",modelValue:y.value,"onUpdate:modelValue":n[4]||(n[4]=r=>y.value=r),id:"password",name:"password",type:"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte aktuální heslo..."},null,8,["modelValue"]),l(s(p),{name:"password",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Ae,[Ie,e("div",Le,[l(s(m),{rules:"required|email",modelValue:v.value,"onUpdate:modelValue":n[5]||(n[5]=r=>v.value=r),id:"new-email",name:"new-email",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte novou emailovou adresu..."},null,8,["modelValue"]),l(s(p),{name:"new-email",class:"text-rose-400 text-sm block pt-1"})])]),e("div",Fe,[De,e("div",Oe,[l(s(m),{rules:"required|email|isEqual:"+v.value,modelValue:j.value,"onUpdate:modelValue":n[6]||(n[6]=r=>j.value=r),id:"new-email-confirmation",name:"new-email-confirmation",type:"email",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte novou emailovou adresu..."},null,8,["rules","modelValue"]),l(s(p),{name:"new-email-confirmation",class:"text-rose-400 text-sm block pt-1"})])])])):f("",!0)]),_:1}))])]),s(u).check("profil.change_password")?(t(),a("div",Me,[e("div",He,[_.value?(t(),a("div",ns,[l(s(q),{class:"mx-auto text-spinner-color",size:"40"})])):(t(),d(s(z),{key:0,onSubmit:I},{default:k(()=>[e("div",Je,[e("div",$e,[l(s(M),{class:"w-7"}),Ge]),Ke]),e("div",Qe,[e("div",Re,[We,e("div",Xe,[l(s(m),{rules:"required",modelValue:b.value,"onUpdate:modelValue":n[7]||(n[7]=r=>b.value=r),id:"old-password",name:"old-password",type:g.value?"text":"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte aktuální heslo..."},null,8,["modelValue","type"]),l(s(p),{name:"old-password",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:Z},[g.value?(t(),d(s(C),{key:0,name:"eye-off",class:"h-5 w-5"})):(t(),d(s(E),{key:1,name:"eye",class:"h-5 w-5"}))])])]),e("div",Ye,[es,e("div",ss,[l(s(m),{rules:"required|password",modelValue:x.value,"onUpdate:modelValue":n[8]||(n[8]=r=>x.value=r),id:"new-password",name:"new-password",type:g.value?"text":"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte nové heslo..."},null,8,["modelValue","type"]),l(s(p),{name:"new-password",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:Z},[g.value?(t(),d(s(C),{key:0,name:"eye-off",class:"h-5 w-5"})):(t(),d(s(E),{key:1,name:"eye",class:"h-5 w-5"}))])])]),e("div",ts,[os,e("div",as,[l(s(m),{rules:"required|password|isEqual:"+x.value,modelValue:U.value,"onUpdate:modelValue":n[9]||(n[9]=r=>U.value=r),id:"new-password-confirm",name:"new-password-confirm",type:g.value?"text":"password",class:"block w-full rounded-lg border-0 py-2 px-3 outline-none text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Opakujte nové heslo..."},null,8,["rules","modelValue","type"]),l(s(p),{name:"new-password-confirm",class:"text-rose-400 text-sm block pt-1"}),e("button",{type:"button",class:"absolute top-2.5 right-0 pr-3 flex items-center text-gray-400",onClick:Z},[g.value?(t(),d(s(C),{key:0,name:"eye-off",class:"h-5 w-5"})):(t(),d(s(E),{key:1,name:"eye",class:"h-5 w-5"}))])])])]),ls]),_:1}))])])):f("",!0)])])],64))}};export{ps as default};

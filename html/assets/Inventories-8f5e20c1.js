import{a as d,j as H,I as le,o,c as V,w as u,e as l,h as re,d as e,u as n,C as $,k as P,q as X,H as W,B as Y,r as G,b as a,v as g,F,f as Z,n as S,t as w,L as de,T as ue}from"./index-0d8d3833.js";import{_ as ce}from"./AppTopbar-2155fa89.js";import{$ as me,i as ve,W as pe}from"./index-5b2a5028.js";import{X as z,e as J,d as fe}from"./index-cf95b4f0.js";import{_ as ge}from"./pagination-a853c4ff.js";import{c as y}from"./checkPermission.service-455e2b2e.js";import{_ as ie}from"./basicModal-0e3662fb.js";import{S as A}from"./transition-4cce3ae4.js";import{d as ee}from"./debounce-c6fdd1f3.js";import{N as te,$ as se,K as oe,U as ae,_ as ne}from"./combobox-9ff0ca81.js";import{S as ye,b as xe,M as be,g as he}from"./menu-b78380b8.js";import"./listbox-26696037.js";import"./hidden-b3b94d42.js";import"./use-tracked-pointer-9b5e4d20.js";import"./use-resolve-button-type-370dbe2f.js";import"./use-controllable-9e1e9dcb.js";import"./dialog-a8268362.js";import"./use-tree-walker-b3b264f7.js";const _e={class:"border-t p-5"},ke={class:"text-right space-x-3"},we={__name:"deleteInventoryModal",props:{inventory:{type:Object,required:!0}},emits:["reloadInventories"],setup(q,{expose:M,emit:L}){const U=q,C=L,x=d(!1);H("debugModeGlobalVar"),d(null),d("");const f=d(!1);le(()=>{});function c(){x.value=!1}function _(){x.value=!0}async function I(){y.check("inventories.delete")||y.check("property.master")?await P.post("api/inventories/"+U.inventory.id+"/delete").then(v=>{X.success(v.data.message)}).catch(v=>{X.error(v.message)}):f.value=!1,c(),C("reloadInventories",!0)}return M({openModal:_}),(v,i)=>(o(),V(n(A),{appear:"",show:x.value,as:"template",onClose:i[3]||(i[3]=b=>c())},{default:u(()=>[l(ie,{size:"sm"},{"modal-title":u(()=>i[4]||(i[4]=[re("Smazat inventuru")])),"modal-close-button":u(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:i[0]||(i[0]=b=>c())},[l(n(z),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":u(()=>[i[5]||(i[5]=e("div",{class:"p-6 gap-4"},[e("p",null,"Opravdu si přejete inventuru smazat?")],-1)),e("div",_e,[e("div",ke,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:i[1]||(i[1]=$(b=>c(),["prevent"]))}," Zavřít "),e("button",{onClick:i[2]||(i[2]=$(b=>I(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}},$e={class:"p-6 grid grid-cols-2 gap-3"},Ce={class:"relative"},Ie={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},Se={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},Ve={key:0},ze={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Me={key:1,class:"h-20 max-h-20 overflow-hidden"},Le={key:2},Ue={class:"relative"},Ne={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},Re={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},je={key:0},Ee={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Pe={key:1,class:"h-20 max-h-20 overflow-hidden"},Fe={key:2},De={class:"border-t p-5"},Oe={class:"text-right space-x-3"},Be={__name:"createInventoryModal",emits:["reloadProtocols"],setup(q,{expose:M,emit:L}){const U=L,C=d(!1);H("debugModeGlobalVar");const x=d(!1),f=d(null),c=d(null),_=d([]),I=d([]),v=d(""),i=d(""),b=d(!1),h=d(!1),D=W(()=>v.value===""?_.value:_.value.filter(m=>`${m.first_name} ${m.last_name}`.toLowerCase().replace(/\s+/g,"").includes(v.value.toLowerCase().replace(/\s+/g,"")))),O=W(()=>i.value===""?I.value:I.value.filter(m=>m.name.toLowerCase().replace(/\s+/g,"").includes(i.value.toLowerCase().replace(/\s+/g,"")))),B=ee(async()=>{try{const m=await P.get("/api/users?page=1&perpage=50&search="+v.value);_.value=m.data.data}catch(m){console.error(m)}finally{b.value=!1}},300),T=ee(async()=>{try{const m=await P.get("/api/rooms?page=1&perpage=50&search="+i.value);I.value=m.data.data}catch(m){console.error(m)}finally{h.value=!1}},300);Y(v,()=>{b.value=!0,B()}),Y(i,()=>{h.value=!0,T()});function p(){C.value=!1}function r(){B(),T(),C.value=!0,f.value="",c.value=""}async function K(){y.check("inventories.create")||y.check("property.master")?await P.post("api/inventories",{user_id:f.value.id,room_id:c.value.id}).then(m=>{X.success(m.data.message)}).catch(m=>{console.log(m)}):x.value=!1,p(),U("reloadInventories",!0)}return M({openModal:r}),(m,t)=>{const N=G("VueSpinner"),Q=G("CheckIcon");return o(),V(n(A),{appear:"",show:C.value,as:"template",onClose:t[11]||(t[11]=s=>p())},{default:u(()=>[l(ie,{size:"sm"},{"modal-title":u(()=>t[12]||(t[12]=[re("Vytvoření nové inventury majetku")])),"modal-close-button":u(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:t[0]||(t[0]=s=>p())},[l(n(z),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":u(()=>[e("div",null,[e("div",$e,[e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-normal leading-6 text-gray-900 pb-1"},"Přiřazený uživatel:",-1)),l(n(te),{modelValue:f.value,"onUpdate:modelValue":t[4]||(t[4]=s=>f.value=s)},{default:u(()=>[e("div",Ce,[e("div",Ie,[l(n(se),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte uživatele...",displayValue:s=>s?`${s.first_name||""} ${s.middle_name?s.middle_name+" ":""}${s.last_name||""}`.trim():"",onChange:t[1]||(t[1]=s=>v.value=s.target.value)},null,8,["displayValue"]),f.value&&f.value.first_name?(o(),a("div",Se,[e("button",{onClick:t[2]||(t[2]=$(s=>f.value=null,["prevent"])),type:"button"},[l(n(z),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):g("",!0),l(n(oe),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:u(()=>[l(n(J),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(n(A),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:t[3]||(t[3]=s=>v.value="")},{default:u(()=>[l(n(ae),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:u(()=>[b.value?g("",!0):(o(),a("div",Ve,[D.value.length===0&&v.value!==""?(o(),a("div",ze," Žádný uživatel nenalezen. ")):g("",!0)])),b.value?(o(),a("div",Me,[l(N,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(o(),a("div",Le,[(o(!0),a(F,null,Z(D.value,s=>(o(),V(n(ne),{as:"template",key:s.id,value:s},{default:u(({active:k})=>{var R,j,E;return[e("li",{class:S(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":k,"text-gray-900":!k}])},[e("span",{class:S(["block truncate",{"font-medium":((R=f.value)==null?void 0:R.id)==s.id,"font-normal":((j=f.value)==null?void 0:j.id)!=s.id}])},w((s==null?void 0:s.first_name)+" "+(s!=null&&s.middle_name?(s==null?void 0:s.middle_name)+" ":"")+(s==null?void 0:s.last_name)),3),((E=f.value)==null?void 0:E.id)==s.id?(o(),a("span",{key:0,class:S(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":k,"text-main-color-600":!k}])},[l(Q,{class:"h-5 w-5","aria-hidden":"true"})],2)):g("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])]),e("div",null,[t[14]||(t[14]=e("label",{class:"block text-sm font-normal leading-6 text-gray-900 pb-1"},"Přiřazená místnost",-1)),l(n(te),{modelValue:c.value,"onUpdate:modelValue":t[8]||(t[8]=s=>c.value=s)},{default:u(()=>[e("div",Ue,[e("div",Ne,[l(n(se),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte místnost...",displayValue:s=>s?`${s.name}`.trim():"",onChange:t[5]||(t[5]=s=>i.value=s.target.value)},null,8,["displayValue"]),c.value&&c.value.name?(o(),a("div",Re,[e("button",{onClick:t[6]||(t[6]=$(s=>c.value=null,["prevent"])),type:"button"},[l(n(z),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):g("",!0),l(n(oe),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:u(()=>[l(n(J),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(n(A),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:t[7]||(t[7]=s=>i.value="")},{default:u(()=>[l(n(ae),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:u(()=>[h.value?g("",!0):(o(),a("div",je,[O.value.length===0&&i.value!==""?(o(),a("div",Ee," Žádná místnost nenalezena. ")):g("",!0)])),h.value?(o(),a("div",Pe,[l(N,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(o(),a("div",Fe,[(o(!0),a(F,null,Z(O.value,s=>(o(),V(n(ne),{as:"template",key:s.id,value:s},{default:u(({active:k})=>{var R,j,E;return[e("li",{class:S(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":k,"text-gray-900":!k}])},[e("span",{class:S(["block truncate",{"font-medium":((R=c.value)==null?void 0:R.id)==s.id,"font-normal":((j=c.value)==null?void 0:j.id)!=s.id}])},w(s==null?void 0:s.name),3),((E=c.value)==null?void 0:E.id)==s.id?(o(),a("span",{key:0,class:S(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":k,"text-main-color-600":!k}])},[l(Q,{class:"h-5 w-5","aria-hidden":"true"})],2)):g("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])]),e("div",De,[e("div",Oe,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:t[9]||(t[9]=$(s=>p(),["prevent"]))}," Zavřít "),e("button",{onClick:t[10]||(t[10]=$(s=>K(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Vytvořit ")])])])]),_:1})]),_:1},8,["show"])}}},Te={class:"space-y-6"},Ae={class:"px-0"},Ge={class:"bg-white border border-zinc-200/70 rounded-md p-5"},Ze={class:"sm:flex justify-between items-center gap-4"},qe={class:"grid grid-cols-6 gap-4"},Ke={class:"col-span-2"},Qe={class:"w-44 text-left"},Xe={key:0,class:"text-gray-900"},Ye={key:1,class:"text-gray-400"},He=["onClick"],We={class:"flex items-center gap-4"},Je={class:"flow-root bg-white border border-zinc-200/70 rounded-md"},et={class:"sm:-mx-6 lg:-mx-8"},tt={class:"inline-block min-w-full align-middle sm:px-6 lg:px-8"},st={key:0,class:"min-w-full divide-y divide-gray-200"},ot={key:0,scope:"col",class:"pr-5 pl-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"},at={key:0,class:"divide-y divide-gray-200"},nt={class:"whitespace-nowrap pl-5 pr-3 text-sm text-gray-600"},lt={key:0},rt={key:0},it={key:1,class:"text-red-600"},dt={key:1},ut={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},ct={key:0},mt={key:0},vt={key:1,class:"text-red-600"},pt={key:1},ft={class:"whitespace-nowrap py-4 px-3 text-sm text-gray-600"},gt={key:0},yt={key:1},xt={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-600"},bt={key:0},ht={key:0,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},_t={class:"flex"},kt={key:1,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},wt={class:"flex"},$t={key:2,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Ct={class:"flex"},It={key:1},St={key:0},Vt={class:"flex justify-end gap-2 pr-5 pl-3"},zt=["onClick"],Mt={key:1},Xt={__name:"Inventories",setup(q){const M=d(""),L=d("");d();const U=de();H("debugModeGlobalVar");const C=d(["inventories"]),x=d(!1),f=d(""),c=d(1),_=d({}),I=d([{id:"OPEN",name:"Čeká na potvrzení"},{id:"SUCCESSFULLY_CLOSED",name:"Úspěšně uzavřeno"},{id:"FAILED_CLOSED",name:"Neúspěšně uzavřeno"}]),v=d({}),i=d({id:""}),b=d();le(()=>{b.value="",h()}),Y(()=>U.perPage,(p,r)=>{x.value=!0,c.value=1,h()});async function h(){x.value=!0,await P.get("/api/inventories?page="+c.value+"&search="+f.value+"&state="+i.value.id).then(p=>{v.value=p.data.data,_.value=p.data.meta}).catch(p=>{console.log(p)}),x.value=!1}function D(p){b.value=p}function O(p){c.value=p,h()}function B(){x.value=!0,c.value=1,f.value="",i.value={id:""},h()}function T(){x.value=!0,c.value=1,h()}return(p,r)=>{const K=G("router-link"),m=G("VueSpinner");return o(),a(F,null,[l(ce,{breadCrumbs:C.value},{topbarButtons:u(()=>[n(y).check("inventories.create")||n(y).check("property.master")?(o(),a("button",{key:0,onClick:r[0]||(r[0]=$(t=>p.$refs.createInventoryRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nová inventura ")):g("",!0)]),_:1},8,["breadCrumbs"]),e("div",Te,[e("div",Ae,[e("div",Ge,[e("div",Ze,[e("div",qe,[e("div",Ke,[l(n(he),{as:"div",class:"relative inline-block text-left"},{default:u(()=>[e("div",null,[l(n(ye),{class:"inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:u(()=>[e("div",Qe,[i.value&&i.value.name?(o(),a("span",Xe,w(i.value.name),1)):(o(),a("span",Ye,"Stav inventury..."))]),l(n(fe),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"})]),_:1})]),l(ue,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:u(()=>[l(n(xe),{class:"absolute right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:u(()=>[(o(!0),a(F,null,Z(I.value,t=>(o(),a("div",{key:t.id,class:"px-1 py-1"},[l(n(be),null,{default:u(({active:N})=>[e("button",{onClick:Q=>i.value=t,class:S([N?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},w(t.name),11,He)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})])]),e("div",We,[e("button",{onClick:r[1]||(r[1]=t=>B()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},r[6]||(r[6]=[e("span",null,"Resetovat",-1)])),e("button",{onClick:r[2]||(r[2]=t=>T()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",Je,[e("div",et,[e("div",tt,[x.value==!1?(o(),a("table",st,[e("thead",null,[e("tr",null,[r[7]||(r[7]=e("th",{scope:"col",class:"pl-5 pr-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Přiřazený uživatel ",-1)),r[8]||(r[8]=e("th",{scope:"col",class:"px-3 py-4 text-left text-sm font-semibold text-gray-900 bg-gray-100/70"}," Přiřazená místnost ",-1)),r[9]||(r[9]=e("th",{scope:"col",class:"py-4 px-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Typ ",-1)),r[10]||(r[10]=e("th",{scope:"col",class:"py-4 px-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"}," Stav ",-1)),n(y).check("inventories.edit")||n(y).check("items.delete")?(o(),a("th",ot)):g("",!0)])]),v.value&&v.value.length?(o(),a("tbody",at,[(o(!0),a(F,null,Z(v.value,t=>(o(),a("tr",{key:t.id},[e("td",nt,[t.user?(o(),a("span",lt,[t.user.full_name?(o(),a("span",rt,w(t.user.full_name),1)):(o(),a("span",it,w(t.user.email)+" - Uživateli chybí údaje",1))])):(o(),a("span",dt,"-"))]),e("td",ut,[t.room?(o(),a("span",ct,[t.room.name?(o(),a("span",mt,w(t.room.name),1)):(o(),a("span",vt,"Místnosti chybí jméno"))])):(o(),a("span",pt,"-"))]),e("td",ft,[t.type?(o(),a("span",gt,w(t.type),1)):(o(),a("span",yt,"-"))]),e("td",xt,[t.state?(o(),a("div",bt,[t.state=="OPEN"?(o(),a("div",ht,[e("div",_t,[l(n(me),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),r[11]||(r[11]=e("span",null,"Čeká na potvrzení",-1))])])):t.state=="SUCCESSFULLY_CLOSED"?(o(),a("div",kt,[e("div",wt,[l(n(ve),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),r[12]||(r[12]=e("span",null,"Úspěšně uzavřeno",-1))])])):t.state=="FAILED_CLOSED"?(o(),a("div",$t,[e("div",Ct,[l(n(z),{class:"h-4 w-4 text-red-600 mr-1","aria-hidden":"true"}),r[13]||(r[13]=e("span",null,"Neúspěšně uzavřeno",-1))])])):g("",!0)])):(o(),a("span",It,"-"))]),n(y).check("inventories.process")||n(y).check("inventories.delete")||n(y).check("property.master")?(o(),a("td",St,[e("div",Vt,[l(K,{to:{name:"inventories-detail",params:{id:t.id}}},{default:u(()=>[l(n(pe),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})]),_:2},1032,["to"]),(n(y).check("inventories.delete")||n(y).check("property.master"))&&t.state=="OPEN"?(o(),a("button",{key:0,onClick:$(N=>(D(t),p.$refs.deleteInventoryRef.openModal()),["prevent"])},[l(n(z),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,zt)):g("",!0)])])):g("",!0)]))),128))])):(o(),a("tbody",Mt,r[14]||(r[14]=[e("tr",null,[e("td",{colspan:"7",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné inventury.")],-1)])))])):(o(),V(m,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),_.value!==null?(o(),V(ge,{key:0,meta:_.value,onSetPage:O,modelValue:c.value,"onUpdate:modelValue":r[3]||(r[3]=t=>c.value=t)},null,8,["meta","modelValue"])):g("",!0)])]),l(Be,{ref_key:"createInventoryRef",ref:L,onReloadInventories:r[4]||(r[4]=t=>h())},null,512),l(we,{ref_key:"deleteInventoryRef",ref:M,inventory:b.value,onReloadInventories:r[5]||(r[5]=t=>h())},null,8,["inventory"])],64)}}};export{Xt as default};

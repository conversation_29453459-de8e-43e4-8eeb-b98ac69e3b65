import{a as d,j as me,q as De,K as fe,r as Ie,o as t,c as E,w as c,e as l,h as N,d as e,u as s,z as Ne,y as F,E as X,b as o,t as x,a3 as we,H as D,l as y,F as _,f as ee,n as A,m as K,x as ze,D as Se,G as ye,O as Be,B as je,a4 as Fe,_ as qe,T as Ke,a8 as Qe}from"./index-cab53f7d.js";import{_ as He}from"./AppTopbar-2a35dfdd.js";import{R as Oe,a0 as Te,i as ue,L as Je,a1 as Ye,A as Xe,g as We,$ as et,o as tt,k as at}from"./index-8f3a2088.js";import{X as Q,d as ot,e as Me}from"./index-0317b1f5.js";import{_ as st}from"./pagination-384e6659.js";import{c as Y}from"./checkPermission.service-d20d14d2.js";import{d as Re}from"./debounce-4cb23abc.js";import{_ as $e}from"./basicModal-4aa23279.js";import{S as Le}from"./vue-tailwind-datepicker-3dc93912.js";/* empty css             */import{N as be,$ as xe,K as ke,U as he,_ as _e}from"./combobox-6d43725e.js";import{S as re}from"./transition-2588fa9b.js";import{S as lt,b as nt,M as it,g as rt}from"./menu-850c33d3.js";import"./listbox-15f8154e.js";import"./hidden-cf911bf3.js";import"./use-tracked-pointer-9f23e63e.js";import"./use-resolve-button-type-8037d2d3.js";import"./use-controllable-4c28b367.js";import"./dialog-cd0c3a73.js";import"./use-tree-walker-48962991.js";const dt="/assets/barcodeIcon-2fc825e0.svg",ut="/assets/barcodeIconWhite-4a7321ad.svg",mt={class:"p-6 grid grid-cols-2 gap-4"},ct={class:"mt-2"},vt={class:"mt-2"},pt={class:"mt-2"},gt={class:"mt-2"},ft={class:"mt-2"},yt={class:"rounded-l-full"},bt={class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},xt={class:"flex items-center w-full"},kt={class:"w-6 h-6"},ht={class:"flex-1"},_t={key:0,class:"text-gray-900"},wt={key:1,class:"text-gray-400"},$t={class:"mt-2"},Ct={class:"relative"},It={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},zt={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},Vt={key:0},Mt={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Dt={key:1,class:"h-20 max-h-20 overflow-hidden"},St={key:2},Ut={class:"col-span-2"},jt={class:"mt-2"},Rt={key:0,class:"col-span-2"},Nt=["for"],Ot={class:"mt-2"},Tt={class:"border-t p-5"},Yt={class:"text-right space-x-3"},Lt={__name:"createItemModal",emits:["reloadItems"],setup(le,{expose:te,emit:oe}){const H=oe,G=d(!1);me("debugModeGlobalVar");const O=d(null),v=d(null),z=d(null),j=d(""),C=d(null),T=d(null),w=d(null),n=De(),g=d(null),b=d(null),L=d(""),ie=d(!1),S=d([]),M=d(!1),I=d({date:"YYYY-MM-DD",month:"MM"}),ne=fe(()=>L.value===""?g.value:g.value.filter($=>$.name.toLowerCase().replace(/\s+/g,"").includes(L.value.toLowerCase().replace(/\s+/g,""))));function P(){G.value=!1}function J(){G.value=!0,O.value="",v.value=null,z.value=null,j.value="",T.value="",w.value="",C.value="",b.value=null,de(),n.custom_columns&&n.custom_columns.length&&(S.value=n.custom_columns.filter($=>$.enable===1).map($=>({...$,value:""})))}async function de(){await K.get("/api/accounting-categories?page=1&perpage=99999").then($=>{g.value=$.data.data}).catch($=>{console.log($)})}async function ce(){var $;if(Y.check("items.create")||Y.check("property.master")){const u={};S.value.forEach(r=>{u[r.name]=r.value||""});const k={name:O.value,count:v.value,price:T.value,invoice_number:z.value,buyed_at:j.value,purchased_from:C.value,description:w.value,accounting_category_id:(($=b==null?void 0:b.value)==null?void 0:$.id)||"",...u};await K.post("api/items",k).then(r=>{ze.success(r.data.message)}).catch(r=>{console.log(r)})}else M.value=!1;P(),H("reloadItems",!0)}return te({openModal:J}),($,u)=>{const k=Ie("VueSpinner");return t(),E(s(re),{appear:"",show:G.value,as:"template",onClose:u[14]||(u[14]=r=>P())},{default:c(()=>[l($e,{size:"md"},{"modal-title":c(()=>u[15]||(u[15]=[N("Vytvoření nové položky")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:u[0]||(u[0]=r=>P())},[l(s(Q),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[l(s(Ne),{onSubmit:u[13]||(u[13]=r=>ce())},{default:c(({values:r})=>[e("div",mt,[e("div",null,[u[16]||(u[16]=e("label",{for:"item-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název položky:",-1)),e("div",ct,[l(s(F),{rules:"required",modelValue:O.value,"onUpdate:modelValue":u[1]||(u[1]=m=>O.value=m),type:"text",name:"item-name",id:"item-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název položky..."},null,8,["modelValue"]),l(s(X),{name:"item-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[u[17]||(u[17]=e("label",{for:"item-count",class:"block text-sm font-normal leading-6 text-gray-900"},"Počet položek:",-1)),e("div",vt,[l(s(F),{rules:"required|minMax:1,99999",modelValue:v.value,"onUpdate:modelValue":u[2]||(u[2]=m=>v.value=m),type:"number",name:"item-count",id:"item-count",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte počet položek..."},null,8,["modelValue"]),l(s(X),{name:"item-count",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[u[18]||(u[18]=e("label",{for:"item-price",class:"block text-sm font-normal leading-6 text-gray-900"},"Cena za kus:",-1)),e("div",pt,[l(s(F),{rules:"minMax:1,9999999999",modelValue:T.value,"onUpdate:modelValue":u[3]||(u[3]=m=>T.value=m),type:"number",name:"item-price",id:"item-price",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cenu za kus..."},null,8,["modelValue"]),l(s(X),{name:"item-price",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[u[19]||(u[19]=e("label",{for:"item-invoice",class:"block text-sm font-normal leading-6 text-gray-900"},"Číslo faktury:",-1)),e("div",gt,[l(s(F),{modelValue:z.value,"onUpdate:modelValue":u[4]||(u[4]=m=>z.value=m),type:"text",name:"item-invoice",id:"item-invoice",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte číslo faktury..."},null,8,["modelValue"]),l(s(X),{name:"item-invoice",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[u[21]||(u[21]=e("label",{for:"item-buyed-at",class:"block text-sm font-normal leading-6 text-gray-900"},"Datum zakoupení:",-1)),e("div",ft,[l(s(F),{name:"blockedTimetableDate"},{default:c(({handleChange:m,value:V})=>[l(s(Le),{name:"blockedTimetableDate",i18n:"cs","as-single":"",shortcuts:!1,modelValue:j.value,"onUpdate:modelValue":[u[5]||(u[5]=p=>j.value=p),m],formatter:I.value,placeholder:"Zvolte datum zakoupení..."},{default:c(({clear:p})=>[e("div",null,[e("div",yt,[e("button",bt,[e("div",xt,[e("div",kt,[j.value?(t(),E(s(Q),{key:0,onClick:p,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(t(),E(s(Oe),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",ht,[j.value?(t(),o("span",_t,[e("span",null,x(s(we)(j.value).format("DD.MM.YYYY")),1)])):(t(),o("span",wt,u[20]||(u[20]=[e("span",null,"Zvolte datum zakoupení...",-1)])))])])])])])]),_:2},1032,["modelValue","onUpdate:modelValue","formatter"])]),_:1})])]),e("div",null,[u[22]||(u[22]=e("label",{for:"item-buyed-place",class:"block text-sm font-normal leading-6 text-gray-900"},"Pořízeno z:",-1)),e("div",$t,[l(s(F),{modelValue:C.value,"onUpdate:modelValue":u[6]||(u[6]=m=>C.value=m),type:"text",name:"item-buyed-place",id:"item-buyed-place",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte pořízeno z..."},null,8,["modelValue"]),l(s(X),{name:"item-buyed-place",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[e("div",null,[u[23]||(u[23]=e("label",{for:"item-account-category",class:"block text-sm font-normal leading-6 text-gray-900"},"Účetní druh majetku:",-1)),l(s(be),{modelValue:b.value,"onUpdate:modelValue":u[10]||(u[10]=m=>b.value=m)},{default:c(()=>[e("div",Ct,[e("div",It,[l(s(xe),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte druh majetku...",displayValue:m=>m?`${m.name}`.trim():"",onChange:u[7]||(u[7]=m=>L.value=m.target.value)},null,8,["displayValue"]),b.value&&b.value.name?(t(),o("div",zt,[e("button",{onClick:u[8]||(u[8]=D(m=>b.value=null,["prevent"])),type:"button"},[l(s(Q),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0),l(s(ke),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:c(()=>[l(s(Te),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(s(re),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:u[9]||(u[9]=m=>L.value="")},{default:c(()=>[l(s(he),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:c(()=>[ie.value?y("",!0):(t(),o("div",Vt,[L.value.length===0&&L.value!==""?(t(),o("div",Mt," Žádný druh majetku nenalezen. ")):y("",!0)])),ie.value?(t(),o("div",Dt,[l(k,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(t(),o("div",St,[(t(!0),o(_,null,ee(ne.value,m=>(t(),E(s(_e),{as:"template",key:m.id,value:m},{default:c(({active:V})=>{var p,R,q;return[e("li",{class:A(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":V,"text-gray-900":!V}])},[e("span",{class:A(["block truncate",{"font-medium":((p=b.value)==null?void 0:p.id)==m.id,"font-normal":((R=b.value)==null?void 0:R.id)!=m.id}])},x(m==null?void 0:m.name),3),((q=b.value)==null?void 0:q.id)==m.id?(t(),o("span",{key:0,class:A(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":V,"text-main-color-600":!V}])},[l(s(ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):y("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])]),e("div",Ut,[u[24]||(u[24]=e("label",{for:"item-description",class:"block text-sm font-normal leading-6 text-gray-900"},"Poznámka:",-1)),e("div",jt,[l(s(F),{modelValue:w.value,"onUpdate:modelValue":u[11]||(u[11]=m=>w.value=m),as:"textarea",name:"item-description",id:"item-description",cols:"30",rows:"3",class:"resize-none block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte poznámku..."},null,8,["modelValue"]),l(s(X),{name:"item-description",class:"text-rose-400 text-sm block pt-1"})])]),S.value.length?(t(!0),o(_,{key:0},ee(S.value,(m,V)=>(t(),o("div",{key:m.id},[m.enable?(t(),o("div",Rt,[e("label",{for:"custom_column"+V,class:"block text-sm font-normal leading-6 text-gray-900"},x(m.custom_name||m.name)+": ",9,Nt),e("div",Ot,[l(s(F),{modelValue:S.value[V].value,"onUpdate:modelValue":p=>S.value[V].value=p,type:"text",name:"custom_column"+V,id:"custom_column"+V,class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte "+(m.custom_name?m.custom_name:m.name)+"..."},null,8,["modelValue","onUpdate:modelValue","name","id","placeholder"]),l(s(X),{name:"custom_column"+V,class:"text-rose-400 text-sm block pt-1"},null,8,["name"])])])):y("",!0)]))),128)):y("",!0)]),e("div",Tt,[e("div",Yt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:u[12]||(u[12]=D(m=>P(),["prevent"]))}," Zavřít "),u[25]||(u[25]=e("button",{type:"submit",class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600"}," Vytvořit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"])}}},Pt={class:"border-t p-5"},Zt={class:"text-right space-x-3"},At={__name:"discardItemModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadItems"],setup(le,{expose:te,emit:oe}){const H=le,G=oe,O=d(!1);me("debugModeGlobalVar");const v=d(null),z=d(""),j=d(!1);Se(()=>{});function C(){O.value=!1}function T(){O.value=!0,v.value="",z.value=null}async function w(){Y.check("items.discard")||Y.check("property.master")?(z.value=parseInt(z.value),await K.post("api/items/"+H.selectedItem.id+"/discard").then(n=>{ze.success(n.data.message)}).catch(n=>{console.log(n)})):j.value=!1,C(),G("reloadItems",!0)}return te({openModal:T}),(n,g)=>(t(),E(s(re),{appear:"",show:O.value,as:"template",onClose:g[3]||(g[3]=b=>C())},{default:c(()=>[l($e,{size:"sm"},{"modal-title":c(()=>g[4]||(g[4]=[N("Vyřadit položku")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:g[0]||(g[0]=b=>C())},[l(s(Q),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[g[5]||(g[5]=e("div",{class:"p-6 gap-4"},[e("p",null,"Opravdu si přejete položku vyřadit?")],-1)),e("div",Pt,[e("div",Zt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:g[1]||(g[1]=D(b=>C(),["prevent"]))}," Zavřít "),e("button",{onClick:g[2]||(g[2]=D(b=>w(),["prevent"])),class:"rounded-md bg-orange-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-orange-600",type:"submit"}," Vyřadit ")])])]),_:1})]),_:1},8,["show"]))}},Et={class:"border-t p-5"},Gt={class:"text-right space-x-3"},Bt={__name:"deleteItemModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadItems"],setup(le,{expose:te,emit:oe}){const H=le,G=oe,O=d(!1);me("debugModeGlobalVar");const v=d(null),z=d(""),j=d(!1);Se(()=>{});function C(){O.value=!1}function T(){O.value=!0,v.value="",z.value=null}async function w(){Y.check("items.delete")||Y.check("property.master")?(z.value=parseInt(z.value),await K.post("api/items/"+H.selectedItem.id+"/delete").then(n=>{ze.success(n.data.message)}).catch(n=>{console.log(n)})):j.value=!1,C(),G("reloadItems",!0)}return te({openModal:T}),(n,g)=>(t(),E(s(re),{appear:"",show:O.value,as:"template",onClose:g[3]||(g[3]=b=>C())},{default:c(()=>[l($e,{size:"sm"},{"modal-title":c(()=>g[4]||(g[4]=[N("Smazat položku")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:g[0]||(g[0]=b=>C())},[l(s(Q),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[g[5]||(g[5]=e("div",{class:"p-6 gap-4"},[e("p",null,"Opravdu si přejete položku smazat?")],-1)),e("div",Et,[e("div",Gt,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:g[1]||(g[1]=D(b=>C(),["prevent"]))}," Zavřít "),e("button",{onClick:g[2]||(g[2]=D(b=>w(),["prevent"])),class:"rounded-md bg-red-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-red-600",type:"submit"}," Smazat ")])])]),_:1})]),_:1},8,["show"]))}},Ft={class:"p-6 grid grid-cols-2 gap-4"},qt={class:"mt-2"},Kt={class:"mt-2"},Qt={class:"mt-2"},Ht={class:"mt-2"},Jt={class:"rounded-l-full"},Xt={class:"rounded-md border border-gray-300 block font-normal px-2.5 py-1.5 w-full text-sm leading-6 items-center justify-center space-x-2 sm:space-x-4 transition ease-out duration-300"},Wt={class:"flex items-center w-full"},ea={class:"w-6 h-6"},ta={class:"flex-1"},aa={key:0,class:"text-gray-900"},oa={key:1,class:"text-gray-400"},sa={class:"mt-2"},la={class:"relative"},na={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},ia={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},ra={key:0},da={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},ua={key:1,class:"h-20 max-h-20 overflow-hidden"},ma={key:2},ca={class:"col-span-2"},va={class:"mt-2"},pa={key:0,class:"col-span-2"},ga=["for"],fa={class:"mt-2"},ya={class:"border-t p-5"},ba={class:"text-right space-x-3"},xa={__name:"editItemModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadItems"],setup(le,{expose:te,emit:oe}){const H=oe,G=le,O=d({date:"YYYY-MM-DD",month:"MM"}),v=d(!1);me("debugModeGlobalVar");const z=De(),j=d(null),C=d([]),T=d(""),w=d(""),n=d(""),g=d(""),b=d(""),L=d(""),ie=d(!1),S=d(null),M=d(null),I=d(""),ne=d(!1),P=fe(()=>I.value===""?S.value:S.value.filter(k=>k.name.toLowerCase().replace(/\s+/g,"").includes(I.value.toLowerCase().replace(/\s+/g,""))));ye(j,k=>{k&&(T.value=k.name,w.value=k.invoice_number,n.value=k.buyed_at||"",b.value=k.price,g.value=k.purchased_from,L.value=k.description,k.accounting_category&&(console.log(k.accounting_category),M.value=S.value.find(r=>r.id===k.accounting_category.id)),z.custom_columns&&z.custom_columns.length&&(C.value=z.custom_columns.filter(r=>r.enable===1).map(r=>({name:r.name,custom_name:r.custom_name,value:k[r.name]||"",enable:r.enable}))))});function J(){v.value=!1}async function de(k){v.value=!0,M.value="",await u(),await ce(k)}async function ce(k){ie.value=!0;try{const r=await K.get(`api/items/${k.id}`);j.value=r.data.data}catch(r){console.error(r)}finally{ie.value=!1}}async function $(){var k;if(Y.check("items.edit")||Y.check("property.master")){const r={name:T.value,price:b.value,invoice_number:w.value,buyed_at:n.value,description:L.value,purchased_from:g.value,accounting_category_id:((k=M==null?void 0:M.value)==null?void 0:k.id)||null};C.value.forEach(m=>{m.enable&&m.value&&(r[m.name]=m.value)}),console.log(JSON.stringify(C.value));try{await K.post(`api/items/${G.selectedItem.id}/update`,r),ze.success("Položka byla úspěšně aktualizována"),H("reloadItems",!0)}catch(m){console.error(m)}}J()}async function u(){await K.get("/api/accounting-categories?page=1&perpage=99999").then(k=>{S.value=k.data.data}).catch(k=>{console.log(k)})}return te({openModal:de}),(k,r)=>{const m=Ie("VueSpinner");return t(),E(s(re),{appear:"",show:v.value,as:"template",onClose:r[13]||(r[13]=V=>J())},{default:c(()=>[l($e,{size:"md"},{"modal-title":c(()=>r[14]||(r[14]=[N("Úprava položky")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:r[0]||(r[0]=V=>J())},[l(s(Q),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>[l(s(Ne),{onSubmit:r[12]||(r[12]=V=>$())},{default:c(({values:V})=>[e("div",Ft,[e("div",null,[r[15]||(r[15]=e("label",{for:"item-name",class:"block text-sm font-normal leading-6 text-gray-900"},"Název položky:",-1)),e("div",qt,[l(s(F),{rules:"required",modelValue:T.value,"onUpdate:modelValue":r[1]||(r[1]=p=>T.value=p),type:"text",name:"item-name",id:"item-name",autocomplete:"given-name",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte název skupiny..."},null,8,["modelValue"]),l(s(X),{name:"item-name",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[r[16]||(r[16]=e("label",{for:"item-price",class:"block text-sm font-normal leading-6 text-gray-900"},"Cena za kus:",-1)),e("div",Kt,[l(s(F),{rules:"minMax:1,9999999999",modelValue:b.value,"onUpdate:modelValue":r[2]||(r[2]=p=>b.value=p),type:"number",name:"item-price",id:"item-price",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte cenu za kus..."},null,8,["modelValue"]),l(s(X),{name:"item-price",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[r[17]||(r[17]=e("label",{for:"item-invoice",class:"block text-sm font-normal leading-6 text-gray-900"},"Číslo faktury:",-1)),e("div",Qt,[l(s(F),{modelValue:w.value,"onUpdate:modelValue":r[3]||(r[3]=p=>w.value=p),type:"text",name:"item-invoice",id:"item-invoice",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte číslo faktury..."},null,8,["modelValue"]),l(s(X),{name:"item-invoice",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[r[19]||(r[19]=e("label",{for:"item-buyed-at",class:"block text-sm font-normal leading-6 text-gray-900"},"Datum zakoupení:",-1)),e("div",Ht,[l(s(F),{name:"blockedTimetableDate"},{default:c(({handleChange:p,value:R})=>[l(s(Le),{name:"blockedTimetableDate",i18n:"cs","as-single":"",shortcuts:!1,modelValue:n.value,"onUpdate:modelValue":[r[4]||(r[4]=q=>n.value=q),p],formatter:O.value,placeholder:"Zvolte datum zakoupení..."},{default:c(({clear:q})=>[e("div",null,[e("div",Jt,[e("button",Xt,[e("div",Wt,[e("div",ea,[n.value?(t(),E(s(Q),{key:0,onClick:q,class:"h-6 w-6 text-gray-900","aria-hidden":"true"},null,8,["onClick"])):(t(),E(s(Oe),{key:1,class:"h-6 w-6 text-gray-900","aria-hidden":"true"}))]),e("div",ta,[n.value?(t(),o("span",aa,[e("span",null,x(s(we)(n.value).format("DD.MM.YYYY")),1)])):(t(),o("span",oa,r[18]||(r[18]=[e("span",null,"Zvolte datum zakoupení...",-1)])))])])])])])]),_:2},1032,["modelValue","onUpdate:modelValue","formatter"])]),_:1})])]),e("div",null,[r[20]||(r[20]=e("label",{for:"item-buyed-place",class:"block text-sm font-normal leading-6 text-gray-900"},"Pořízeno z:",-1)),e("div",sa,[l(s(F),{modelValue:g.value,"onUpdate:modelValue":r[5]||(r[5]=p=>g.value=p),type:"text",name:"item-buyed-place",id:"item-buyed-place",class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte pořízeno z..."},null,8,["modelValue"]),l(s(X),{name:"item-buyed-place",class:"text-rose-400 text-sm block pt-1"})])]),e("div",null,[r[21]||(r[21]=e("label",{for:"item-account-category",class:"block text-sm font-normal leading-6 text-gray-900 mb-2"},"Účetní druh majetku:",-1)),l(s(be),{modelValue:M.value,"onUpdate:modelValue":r[9]||(r[9]=p=>M.value=p)},{default:c(()=>[e("div",la,[e("div",na,[l(s(xe),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte druh majetku...",displayValue:p=>p?`${p.name}`.trim():"",onChange:r[6]||(r[6]=p=>I.value=p.target.value)},null,8,["displayValue"]),M.value&&M.value.name?(t(),o("div",ia,[e("button",{onClick:r[7]||(r[7]=D(p=>M.value=null,["prevent"])),type:"button"},[l(s(Q),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0),l(s(ke),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:c(()=>[l(s(Te),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(s(re),{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:r[8]||(r[8]=p=>I.value="")},{default:c(()=>[l(s(he),{class:"absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:c(()=>[ne.value?y("",!0):(t(),o("div",ra,[I.value.length===0&&I.value!==""?(t(),o("div",da," Žádný druh majetku nenalezen. ")):y("",!0)])),ne.value?(t(),o("div",ua,[l(m,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(t(),o("div",ma,[(t(!0),o(_,null,ee(P.value,p=>(t(),E(s(_e),{as:"template",key:p.id,value:p},{default:c(({active:R})=>{var q,ve,pe;return[e("li",{class:A(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":R,"text-gray-900":!R}])},[e("span",{class:A(["block truncate",{"font-medium":((q=M.value)==null?void 0:q.id)==p.id,"font-normal":((ve=M.value)==null?void 0:ve.id)!=p.id}])},x(p==null?void 0:p.name),3),((pe=M.value)==null?void 0:pe.id)==p.id?(t(),o("span",{key:0,class:A(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":R,"text-main-color-600":!R}])},[l(s(ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):y("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])]),e("div",ca,[r[22]||(r[22]=e("label",{for:"item-description",class:"block text-sm font-normal leading-6 text-gray-900"},"Poznámka:",-1)),e("div",va,[l(s(F),{modelValue:L.value,"onUpdate:modelValue":r[10]||(r[10]=p=>L.value=p),as:"textarea",name:"item-description",id:"item-description",cols:"30",rows:"3",class:"resize-none block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte poznámku..."},null,8,["modelValue"]),l(s(X),{name:"item-description",class:"text-rose-400 text-sm block pt-1"})])]),C.value.length?(t(!0),o(_,{key:0},ee(C.value,(p,R)=>(t(),o("div",{key:p.id},[p.enable?(t(),o("div",pa,[e("label",{for:"custom_column"+R,class:"block text-sm font-normal leading-6 text-gray-900"},x(p.custom_name||p.name)+": ",9,ga),e("div",fa,[l(s(F),{modelValue:C.value[R].value,"onUpdate:modelValue":q=>C.value[R].value=q,type:"text",name:"custom_column"+R,id:"custom_column"+R,class:"block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-main-color-600 sm:text-sm sm:leading-6 outline-none",placeholder:"Zadejte "+(p.custom_name?p.custom_name:p.name)+"..."},null,8,["modelValue","onUpdate:modelValue","name","id","placeholder"]),l(s(X),{name:"custom_column"+R,class:"text-rose-400 text-sm block pt-1"},null,8,["name"])])])):y("",!0)]))),128)):y("",!0)]),e("div",ya,[e("div",ba,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:r[11]||(r[11]=D(p=>J(),["prevent"]))}," Zavřít "),r[23]||(r[23]=e("button",{class:"rounded-md bg-green-500 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-green-600",type:"submit"}," Upravit ",-1))])])]),_:1})]),_:1})]),_:1},8,["show"])}}},ka={key:0},ha={class:"p-6"},_a={class:"grid grid-cols-3 gap-x-5 gap-y-4"},wa={key:0},$a={key:1,class:"text-red-600"},Ca={key:0,class:"text-sm"},Ia={key:1,class:"text-red-600"},za={key:0,class:"border border-green-600 px-2 py-1 rounded-md text-xs text-green-600 inline-block"},Va={class:"flex"},Ma={key:1,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Da={key:2,class:"border border-red-600 px-2 py-1 rounded-md text-xs text-red-600 inline-block"},Sa={key:3,class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},Ua={class:"flex"},ja={key:4},Ra={class:"border border-amber-600 px-2 py-1 rounded-md text-xs text-amber-600 inline-block"},Na={class:"flex"},Oa={class:"text-xs mt-1"},Ta={key:0,class:"text-sm"},Ya={key:1},La={key:0,class:"text-sm"},Pa={key:1},Za={key:0,class:"text-sm"},Aa={key:1},Ea={class:"col-span-3"},Ga={key:0},Ba={key:1},Fa={key:0,class:"mt-6 space-y-6"},qa={key:0},Ka={class:"flex items-center gap-2"},Qa={class:"mt-4 flex gap-10"},Ha={key:0},Ja={key:1},Xa={key:0},Wa={key:1},eo={key:1},to={class:"flex items-center gap-2"},ao={class:"mt-4 flex gap-10"},oo={key:0},so={key:1},lo={key:0},no={key:1},io={key:0},ro={key:0},uo={key:1},mo={key:1,class:"pt-6 grid grid-cols-3 gap-4"},co={key:0,class:"col-span-2"},vo={class:"block text-xs text-gray-400"},po={class:"text-sm"},go={class:"border-t p-5"},fo={class:"text-right space-x-3"},yo={__name:"itemDetailModal",props:{selectedItem:{type:Object,required:!0}},emits:["reloadGroups"],setup(le,{expose:te,emit:oe}){const H=De(),G=d(!1);me("debugModeGlobalVar");const O=d(!1),v=d(null),z=d([]);ye(v,w=>{w&&H.custom_columns&&H.custom_columns.length&&(z.value=H.custom_columns.filter(n=>n.enable===1).map(n=>({name:n.name,custom_name:n.custom_name,value:w[n.name]||"",enable:n.enable})))});function j(){G.value=!1}async function C(w){G.value=!0,await T(w)}async function T(w){O.value=!0;try{const n=await K.get(`api/items/${w.id}`);v.value=n.data.data}catch(n){console.error(n)}finally{O.value=!1}}return te({openModal:C}),(w,n)=>(t(),E(s(re),{appear:"",show:G.value,as:"template",onClose:n[2]||(n[2]=g=>j())},{default:c(()=>[l($e,{size:"xs"},{"modal-title":c(()=>n[3]||(n[3]=[N("Detail položky")])),"modal-close-button":c(()=>[e("button",{type:"button",class:"flex justify-center items-center rounded-xl border border-transparent bg-main-color-50 shadow-sm hover:bg-main-color-100",onClick:n[0]||(n[0]=g=>j())},[l(s(Q),{class:"mx-auto h-10 w-10 p-1 text-main-color-600","aria-hidden":"true"})])]),"modal-content":c(()=>{var g;return[v.value?(t(),o("div",ka,[e("div",ha,[e("div",_a,[e("div",null,[n[4]||(n[4]=e("span",{class:"block text-xs text-gray-400"},"Evidenční číslo:",-1)),v.value.evidence_number?(t(),o("span",wa,x(v.value.evidence_number),1)):(t(),o("span",$a,"CHYBÍ"))]),e("div",null,[n[5]||(n[5]=e("span",{class:"block text-xs text-gray-400"},"Název položky:",-1)),v.value.name?(t(),o("span",Ca,x(v.value.name),1)):(t(),o("span",Ia,"CHYBÍ"))]),e("div",null,[n[11]||(n[11]=e("span",{class:"block text-xs text-gray-400 pb-1"},"Stav:",-1)),v.value.state=="NOT_ASSIGNED"?(t(),o("div",za,[e("div",Va,[l(s(ue),{class:"h-4 w-4 text-green-600 mr-1","aria-hidden":"true"}),n[6]||(n[6]=e("span",null,"Nepřiřazeno",-1))])])):v.value.state=="ASSIGNED_TO_USER"?(t(),o("div",Ma,n[7]||(n[7]=[e("div",null,"Přiřazeno uživateli",-1)]))):v.value.state=="ASSIGNED_TO_ROOM"?(t(),o("div",Da,n[8]||(n[8]=[e("div",null,"Přiřazeno místnosti",-1)]))):v.value.state=="LOCKED"?(t(),o("div",Sa,[e("div",Ua,[l(s(Je),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),n[9]||(n[9]=e("span",null,"Uzamčeno",-1))])])):v.value.state=="DISCARDED"?(t(),o("div",ja,[e("div",Ra,[e("div",Na,[l(s(Ye),{class:"h-4 w-4 text-amber-600 mr-1","aria-hidden":"true"}),n[10]||(n[10]=e("span",null,"Vyřazeno",-1))]),e("p",Oa,x(s(we)(v.value.buyed_at).format("DD. MM. YYYY - hh:mm")),1)])])):y("",!0)]),e("div",null,[n[12]||(n[12]=e("span",{class:"block text-xs text-gray-400"},"Cena za ks:",-1)),v.value.evidence_number?(t(),o("span",Ta,x(v.value.price),1)):(t(),o("span",Ya,"-"))]),e("div",null,[n[13]||(n[13]=e("span",{class:"block text-xs text-gray-400"},"Číslo faktury:",-1)),v.value.evidence_number?(t(),o("span",La,x(v.value.invoice_number),1)):(t(),o("span",Pa,"-"))]),e("div",null,[n[14]||(n[14]=e("span",{class:"block text-xs text-gray-400"},"Datum zakoupení:",-1)),v.value.buyed_at?(t(),o("span",Za,x(s(we)(v.value.buyed_at).format("DD. MM. YYYY")),1)):(t(),o("span",Aa,"-"))]),e("div",Ea,[n[15]||(n[15]=e("span",{class:"block text-xs text-gray-400"},"Účetní druh majetku:",-1)),v.value.accounting_category?(t(),o("span",Ga,x((g=v.value.accounting_category)==null?void 0:g.name),1)):(t(),o("span",Ba,"-"))])]),v.value.user||v.value.room?(t(),o("div",Fa,[v.value.room?(t(),o("div",qa,[e("div",Ka,[l(s(Xe),{class:"h-6 w-6","aria-hidden":"true"}),n[16]||(n[16]=e("span",null,"Informace o přiřazené místnosti",-1))]),e("div",Qa,[e("div",null,[n[17]||(n[17]=e("span",{class:"block text-xs text-gray-400"},"Kód místnosti:",-1)),v.value.room.code?(t(),o("span",Ha,x(v.value.room.code),1)):(t(),o("span",Ja,"-"))]),e("div",null,[n[18]||(n[18]=e("span",{class:"block text-xs text-gray-400"},"Název místnosti:",-1)),v.value.room.name?(t(),o("span",Xa,x(v.value.room.name),1)):(t(),o("span",Wa,"-"))])])])):y("",!0),v.value.user?(t(),o("div",eo,[e("div",to,[l(s(We),{class:"h-6 w-6","aria-hidden":"true"}),n[19]||(n[19]=e("span",null,"Informace o přiřazeném uživateli",-1))]),e("div",ao,[e("div",null,[n[20]||(n[20]=e("span",{class:"block text-xs text-gray-400"},"Jméno uživatele:",-1)),v.value.user.full_name?(t(),o("span",oo,x(v.value.user.full_name),1)):(t(),o("span",so,"-"))]),e("div",null,[n[21]||(n[21]=e("span",{class:"block text-xs text-gray-400"},"E-mail uživatele:",-1)),v.value.user.email?(t(),o("span",lo,x(v.value.user.email),1)):(t(),o("span",no,"-"))]),v.value.user.organization_unit?(t(),o("div",io,[n[22]||(n[22]=e("span",{class:"block text-xs text-gray-400"},"Organizace:",-1)),v.value.user.organization_unit.name?(t(),o("span",ro,x(v.value.user.organization_unit.name),1)):(t(),o("span",uo,"-"))])):y("",!0)])])):y("",!0)])):y("",!0),z.value.length?(t(),o("div",mo,[(t(!0),o(_,null,ee(z.value,(b,L)=>(t(),o("div",{key:b.id},[b.enable?(t(),o("div",co,[e("span",vo,x((b==null?void 0:b.custom_name)||b.name)+":",1),e("p",po,x(z.value[L].value||"-"),1)])):y("",!0)]))),128))])):y("",!0)]),e("div",go,[e("div",fo,[e("button",{class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200",onClick:n[1]||(n[1]=D(b=>j(),["prevent"]))}," Zavřít ")])])])):y("",!0)]}),_:1})]),_:1},8,["show"]))}},bo={class:"space-y-6"},xo={class:"px-0"},ko={class:"bg-white border border-zinc-200/70 rounded-md p-5"},ho={class:"sm:flex justify-between items-center gap-x-20 gap-y-4"},_o={class:"grid grid-cols-6 grow gap-4"},wo={class:"col-span-6"},$o={class:"col-span-2"},Co={class:"w-full text-left"},Io={key:0,class:"text-gray-900"},zo={key:1,class:"text-gray-400"},Vo={key:0,class:"flex ml-4"},Mo=["onClick"],Do={class:"col-span-2"},So={class:"relative"},Uo={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},jo={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},Ro={key:0},No={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Oo={key:1,class:"h-20 max-h-20 overflow-hidden"},To={key:2},Yo={class:"col-span-2"},Lo={class:"relative"},Po={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},Zo={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},Ao={key:0},Eo={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Go={key:1,class:"h-20 max-h-20 overflow-hidden"},Bo={key:2},Fo={class:"col-span-3"},qo={class:"relative"},Ko={class:"relative w-full cursor-default overflow-hidden rounded-md bg-white border border-gray-300 text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 text-sm"},Qo={key:0,class:"absolute top-1.5 right-8 w-6 h-6"},Ho={key:0},Jo={key:0,class:"relative cursor-default select-none px-4 py-2 text-gray-700"},Xo={key:1,class:"h-20 max-h-20 overflow-hidden"},Wo={key:2},es={class:"flex items-center gap-4"},ts={class:"flow-root bg-white border border-zinc-200/70 rounded-md overflow-hidden"},as={class:"sm:-mx-6 lg:-mx-8"},os={class:"inline-block overflow-x-auto w-full align-middle sm:px-6 lg:px-8"},ss={key:0,class:"min-w-full divide-y divide-gray-200"},ls={key:0,class:"pl-2"},ns=["checked"],is={key:9,class:"pr-2"},rs={key:0,class:"divide-y divide-gray-200"},ds={key:0,class:"flex items-center gap-3 pl-2"},us=["value"],ms=["onClick"],cs={key:0},vs={key:0,class:"text-green-600"},ps={key:1,class:"text-red-600"},gs={key:2,class:"text-red-600"},fs={key:3,class:"text-amber-600"},ys={key:4,class:"text-amber-600"},bs={key:9,class:"flex items-center justify-end pr-2"},xs=["onClick"],ks=["onClick"],hs=["onClick"],_s=["onClick"],ws={key:0},$s={key:1},Cs={key:2,class:"bg-gray-100/70"},Is={colspan:"10",class:"py-4 pl-5 px-3 text-sm text-gray-900 bg-gray-100/70"},zs={class:"flex items-center gap-2"},Ks={__name:"Property",setup(le){const te=d(),oe=d(),H=d(),G=d(),O=d(),v=d(),z=Be();me("debugModeGlobalVar");const j=d(["property"]),T=d(Object.entries({barcodes:{active:!0,position:1,locked:!0,custom_name:"Čárový kód"},evidence_number:{active:!0,position:2,locked:!0,custom_name:"Ev. číslo"},invoice_number:{active:!0,position:3,locked:!1,custom_name:"Číslo faktury"},name:{active:!0,position:4,locked:!1,custom_name:"Název položky"},price:{active:!0,position:5,locked:!1,custom_name:"Cena za kus"},buyed_at:{active:!0,position:6,locked:!1,custom_name:"Datum zakoupení"},room:{active:!0,position:7,locked:!1,custom_name:"Přiřazená třída"},user:{active:!0,position:8,locked:!1,custom_name:"Přiřazený uživatel"},state:{active:!0,position:9,locked:!0,custom_name:"Stav"},actions:{active:!0,position:10,locked:!0,custom_name:"Akce"}}).map(([f,i])=>({name:f,...i}))),w=d(!1),n=d(!1),g=d(""),b=d(1),L=d({}),ie=d([{id:"NOT_ASSIGNED",name:"Nepřiřazeno"},{id:"ASSIGNED_TO_ROOM",name:"Přiřazeno místnosti"},{id:"ASSIGNED_TO_USER",name:"Přiřazeno uživateli"},{id:"LOCKED",name:"Uzamčeno"},{id:"DISCARDED",name:"Vyřazeno"}]),S=d({id:"",name:""}),M=d({}),I=d([]);d(),Se(async()=>{n.value=!0,await pe(),await Ue(),await se(),await Pe(),await Ae(),n.value=!1});const ne=d(null),P=d(null),J=d(""),de=d(!1),ce=fe(()=>J.value===""?ne.value:ne.value.filter(f=>f.name.toLowerCase().replace(/\s+/g,"").includes(J.value.toLowerCase().replace(/\s+/g,"")))),$=d(null),u=d(null),k=d([]),r=d([]),m=d(""),V=d(""),p=d(!1),R=d(!1),q=fe(()=>m.value===""?k.value:k.value.filter(f=>`${f.first_name} ${f.last_name}`.toLowerCase().replace(/\s+/g,"").includes(m.value.toLowerCase().replace(/\s+/g,"")))),ve=fe(()=>V.value===""?r.value:r.value.filter(f=>f.name.toLowerCase().replace(/\s+/g,"").includes(V.value.toLowerCase().replace(/\s+/g,"")))),pe=Re(async()=>{try{const f=await K.get("/api/users?page=1&perpage=50&search="+m.value+"&with_deleted=1");k.value=f.data.data}catch(f){console.error(f)}finally{p.value=!1}},300),Ue=Re(async()=>{try{const f=await K.get("/api/rooms?page=1&perpage=50&search="+V.value);r.value=f.data.data}catch(f){console.error(f)}finally{R.value=!1}},300);ye(m,()=>{p.value=!0,pe()}),ye(V,()=>{R.value=!0,Ue()});async function Pe(){await K.get("/api/accounting-categories?page=1&perpage=99999").then(f=>{ne.value=f.data.data}).catch(f=>{console.log(f)})}ye(()=>z.perPage,(f,i)=>{w.value=!0,b.value=1,se()});async function se(){var f,i,Z,W;w.value=!0,Y.check("items.read")?await K.get("api/items?page="+b.value+"&perpage="+z.perPage+"&search="+g.value+"&state="+(((f=S.value)==null?void 0:f.id)||"")+"&user_id="+(((i=$.value)==null?void 0:i.id)||"")+"&room_id="+(((Z=$.value)==null?void 0:Z.id)||"")+"&accounting_category_id="+(((W=P.value)==null?void 0:W.id)||"")).then(a=>{M.value=a.data.data,L.value=a.data.meta,w.value=!1}).catch(a=>{console.log(a)}):w.value=!1}function Ce(f){v.value=f}function Ze(){I.value.length==M.value.length?I.value=[]:I.value=M.value.map(f=>f.id)}function Ve(f,i){console.log(I.value.length),K.post("api/items/barcodes",{items:I.value},{responseType:"blob"}).then(Z=>{const W=new Blob([Z.data],{type:"application/pdf"}),a=window.URL.createObjectURL(W);let h=i&&I.value.length===1?`${i}.pdf`:"Štítky.pdf";if(f==="download"){const U=document.createElement("a");U.href=a,U.setAttribute("download",h),document.body.appendChild(U),U.click(),document.body.removeChild(U)}else if(f==="open"){const U=window.open();U.document.write(`
                <html>
                    <head>
                        <title>Tisk štítků</title>
                    </head>
                    <body style="margin:0;">
                        <iframe src="${a}" style="width:100vw; height:100vh; border:none;"></iframe>
                    </body>
                </html>
            `),U.document.close()}setTimeout(()=>window.URL.revokeObjectURL(a),1e3),I.value=[]}).catch(Z=>{console.error("Chyba při získávání souboru:",Z)})}async function Ae(){await K.get("/api/settings/custom-table-settings/property-list").then(f=>{console.log(f.data.data);let i=JSON.parse(f.data.data);i=Object.entries(i).map(([Z,W])=>({name:Z,...W})).filter(Z=>Z.active).sort((Z,W)=>Z.position-W.position),T.value=i}).catch(f=>{console.log(f)})}function Ee(f){b.value=f,se()}function Ge(){w.value=!0,b.value=1,g.value="",S.value={id:"",name:""},$.value="",u.value="",I.value=[],P.value="",se()}function ge(){w.value=!0,b.value=1,I.value=[],se()}return(f,i)=>{const Z=Ie("VueSpinner"),W=Ie("TransitionRoot");return t(),o(_,null,[l(He,{breadCrumbs:j.value},{topbarButtons:c(()=>[s(Y).check("items.create")||s(Y).check("property.master")?(t(),o("button",{key:0,onClick:i[0]||(i[0]=D(a=>f.$refs.createItemRef.openModal(),["prevent"])),class:"rounded-md bg-green-500 px-4 py-2.5 text-sm mt-0 text-white shadow-sm hover:bg-green-600"},"Nová položka ")):y("",!0)]),_:1},8,["breadCrumbs"]),e("div",bo,[e("div",xo,[e("div",ko,[e("div",ho,[e("div",_o,[e("div",wo,[je(e("input",{type:"text",name:"search",id:"search","onUpdate:modelValue":i[1]||(i[1]=a=>g.value=a),onKeyup:i[2]||(i[2]=qe(a=>ge(),["enter"])),class:"block w-full rounded-md py-1.5 text-gray-900 border border-gray-300 placeholder:text-gray-400 focus:ring-main-color-600 sm:text-sm sm:leading-6",placeholder:"Zadejte hledaný výraz..."},null,544),[[Fe,g.value]])]),e("div",$o,[l(s(rt),{as:"div",class:"relative inline-block text-left w-full"},{default:c(()=>[e("div",null,[l(s(lt),{class:"inline-flex w-full justify-center items-center rounded-md bg-white px-4 py-2 text-sm font-medium text-white border border-gray-300"},{default:c(()=>[e("div",Co,[S.value&&S.value.name?(t(),o("span",Io,x(S.value.name),1)):(t(),o("span",zo,"Stav položky..."))]),l(s(ot),{class:"ml-2 -mr-1 h-5 w-5 text-violet-200 hover:text-violet-100","aria-hidden":"true"}),S.value&&S.value.name?(t(),o("div",Vo,[e("button",{onClick:i[3]||(i[3]=D(a=>(S.value={id:"",name:""},ge()),["prevent"]))},[l(s(Q),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0)]),_:1})]),l(Ke,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:c(()=>[l(s(nt),{class:"absolute z-10 right-0 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"},{default:c(()=>[(t(!0),o(_,null,ee(ie.value,a=>(t(),o("div",{key:a.id,class:"px-1 py-1"},[l(s(it),null,{default:c(({active:h})=>[e("button",{onClick:U=>S.value=a,class:A([h?"bg-main-color-600 text-white":"text-gray-900","group flex w-full rounded-md px-2 py-2 text-sm text-left"])},x(a.name),11,Mo)]),_:2},1024)]))),128))]),_:1})]),_:1})]),_:1})]),e("div",Do,[l(s(be),{modelValue:$.value,"onUpdate:modelValue":i[7]||(i[7]=a=>$.value=a)},{default:c(()=>[e("div",So,[e("div",Uo,[l(s(xe),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte uživatele...",displayValue:a=>a?`${a.first_name||""} ${a.middle_name?a.middle_name+" ":""}${a.last_name||""}`.trim():"",onChange:i[4]||(i[4]=a=>m.value=a.target.value)},null,8,["displayValue"]),$.value&&$.value.first_name?(t(),o("div",jo,[e("button",{onClick:i[5]||(i[5]=D(a=>($.value=null,ge()),["prevent"]))},[l(s(Q),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0),l(s(ke),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:c(()=>[l(s(Me),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(W,{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:i[6]||(i[6]=a=>m.value="")},{default:c(()=>[l(s(he),{class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:c(()=>[p.value?y("",!0):(t(),o("div",Ro,[q.value.length===0&&m.value!==""?(t(),o("div",No," Žádný uživatel nenalezen. ")):y("",!0)])),p.value?(t(),o("div",Oo,[l(Z,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(t(),o("div",To,[(t(!0),o(_,null,ee(q.value,a=>(t(),E(s(_e),{as:"template",key:a.id,value:a},{default:c(({active:h})=>{var U,ae,B;return[e("li",{class:A(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":h,"text-gray-900":!h}])},[e("span",{class:A(["block truncate",{"font-medium":((U=$.value)==null?void 0:U.id)==a.id,"font-normal":((ae=$.value)==null?void 0:ae.id)!=a.id}])},x((a==null?void 0:a.first_name)+" "+(a!=null&&a.middle_name?(a==null?void 0:a.middle_name)+" ":"")+(a==null?void 0:a.last_name)),3),((B=$.value)==null?void 0:B.id)==a.id?(t(),o("span",{key:0,class:A(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":h,"text-main-color-600":!h}])},[l(s(ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):y("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])]),e("div",Yo,[l(s(be),{modelValue:u.value,"onUpdate:modelValue":i[11]||(i[11]=a=>u.value=a)},{default:c(()=>[e("div",Lo,[e("div",Po,[l(s(xe),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte místnost...",displayValue:a=>a?`${a.name}`.trim():"",onChange:i[8]||(i[8]=a=>V.value=a.target.value)},null,8,["displayValue"]),u.value&&u.value.name?(t(),o("div",Zo,[e("button",{onClick:i[9]||(i[9]=D(a=>(u.value=null,ge()),["prevent"]))},[l(s(Q),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0),l(s(ke),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:c(()=>[l(s(Me),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(W,{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:i[10]||(i[10]=a=>V.value="")},{default:c(()=>[l(s(he),{class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:c(()=>[R.value?y("",!0):(t(),o("div",Ao,[ve.value.length===0&&V.value!==""?(t(),o("div",Eo," Žádná místnost nenalezena. ")):y("",!0)])),R.value?(t(),o("div",Go,[l(Z,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(t(),o("div",Bo,[(t(!0),o(_,null,ee(ve.value,a=>(t(),E(s(_e),{as:"template",key:a.id,value:a},{default:c(({active:h})=>{var U,ae,B;return[e("li",{class:A(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":h,"text-gray-900":!h}])},[e("span",{class:A(["block truncate",{"font-medium":((U=u.value)==null?void 0:U.id)==a.id,"font-normal":((ae=u.value)==null?void 0:ae.id)!=a.id}])},x(a==null?void 0:a.name),3),((B=u.value)==null?void 0:B.id)==a.id?(t(),o("span",{key:0,class:A(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":h,"text-main-color-600":!h}])},[l(s(ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):y("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])]),e("div",Fo,[l(s(be),{modelValue:P.value,"onUpdate:modelValue":i[15]||(i[15]=a=>P.value=a)},{default:c(()=>[e("div",qo,[e("div",Ko,[l(s(xe),{class:"w-full border-none py-2 pl-4 pr-10 text-sm leading-5 text-gray-900 placeholder:text-gray-400 focus:ring-0 font-medium",placeholder:"Zvolte druh majetku...",displayValue:a=>a?`${a.name}`.trim():"",onChange:i[12]||(i[12]=a=>J.value=a.target.value)},null,8,["displayValue"]),P.value&&P.value.name?(t(),o("div",Qo,[e("button",{onClick:i[13]||(i[13]=D(a=>P.value=null,["prevent"])),type:"button"},[l(s(Q),{class:"h-6 w-6 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-0.5 rounded-lg cursor-pointer","aria-hidden":"true"})])])):y("",!0),l(s(ke),{class:"absolute inset-y-0 right-0 flex items-center pr-2"},{default:c(()=>[l(s(Me),{class:"h-5 w-5 text-gray-400","aria-hidden":"true"})]),_:1})]),l(W,{leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",onAfterLeave:i[14]||(i[14]=a=>J.value="")},{default:c(()=>[l(s(he),{class:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"},{default:c(()=>[de.value?y("",!0):(t(),o("div",Ho,[J.value.length===0&&J.value!==""?(t(),o("div",Jo," Žádný druh majetku nenalezen. ")):y("",!0)])),de.value?(t(),o("div",Xo,[l(Z,{class:"mx-auto mt-5 w-full text-main-color-600",size:"40"})])):(t(),o("div",Wo,[(t(!0),o(_,null,ee(ce.value,a=>(t(),E(s(_e),{as:"template",key:a.id,value:a},{default:c(({active:h})=>{var U,ae,B;return[e("li",{class:A(["relative cursor-default select-none py-2 pl-4 pr-10",{"bg-main-color-600 text-white":h,"text-gray-900":!h}])},[e("span",{class:A(["block truncate",{"font-medium":((U=P.value)==null?void 0:U.id)==a.id,"font-normal":((ae=P.value)==null?void 0:ae.id)!=a.id}])},x(a==null?void 0:a.name),3),((B=P.value)==null?void 0:B.id)==a.id?(t(),o("span",{key:0,class:A(["absolute inset-y-0 right-0 flex items-center pr-3",{"text-white":h,"text-main-color-600":!h}])},[l(s(ue),{class:"h-5 w-5","aria-hidden":"true"})],2)):y("",!0)],2)]}),_:2},1032,["value"]))),128))]))]),_:1})]),_:1})])]),_:1},8,["modelValue"])])]),e("div",es,[e("button",{onClick:i[16]||(i[16]=a=>Ge()),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},i[28]||(i[28]=[e("span",null,"Resetovat",-1)])),e("button",{onClick:i[17]||(i[17]=a=>ge()),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700"}," Filtrovat ")])])])]),e("div",null,[e("div",ts,[e("div",as,[e("div",os,[w.value==!1&&n.value==!1?(t(),o("table",ss,[e("thead",null,[e("tr",null,[(t(!0),o(_,null,ee(T.value,a=>(t(),o("th",{key:a,class:"py-4 px-3 text-left text-sm font-semibold text-gray-900 bg-gray-100/70 rounded-tl-md"},[a.name==="barcodes"?(t(),o("div",ls,[e("input",{checked:I.value.length==M.value.length,onClick:Ze,"aria-describedby":"comments-description",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-indigo-600 ring-transparent focus:ring-transparent cursor-pointer"},null,8,ns)])):a.name==="evidence_number"?(t(),o(_,{key:1},[N(" Ev. číslo ")],64)):a.name==="invoice_number"?(t(),o(_,{key:2},[N("Číslo faktury")],64)):a.name==="name"?(t(),o(_,{key:3},[N("Název položky")],64)):a.name==="price"?(t(),o(_,{key:4},[N("Cena za kus")],64)):a.name==="buyed_at"?(t(),o(_,{key:5},[N("Datum zakoupení")],64)):a.name==="room"?(t(),o(_,{key:6},[N("Přiřazená třída")],64)):a.name==="user"?(t(),o(_,{key:7},[N("Přiřazený uživatel")],64)):a.name==="state"?(t(),o(_,{key:8},[N("Stav")],64)):a.name==="actions"?(t(),o("div",is)):(t(),o(_,{key:10},[N(x(a.custom_name||a.name),1)],64))]))),128))])]),M.value&&M.value.length?(t(),o("tbody",rs,[(t(!0),o(_,null,ee(M.value,a=>(t(),o("tr",{key:a.id},[(t(!0),o(_,null,ee(T.value,h=>{var U,ae;return t(),o("td",{key:h,class:"py-4 px-3 text-sm text-gray-600"},[h.name==="barcodes"?(t(),o("div",ds,[je(e("input",{value:a.id,"onUpdate:modelValue":i[18]||(i[18]=B=>I.value=B),id:"comments","aria-describedby":"comments-description",name:"comments",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-transparent cursor-pointer"},null,8,us),[[Qe,I.value]]),e("button",{onClick:D(B=>(I.value=[a.id],Ve("download",a.evidence_number)),["prevent"]),class:"w-8 h-8"},i[29]||(i[29]=[e("img",{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 rounded-lg p-1.5",src:dt,"aria-hidden":"true",alt:"Generovat carovy kod"},null,-1)]),8,ms)])):h.name==="evidence_number"?(t(),o(_,{key:1},[N(x(a.evidence_number||"-"),1)],64)):h.name==="invoice_number"?(t(),o(_,{key:2},[N(x(a.invoice_number||"-"),1)],64)):h.name==="name"?(t(),o(_,{key:3},[N(x(a.name||"-"),1)],64)):h.name==="price"?(t(),o(_,{key:4},[N(x(a.price)+" Kč ",1)],64)):h.name==="buyed_at"?(t(),o(_,{key:5},[a.buyed_at?(t(),o("span",cs,x(s(we)(a.buyed_at).format("DD. MM. YYYY")),1)):y("",!0)],64)):h.name==="room"?(t(),o(_,{key:6},[N(x(((U=a.room)==null?void 0:U.name)||"-"),1)],64)):h.name==="user"?(t(),o(_,{key:7},[N(x(((ae=a.user)==null?void 0:ae.full_name)||"-"),1)],64)):h.name==="state"?(t(),o(_,{key:8},[a.state==="NOT_ASSIGNED"?(t(),o("span",vs,"Nepřiřazeno")):a.state==="ASSIGNED_TO_USER"?(t(),o("span",ps,"Přiřazeno uživateli")):a.state==="ASSIGNED_TO_ROOM"?(t(),o("span",gs,"Přiřazeno místnosti")):a.state==="LOCKED"?(t(),o("span",fs,"Uzamčeno")):a.state==="DISCARDED"?(t(),o("span",ys,"Vyřazeno")):y("",!0)],64)):h.name==="actions"?(t(),o("div",bs,[s(Y).check("items.read")||s(Y).check("property.master")?(t(),o("button",{key:0,onClick:D(B=>(Ce(a),f.$refs.detailItemRef.openModal(a)),["prevent"]),class:"mr-2"},[l(s(et),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,xs)):y("",!0),s(Y).check("items.edit")||s(Y).check("property.master")?(t(),o("button",{key:1,onClick:D(B=>(Ce(a),f.$refs.editItemRef.openModal(a)),["prevent"]),class:"mr-2"},[l(s(tt),{class:"h-8 w-8 text-main-color-600 bg-main-color-200/75 hover:bg-main-color-200 duration-150 p-2 rounded-lg","aria-hidden":"true"})],8,ks)):y("",!0),s(Y).check("items.discard")||s(Y).check("property.master")?(t(),o("button",{key:2,onClick:D(B=>(Ce(a),f.$refs.discardItemRef.openModal()),["prevent"]),class:"mr-2"},[l(s(Ye),{class:"h-8 w-8 text-orange-600 bg-orange-200/75 hover:bg-orange-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,hs)):y("",!0),s(Y).check("items.delete")||s(Y).check("property.master")?(t(),o("button",{key:3,onClick:D(B=>(Ce(a),f.$refs.deleteItemRef.openModal()),["prevent"])},[l(s(Q),{class:"h-8 w-8 text-red-600 bg-red-200/75 hover:bg-red-200 duration-150 p-1.5 rounded-lg","aria-hidden":"true"})],8,_s)):y("",!0)])):(t(),o(_,{key:10},[h.name in a?(t(),o("div",ws,x(a[h.name]),1)):y("",!0)],64))])}),128))]))),128))])):(t(),o("tbody",$s,i[30]||(i[30]=[e("tr",null,[e("td",{colspan:"10",class:"text-center py-10 text-gray-600 text-sm"},"Nebyly nalezeny žádné položky.")],-1)]))),I.value&&I.value.length?(t(),o("tfoot",Cs,[e("tr",null,[e("td",Is,[e("div",zs,[e("button",{onClick:i[19]||(i[19]=D(a=>Ve("open"),["prevent"])),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700 flex gap-1 justify-center items-center"},i[31]||(i[31]=[e("img",{class:"h-5 w-6",src:ut,"aria-hidden":"true",alt:"Generovat carovy kod"},null,-1),e("span",null,"Tisk štítků",-1)])),e("button",{onClick:i[20]||(i[20]=D(a=>Ve("download"),["prevent"])),class:"rounded-md bg-main-color-600 px-4 py-2.5 text-sm text-white shadow-sm hover:bg-main-color-700 flex gap-1 justify-center items-center"},[l(s(at),{class:"h-5 w-5 text-white","aria-hidden":"true"}),i[32]||(i[32]=e("span",null,"Stažení štítků",-1))]),e("button",{onClick:i[21]||(i[21]=D(a=>I.value=[],["prevent"])),class:"rounded-md bg-main-color-200/75 px-4 py-2.5 text-sm text-main-color-600 shadow-sm hover:bg-main-color-200"},i[33]||(i[33]=[e("span",null,"Zrušit výběr",-1)]))])])])])):y("",!0)])):(t(),E(Z,{key:1,class:"mx-auto text-spinner-color",size:"40"}))])])]),L.value!==null?(t(),E(st,{key:0,meta:L.value,onSetPage:Ee,modelValue:b.value,"onUpdate:modelValue":i[22]||(i[22]=a=>b.value=a)},null,8,["meta","modelValue"])):y("",!0)])]),l(Lt,{ref_key:"createItemRef",ref:te,onReloadItems:i[23]||(i[23]=a=>se())},null,512),l(Bt,{ref_key:"deleteItemRef",ref:H,selectedItem:v.value,onReloadItems:i[24]||(i[24]=a=>se())},null,8,["selectedItem"]),l(At,{ref_key:"discardItemRef",ref:oe,selectedItem:v.value,onReloadItems:i[25]||(i[25]=a=>se())},null,8,["selectedItem"]),l(xa,{ref_key:"editItemRef",ref:G,selectedItem:v.value,onReloadItems:i[26]||(i[26]=a=>se())},null,8,["selectedItem"]),l(yo,{ref_key:"detailItemRef",ref:O,selectedItem:v.value,onReloadItems:i[27]||(i[27]=a=>se())},null,8,["selectedItem"])],64)}}};export{Ks as default};

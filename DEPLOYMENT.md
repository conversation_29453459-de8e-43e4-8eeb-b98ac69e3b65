# ADSYS Deployment Guide

Tento dokument popisuje, jak nasadit a aktualizovat ADSYS aplikaci s využitím Docker kontejnerů a persistentních dat.

## Architektura persistentních dat

Aplikace používá Docker volumes pro zachování důležitých dat při aktualizacích:

- **adsys_storage**: Obsahuje <PERSON> storage adresář (`html/backend/storage/`)
- **adsys_env**: Obsahuje .env soubor (`html/backend/.env`)
- **dbdata**: Obsahuje MySQL databázi

## První nasazení

### Předpoklady
- Docker a Docker Compose nainstalované
- Rozbalený projekt ADSYS

### Kroky

1. **Přejděte do adresáře projektu:**
   ```bash
   cd /cesta/k/adsys/projektu
   ```

2. **Spusťte první nasazení:**
   ```bash
   ./scripts/deploy-initial.sh
   ```

3. **Nakonfigurujte .env soubor:**
   - Aplikace vytvoří základní .env soubor
   - Upravte nastavení podle vašeho prostředí
   - Konfigurace se provádí přes webové rozhraní aplikace

4. **Ověřte nasazení:**
   - Otevřete http://localhost v prohlížeči
   - Přihlaste se do aplikace

## Aktualizace aplikace

### Postup aktualizace

1. **Získejte nový ZIP soubor** s aktualizovanou verzí aplikace

2. **Spusťte aktualizaci:**
   ```bash
   ./scripts/deploy-update.sh /cesta/k/novemu/projektu.zip
   ```

3. **Script automaticky:**
   - Vytvoří zálohu současného stavu
   - Zastaví kontejnery
   - Rozbalí nový projekt
   - Zachová persistentní data (.env a storage)
   - Spustí kontejnery s novou verzí
   - Spustí migrace a cache refresh

### Co se zachovává při aktualizaci

- ✅ **Databáze** - kompletně zachována
- ✅ **Storage adresář** - všechny soubory, logy, cache
- ✅ **.env konfigurace** - všechna nastavení aplikace
- ❌ **Kód aplikace** - kompletně nahrazen novou verzí

## Správa dat

### Zálohy

Script automaticky vytváří zálohy před každou aktualizací:
```
backup_YYYYMMDD_HHMMSS/
├── html/
├── docker-compose.yml
├── Dockerfile
└── scripts/
```

### Ruční správa persistentních dat

**Zobrazení volumes:**
```bash
docker volume ls
```

**Záloha .env souboru:**
```bash
docker-compose exec app cat /var/www/html/backend/.env > backup_env_$(date +%Y%m%d).txt
```

**Záloha storage adresáře:**
```bash
docker-compose exec app tar -czf - /var/www/html/backend/storage > backup_storage_$(date +%Y%m%d).tar.gz
```

## Řešení problémů

### Kontejner se nespustí

1. **Zkontrolujte logy:**
   ```bash
   docker-compose logs app
   ```

2. **Restartujte kontejnery:**
   ```bash
   docker-compose down
   docker-compose up -d
   ```

### Problémy s právy

```bash
docker-compose exec app chown -R www-data:www-data /var/www/html/backend/storage
docker-compose exec app chmod -R 775 /var/www/html/backend/storage
```

### Reset persistentních dat

**VAROVÁNÍ: Toto smaže všechna data!**

```bash
docker-compose down
docker volume rm adsys_storage adsys_env
docker-compose up -d
```

### Obnovení ze zálohy

1. **Zastavte kontejnery:**
   ```bash
   docker-compose down
   ```

2. **Obnovte soubory ze zálohy:**
   ```bash
   cp -r backup_YYYYMMDD_HHMMSS/* .
   ```

3. **Spusťte kontejnery:**
   ```bash
   docker-compose up -d
   ```

## Monitoring

### Stav kontejnerů
```bash
docker-compose ps
```

### Logy aplikace
```bash
docker-compose logs -f app
```

### Logy databáze
```bash
docker-compose logs -f db
```

## Bezpečnost

- .env soubor obsahuje citlivé údaje (hesla, klíče)
- Storage adresář může obsahovat nahrané soubory
- Pravidelně zálohujte persistentní data
- Používejte silná hesla v .env konfiguraci

## Poznámky

- Aplikace automaticky detekuje změny v .env souboru
- Storage adresář se automaticky inicializuje při prvním spuštění
- Migrace databáze se spouští automaticky při každé aktualizaci
- Cache se automaticky obnovuje po aktualizaci

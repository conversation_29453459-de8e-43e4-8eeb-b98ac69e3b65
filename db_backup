#!/bin/bash

# Nastavení MySQL přihlaš<PERSON><PERSON><PERSON>ch ú<PERSON>jů
DB_HOST="adsys_db"
DB_USER="adsys_admin"
DB_PASSWORD='adsys_pw '
DB_NAME="adsys_db"

# Nastavení umístění záloh
BACKUP_DIR="/var/www/html/backend/storage/app/backups/"
DATE=$(date +%Y%m%d%H%M%S)

# Vytvoření zálohy
mysqldump -h$DB_HOST -u$DB_USER -p$DB_PASSWORD $DB_NAME | gzip > $BACKUP_DIR/$DB_NAME-$DATE.sql.gz

# Odstranění záloh starších než 30 dnů
find $BACKUP_DIR -type f -name "*.sql.gz" -mtime +30 -exec rm {} \;
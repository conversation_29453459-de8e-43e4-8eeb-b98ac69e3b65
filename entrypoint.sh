#!/bin/sh

# Inicializace persistentních dat
echo "Inicializace persistentních dat..."

# Inicializace .env souboru z persistentního volume
if [ ! -f "/var/www/html/backend/.env_persistent/.env" ]; then
    echo "Vytváření nového .env souboru v persistentním volume..."
    mkdir -p /var/www/html/backend/.env_persistent
    if [ -f "/var/www/html/backend/.env" ]; then
        cp /var/www/html/backend/.env /var/www/html/backend/.env_persistent/.env
    else
        echo "VAROVÁNÍ: .env soubor neexistuje v projektu!"
    fi
else
    echo "Obnovování .env souboru z persistentního volume..."
fi

# Vytvoření symlinku na persistentní .env
rm -f /var/www/html/backend/.env
ln -sf /var/www/html/backend/.env_persistent/.env /var/www/html/backend/.env

# Kontrola a inicializace storage adresáře
if [ ! -d "/var/www/html/backend/storage/app" ]; then
    echo "Inicializace storage adresáře..."
    mkdir -p /var/www/html/backend/storage/app/public
    mkdir -p /var/www/html/backend/storage/framework/cache/data
    mkdir -p /var/www/html/backend/storage/framework/sessions
    mkdir -p /var/www/html/backend/storage/framework/testing
    mkdir -p /var/www/html/backend/storage/framework/views
    mkdir -p /var/www/html/backend/storage/logs
fi

# Nastavení práv pro storage
chown -R www-data:www-data /var/www/html/backend/storage
chmod -R 775 /var/www/html/backend/storage

# Nastavení práv pro skripty
if [ -d "/var/www/scripts" ]; then
    chmod +x /var/www/scripts/*.sh || true
fi

chmod +x /etc/cron.daily/db_backup

# Nastavení vlastníka pro Laravel skripty
chown -R www-data:www-data /var/www/scripts/laravel_queue.sh
chown -R www-data:www-data /var/www/scripts/laravel_scheduler.sh

# Vytvoření log adresáře pro supervisor
mkdir -p /var/log/supervisor

echo "Inicializace dokončena."

# Spuštění supervisord (který spustí všechny služby včetně Apache)
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
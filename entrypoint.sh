#!/bin/sh

# Inicializace persistentních dat při prvním spuštění
echo "Kontrola persistentních dat..."

# Inicializace .env souboru - obousměrná synchronizace
if [ ! -f "/var/www/html/backend/.env_data/.env" ]; then
    echo "Inicializace .env souboru z projektu..."
    # Najdeme .env soubor v projektu (může být .env.example)
    if [ -f "/var/www/html/backend/.env" ]; then
        cp /var/www/html/backend/.env /var/www/html/backend/.env_data/.env
        echo ".env zkopírován z projektu do persistentního volume"
    elif [ -f "/var/www/html/backend/.env.example" ]; then
        cp /var/www/html/backend/.env.example /var/www/html/backend/.env_data/.env
        echo ".env vytvořen z .env.example"
    elif [ -f "/var/www/html/backend/.env.dist" ]; then
        cp /var/www/html/backend/.env.dist /var/www/html/backend/.env_data/.env
        echo ".env vytvořen z .env.dist"
    else
        echo "VAROVÁNÍ: Žádný template .env soubor nenalezen!"
    fi
else
    echo ".env soubor již existuje v persistentním volume"
fi

# Zkopírujeme persistentní .env do projektu (pro VSCode editaci)
if [ -f "/var/www/html/backend/.env_data/.env" ]; then
    cp /var/www/html/backend/.env_data/.env /var/www/html/backend/.env
    echo ".env zkopírován z persistentního volume do projektu"
fi

# Inicializace storage adresáře, pokud je prázdný
if [ ! -d "/var/www/html/backend/storage/app" ]; then
    echo "Inicializace storage adresáře..."
    mkdir -p /var/www/html/backend/storage/app/public
    mkdir -p /var/www/html/backend/storage/framework/cache/data
    mkdir -p /var/www/html/backend/storage/framework/sessions
    mkdir -p /var/www/html/backend/storage/framework/testing
    mkdir -p /var/www/html/backend/storage/framework/views
    mkdir -p /var/www/html/backend/storage/logs
    echo "Storage adresář inicializován"
else
    echo "Storage adresář již existuje v persistentním volume"
fi

# Nastavení práv
chown -R www-data:www-data /var/www/html/backend/storage
chmod -R 775 /var/www/html/backend/storage

# Nastavení práv pro skripty
if [ -d "/var/www/scripts" ]; then
    chmod +x /var/www/scripts/*.sh || true
fi

chmod +x /etc/cron.daily/db_backup

# Nastavení vlastníka pro Laravel skripty
chown -R www-data:www-data /var/www/scripts/laravel_queue.sh
chown -R www-data:www-data /var/www/scripts/laravel_scheduler.sh

# Vytvoření log adresáře pro supervisor
mkdir -p /var/log/supervisor

echo "Inicializace dokončena."

# Spuštění background procesu pro synchronizaci .env změn
(
    while true; do
        sleep 10
        # Pokud se .env v projektu změnil, zkopíruj ho do persistentního volume
        if [ -f "/var/www/html/backend/.env" ] && [ -f "/var/www/html/backend/.env_data/.env" ]; then
            if ! cmp -s "/var/www/html/backend/.env" "/var/www/html/backend/.env_data/.env"; then
                cp /var/www/html/backend/.env /var/www/html/backend/.env_data/.env
                echo "$(date): .env synchronizován do persistentního volume"
            fi
        fi
    done
) &

# Spuštění supervisord (který spustí všechny služby včetně Apache)
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
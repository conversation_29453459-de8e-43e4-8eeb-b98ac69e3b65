#!/bin/bash

# Script pro aktualizaci ADSYS projektu se zachováním persistentních dat
# Použití: ./scripts/deploy-update.sh [cesta_k_novemu_zip_souboru]

set -e  # Ukončit při chybě

# Barvy pro výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Funkce pro logování
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Kontrola parametrů
if [ $# -eq 0 ]; then
    log_error "Chybí parametr s cestou k ZIP souboru"
    echo "Použití: $0 [cesta_k_novemu_zip_souboru]"
    exit 1
fi

ZIP_FILE="$1"

# Kontrola existence ZIP souboru
if [ ! -f "$ZIP_FILE" ]; then
    log_error "ZIP soubor '$ZIP_FILE' neexistuje"
    exit 1
fi

# Kontrola, zda jsme v root adresáři projektu
if [ ! -f "docker-compose.yml" ]; then
    log_error "Tento script musí být spuštěn z root adresáře projektu (kde je docker-compose.yml)"
    exit 1
fi

log_info "Začínám aktualizaci ADSYS projektu..."
log_info "ZIP soubor: $ZIP_FILE"

# Vytvoření záložního adresáře s timestampem
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
log_info "Vytváření zálohy do adresáře: $BACKUP_DIR"

# Záloha současného stavu (bez persistentních dat)
mkdir -p "$BACKUP_DIR"
cp -r html "$BACKUP_DIR/" 2>/dev/null || true
cp docker-compose.yml "$BACKUP_DIR/" 2>/dev/null || true
cp Dockerfile "$BACKUP_DIR/" 2>/dev/null || true
cp entrypoint.sh "$BACKUP_DIR/" 2>/dev/null || true
cp -r scripts "$BACKUP_DIR/" 2>/dev/null || true

log_info "Záloha vytvořena"

# Zastavení kontejnerů
log_info "Zastavování Docker kontejnerů..."
docker-compose down

# Vytvoření dočasného adresáře pro rozbalení
TEMP_DIR=$(mktemp -d)
log_info "Rozbaluji nový projekt do: $TEMP_DIR"

# Rozbalení nového ZIP souboru
cd "$TEMP_DIR"
unzip -q "$ZIP_FILE"

# Najití root adresáře v ZIP (může být vnořený)
PROJECT_ROOT=""
if [ -f "docker-compose.yml" ]; then
    PROJECT_ROOT="."
else
    # Hledání adresáře s docker-compose.yml
    PROJECT_ROOT=$(find . -name "docker-compose.yml" -type f | head -1 | xargs dirname)
fi

if [ -z "$PROJECT_ROOT" ] || [ ! -f "$PROJECT_ROOT/docker-compose.yml" ]; then
    log_error "V ZIP souboru nebyl nalezen docker-compose.yml"
    rm -rf "$TEMP_DIR"
    exit 1
fi

cd "$PROJECT_ROOT"
log_info "Projekt nalezen v: $PROJECT_ROOT"

# Návrat do původního adresáře
cd - > /dev/null

# Odstranění starých souborů (kromě persistentních dat)
log_info "Odstraňování starých souborů..."
rm -rf html
rm -f docker-compose.yml Dockerfile entrypoint.sh apache.conf supervisord.conf init.sql laravel_*.service
rm -rf scripts

# Kopírování nových souborů
log_info "Kopírování nových souborů..."
cp -r "$TEMP_DIR/$PROJECT_ROOT"/* .

# Úklid dočasného adresáře
rm -rf "$TEMP_DIR"

# Spuštění kontejnerů
log_info "Spouštění Docker kontejnerů..."
docker-compose up -d

# Čekání na spuštění
log_info "Čekání na spuštění aplikace..."
sleep 10

# Spuštění update scriptu v kontejneru
log_info "Spouštění update scriptu..."
docker-compose exec app /var/www/scripts/update.sh

log_info "Aktualizace dokončena úspěšně!"
log_info "Záloha je uložena v adresáři: $BACKUP_DIR"
log_warn "Persistentní data (.env a storage) byla zachována z předchozí verze"

# Zobrazení stavu kontejnerů
log_info "Stav kontejnerů:"
docker-compose ps

#!/bin/bash

# Script pro první nasazení ADSYS projektu
# Použití: ./scripts/deploy-initial.sh

set -e  # Ukončit při chybě

# Barvy pro výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Funkce pro logování
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Kontrola, zda jsme v root adresáři projektu
if [ ! -f "docker-compose.yml" ]; then
    log_error "Tento script musí být spuštěn z root adresáře projektu (kde je docker-compose.yml)"
    exit 1
fi

log_info "Začínám první nasazení ADSYS projektu..."

# Kontrola existence .env souboru
if [ ! -f ".env" ]; then
    log_warn ".env soubor neexistuje, vytvářím z .env.example"
    if [ -f ".env.example" ]; then
        cp .env.example .env
    else
        log_error ".env.example soubor neexistuje"
        exit 1
    fi
fi

# Nastavení práv pro skripty
log_info "Nastavování práv pro skripty..."
chmod +x scripts/*.sh

# Spuštění kontejnerů
log_info "Spouštění Docker kontejnerů..."
docker-compose up -d --build

# Čekání na spuštění databáze
log_info "Čekání na spuštění databáze..."
sleep 30

# Spuštění inicializačního scriptu
log_info "Spouštění inicializačního scriptu..."
docker-compose exec app /var/www/scripts/update.sh

log_info "První nasazení dokončeno úspěšně!"
log_info "Aplikace je dostupná na: http://localhost"

# Zobrazení stavu kontejnerů
log_info "Stav kontejnerů:"
docker-compose ps

log_warn "DŮLEŽITÉ: Nezapomeňte nakonfigurovat .env soubor podle vašeho prostředí!"
